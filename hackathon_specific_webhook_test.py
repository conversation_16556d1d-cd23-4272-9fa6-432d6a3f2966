"""
Hackathon Specific Webhook Test Client
=====================================

This script tests the exact hackathon endpoint with the provided specifications.

Target: http://**************:8000/hackrx/run
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hackrx_test.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HackRXTester:
    """Test client specifically for HackRX endpoint."""
    
    def __init__(self):
        self.base_url = "http://**************:8000"
        self.endpoint = "/hackrx/run"
        self.bearer_token = "0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378"
        self.document_url = "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D"
        self.questions = [
            "What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?",
            "What is the waiting period for pre-existing diseases (PED) to be covered?",
            "Does this policy cover maternity expenses, and what are the conditions?",
            "What is the waiting period for cataract surgery?",
            "Are the medical expenses for an organ donor covered under this policy?",
            "What is the No Claim Discount (NCD) offered in this policy?",
            "Is there a benefit for preventive health check-ups?",
            "How does the policy define a 'Hospital'?",
            "What is the extent of coverage for AYUSH treatments?",
            "Are there any sub-limits on room rent and ICU charges for Plan A?"
        ]
        
    async def test_endpoint(self) -> Dict[str, Any]:
        """Test the HackRX endpoint with the exact specifications."""
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.bearer_token}'
        }
        
        payload = {
            "documents": self.document_url,
            "questions": self.questions
        }
        
        url = f"{self.base_url}{self.endpoint}"
        
        logger.info("Starting HackRX endpoint test...")
        logger.info(f"URL: {url}")
        logger.info(f"Questions: {len(self.questions)} questions")
        
        start_time = time.time()
        
        try:
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                logger.info("📡 Sending request...")
                
                async with session.post(url, json=payload, headers=headers) as response:
                    response_time = time.time() - start_time
                    
                    logger.info(f"📊 Response received: {response.status} ({response_time:.2f}s)")
                    
                    # Get response data
                    try:
                        response_data = await response.json()
                    except:
                        response_text = await response.text()
                        response_data = {"raw_response": response_text}
                    
                    # Create result
                    result = {
                        "success": response.status == 200,
                        "status_code": response.status,
                        "response_time": response_time,
                        "response_data": response_data,
                        "timestamp": datetime.now().isoformat(),
                        "url": url,
                        "payload": payload
                    }
                    
                    # Log detailed results
                    if result["success"]:
                        logger.info("✅ Test PASSED!")
                        
                        if isinstance(response_data, dict) and "answers" in response_data:
                            answers = response_data["answers"]
                            if isinstance(answers, list):
                                logger.info(f"📝 Received {len(answers)} answers")
                                
                                # Show first few answers
                                for i, answer in enumerate(answers[:3], 1):
                                    logger.info(f"Answer {i}: {answer[:100]}...")
                                
                                # Validate answer count
                                if len(answers) == len(self.questions):
                                    logger.info("✅ Answer count matches question count")
                                else:
                                    logger.warning(f"⚠️ Answer count mismatch: got {len(answers)}, expected {len(self.questions)}")
                            else:
                                logger.warning("⚠️ 'answers' field is not a list")
                        else:
                            logger.warning("⚠️ Response missing 'answers' field")
                    else:
                        logger.error(f"❌ Test FAILED: {response.status}")
                        logger.error(f"Response: {response_data}")
                    
                    return result
                    
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            logger.error(f"⏰ Request timed out after {response_time:.2f}s")
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "response_data": None,
                "error": "Request timeout",
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "payload": payload
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"💥 Request failed: {str(e)}")
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "response_data": None,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "payload": payload
            }
    
    async def test_with_subset_questions(self) -> Dict[str, Any]:
        """Test with a smaller subset of questions for faster testing."""
        
        logger.info("🔬 Testing with subset of questions...")
        
        # Use first 3 questions for quick test
        subset_questions = self.questions[:3]
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.bearer_token}'
        }
        
        payload = {
            "documents": self.document_url,
            "questions": subset_questions
        }
        
        url = f"{self.base_url}{self.endpoint}"
        start_time = time.time()
        
        try:
            timeout = aiohttp.ClientTimeout(total=120)  # 2 minutes for subset
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    response_time = time.time() - start_time
                    
                    try:
                        response_data = await response.json()
                    except:
                        response_text = await response.text()
                        response_data = {"raw_response": response_text}
                    
                    result = {
                        "test_type": "subset",
                        "success": response.status == 200,
                        "status_code": response.status,
                        "response_time": response_time,
                        "response_data": response_data,
                        "timestamp": datetime.now().isoformat(),
                        "questions_count": len(subset_questions)
                    }
                    
                    logger.info(f"📊 Subset test: {response.status} ({response_time:.2f}s)")
                    return result
                    
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"💥 Subset test failed: {str(e)}")
            return {
                "test_type": "subset",
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "questions_count": len(subset_questions)
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check if the server is accessible."""
        
        logger.info("🏥 Performing health check...")
        
        try:
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Try to access base URL
                async with session.get(self.base_url) as response:
                    logger.info(f"📡 Server accessible: {response.status}")
                    return {
                        "server_accessible": True,
                        "status_code": response.status,
                        "response_time": 0
                    }
                    
        except Exception as e:
            logger.error(f"💥 Server not accessible: {str(e)}")
            return {
                "server_accessible": False,
                "error": str(e)
            }
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate test report."""
        
        report_lines = [
            "=" * 80,
            "HACKRX ENDPOINT TEST REPORT",
            "=" * 80,
            f"Test Time: {datetime.now().isoformat()}",
            f"Target URL: {self.base_url}{self.endpoint}",
            f"Document URL: {self.document_url[:50]}...",
            f"Total Questions: {len(self.questions)}",
            "",
            "HEALTH CHECK:",
            "-" * 40
        ]
        
        if "health_check" in results:
            health = results["health_check"]
            if health.get("server_accessible"):
                report_lines.append("✅ Server is accessible")
            else:
                report_lines.append(f"❌ Server not accessible: {health.get('error', 'Unknown error')}")
        
        report_lines.append("")
        
        # Main test results
        if "main_test" in results:
            main = results["main_test"]
            report_lines.extend([
                "MAIN TEST (All 10 Questions):",
                "-" * 40,
                f"Status: {'✅ PASSED' if main['success'] else '❌ FAILED'}",
                f"Status Code: {main['status_code']}",
                f"Response Time: {main['response_time']:.2f}s",
                f"Timestamp: {main['timestamp']}"
            ])
            
            if main['success'] and isinstance(main.get('response_data'), dict):
                response_data = main['response_data']
                if "answers" in response_data:
                    answers = response_data["answers"]
                    report_lines.extend([
                        f"Answers Received: {len(answers) if isinstance(answers, list) else 'Invalid format'}",
                        "",
                        "SAMPLE ANSWERS:",
                        "-" * 20
                    ])
                    
                    if isinstance(answers, list):
                        for i, (question, answer) in enumerate(zip(self.questions[:3], answers[:3]), 1):
                            report_lines.extend([
                                f"Q{i}: {question}",
                                f"A{i}: {answer[:200]}...",
                                ""
                            ])
            
            if not main['success']:
                report_lines.extend([
                    f"Error: {main.get('error', 'Unknown error')}",
                    ""
                ])
        
        # Subset test results
        if "subset_test" in results:
            subset = results["subset_test"]
            report_lines.extend([
                "SUBSET TEST (3 Questions):",
                "-" * 40,
                f"Status: {'✅ PASSED' if subset['success'] else '❌ FAILED'}",
                f"Status Code: {subset['status_code']}",
                f"Response Time: {subset['response_time']:.2f}s",
                ""
            ])
        
        # Recommendations
        report_lines.extend([
            "RECOMMENDATIONS:",
            "-" * 40
        ])
        
        if results.get("main_test", {}).get("success"):
            report_lines.append("✅ Endpoint is working correctly!")
            if results["main_test"]["response_time"] > 60:
                report_lines.append("⚠️ Response time is high (>60s). Consider optimization.")
        else:
            report_lines.append("❌ Endpoint has issues that need to be resolved.")
            
        if not results.get("health_check", {}).get("server_accessible"):
            report_lines.append("❌ Server accessibility issues detected.")
        
        report_lines.extend([
            "",
            "=" * 80,
            "END OF REPORT",
            "=" * 80
        ])
        
        return "\n".join(report_lines)

async def main():
    """Main function to run the HackRX test."""
    
    print("🧪 HackRX Endpoint Tester")
    print("=" * 50)
    print("Target: http://**************:8000/hackrx/run")
    print("Questions: 10 policy-related questions")
    print("=" * 50)
    
    tester = HackRXTester()
    results = {}
    
    try:
        # 1. Health check
        logger.info("Step 1: Health check...")
        results["health_check"] = await tester.health_check()
        
        # 2. Quick subset test first
        logger.info("Step 2: Quick subset test...")
        results["subset_test"] = await tester.test_with_subset_questions()
        
        # 3. Full test if subset works
        if results["subset_test"]["success"]:
            logger.info("Step 3: Full test with all questions...")
            results["main_test"] = await tester.test_endpoint()
        else:
            logger.warning("Skipping full test due to subset test failure")
            results["main_test"] = {"success": False, "skipped": True}
        
        # Generate and save report
        report = tester.generate_report(results)
        
        # Save to file
        report_filename = f"hackrx_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_filename, 'w') as f:
            f.write(report)
        
        # Display results
        print("\n" + report)
        print(f"\n📊 Report saved to: {report_filename}")
        
        # Final status
        if results.get("main_test", {}).get("success"):
            print("\n🎉 SUCCESS! Your HackRX endpoint is working!")
        elif results.get("subset_test", {}).get("success"):
            print("\n⚠️ PARTIAL SUCCESS! Subset test passed, but full test had issues.")
        else:
            print("\n❌ FAILURE! Endpoint needs attention.")
            
    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
    except Exception as e:
        logger.error(f"💥 Test failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
