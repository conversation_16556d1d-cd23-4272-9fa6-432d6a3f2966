#!/usr/bin/env python3
"""
HackRX Endpoint Tester - Windows Compatible
==========================================

Tests the specific HackRX endpoint with the exact format required.
Compatible with Windows command prompt (no emojis in logs).
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Configure logging without emojis for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hackrx_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HackRXEndpointTester:
    """Tester for the specific HackRX endpoint."""
    
    def __init__(self):
        # HackRX specific configuration
        self.base_url = "http://140.238.227.29:8000"
        self.endpoint = "/api/v1/hackrx/run"
        self.bearer_token = "0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378"
        
        # Test data
        self.document_url = "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D"
        
        self.all_questions = [
            "What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?",
            "What is the waiting period for pre-existing diseases (PED) to be covered?",
            "Does this policy cover maternity expenses, and what are the conditions?",
            "What is the waiting period for cataract surgery?",
            "Are the medical expenses for an organ donor covered under this policy?",
            "What is the No Claim Discount (NCD) offered in this policy?",
            "Is there a benefit for preventive health check-ups?",
            "How does the policy define a 'Hospital'?",
            "What is the extent of coverage for AYUSH treatments?",
            "Are there any sub-limits on room rent and ICU charges for Plan A?"
        ]
        
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Create aiohttp session."""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.bearer_token}',
            'User-Agent': 'HackRX-Tester/1.0'
        }
        
        timeout = aiohttp.ClientTimeout(total=180)  # 3 minutes timeout
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Close aiohttp session."""
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check if the server is accessible."""
        logger.info("Performing health check...")
        
        try:
            # Test base URL first
            async with self.session.get(self.base_url) as response:
                logger.info(f"Server accessible: {response.status}")
                
                return {
                    "success": True,
                    "status_code": response.status,
                    "response_time": 0,
                    "message": f"Server responding with status {response.status}"
                }
                
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {
                "success": False,
                "status_code": 0,
                "response_time": 0,
                "error": str(e)
            }
    
    async def test_with_subset_questions(self, num_questions: int = 3) -> Dict[str, Any]:
        """Test with a subset of questions first."""
        logger.info(f"Testing with subset of {num_questions} questions...")
        
        subset_questions = self.all_questions[:num_questions]
        
        payload = {
            "documents": self.document_url,
            "questions": subset_questions
        }
        
        return await self.send_request(payload, f"Subset Test ({num_questions} questions)")
    
    async def test_with_all_questions(self) -> Dict[str, Any]:
        """Test with all 10 questions."""
        logger.info("Testing with all 10 questions...")
        
        payload = {
            "documents": self.document_url,
            "questions": self.all_questions
        }
        
        return await self.send_request(payload, "Full Test (10 questions)")
    
    async def send_request(self, payload: Dict[str, Any], test_name: str) -> Dict[str, Any]:
        """Send request to the endpoint."""
        url = f"{self.base_url}{self.endpoint}"
        
        logger.info(f"Sending {test_name} to: {url}")
        logger.info(f"Payload: {json.dumps(payload, indent=2)}")
        
        start_time = time.time()
        
        try:
            async with self.session.post(url, json=payload) as response:
                response_time = time.time() - start_time
                
                logger.info(f"{test_name}: {response.status} ({response_time:.2f}s)")
                
                # Try to get response body
                try:
                    if response.content_type == 'application/json':
                        response_data = await response.json()
                    else:
                        response_text = await response.text()
                        response_data = {"raw_response": response_text}
                except Exception as parse_error:
                    logger.warning(f"Could not parse response: {parse_error}")
                    response_data = {"error": "Could not parse response"}
                
                success = 200 <= response.status < 300
                
                result = {
                    "success": success,
                    "status_code": response.status,
                    "response_time": response_time,
                    "test_name": test_name,
                    "response_data": response_data
                }
                
                if not success:
                    result["error"] = f"HTTP {response.status}"
                    logger.error(f"Request failed: {response.status}")
                    if isinstance(response_data, dict) and "raw_response" in response_data:
                        logger.error(f"Response: {response_data['raw_response'][:500]}")
                
                return result
                
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            logger.error(f"{test_name}: Timeout after {response_time:.2f}s")
            
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "test_name": test_name,
                "error": "Request timeout"
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"{test_name}: Error - {str(e)}")
            
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "test_name": test_name,
                "error": str(e)
            }
    
    async def test_authentication(self) -> Dict[str, Any]:
        """Test authentication by sending a simple request."""
        logger.info("Testing authentication...")
        
        # Simple payload for auth test
        auth_payload = {
            "documents": self.document_url,
            "questions": ["What is this document about?"]
        }
        
        return await self.send_request(auth_payload, "Authentication Test")
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate test report."""
        report_lines = [
            "=" * 60,
            "HACKRX ENDPOINT TEST REPORT",
            "=" * 60,
            f"Test Time: {datetime.now().isoformat()}",
            f"Target: {self.base_url}{self.endpoint}",
            f"Document: {self.document_url[:50]}...",
            f"Questions: {len(self.all_questions)} total",
            "",
            "RESULTS:",
            "-" * 40
        ]
        
        # Health check
        if "health_check" in results:
            health = results["health_check"]
            status = "PASS" if health["success"] else "FAIL"
            report_lines.append(f"Health Check: {status} (HTTP {health.get('status_code', 'N/A')})")
        
        # Authentication test
        if "auth_test" in results:
            auth = results["auth_test"]
            status = "PASS" if auth["success"] else "FAIL"
            report_lines.append(f"Authentication: {status} (HTTP {auth.get('status_code', 'N/A')})")
        
        # Subset test
        if "subset_test" in results:
            subset = results["subset_test"]
            status = "PASS" if subset["success"] else "FAIL"
            report_lines.append(f"Subset Test: {status} (HTTP {subset.get('status_code', 'N/A')}) - {subset.get('response_time', 0):.2f}s")
        
        # Full test
        if "full_test" in results:
            full = results["full_test"]
            status = "PASS" if full["success"] else "FAIL"
            report_lines.append(f"Full Test: {status} (HTTP {full.get('status_code', 'N/A')}) - {full.get('response_time', 0):.2f}s")
        
        report_lines.extend([
            "",
            "DETAILED RESULTS:",
            "-" * 40
        ])
        
        # Add detailed results
        for test_name, result in results.items():
            if isinstance(result, dict):
                report_lines.extend([
                    f"{test_name.upper()}:",
                    f"  Success: {result.get('success', False)}",
                    f"  Status: {result.get('status_code', 'N/A')}",
                    f"  Time: {result.get('response_time', 0):.2f}s"
                ])
                
                if "error" in result:
                    report_lines.append(f"  Error: {result['error']}")
                
                if "response_data" in result and result["response_data"]:
                    response_data = result["response_data"]
                    if isinstance(response_data, dict):
                        if "answers" in response_data:
                            answers = response_data["answers"]
                            if isinstance(answers, list):
                                report_lines.append(f"  Answers: {len(answers)} received")
                                if len(answers) > 0:
                                    report_lines.append(f"  Sample: {answers[0][:100]}...")
                        elif "raw_response" in response_data:
                            raw = response_data["raw_response"]
                            report_lines.append(f"  Raw Response: {raw[:200]}...")
                
                report_lines.append("")
        
        # Summary
        successful_tests = sum(1 for result in results.values() 
                              if isinstance(result, dict) and result.get('success', False))
        total_tests = len([r for r in results.values() if isinstance(r, dict)])
        
        report_lines.extend([
            "SUMMARY:",
            "-" * 40,
            f"Tests Passed: {successful_tests}/{total_tests}",
            f"Success Rate: {(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "No tests",
            "",
            "RECOMMENDATIONS:",
            "-" * 40
        ])
        
        if successful_tests == total_tests:
            report_lines.append("EXCELLENT! All tests passed. Endpoint is working correctly.")
        elif successful_tests > 0:
            report_lines.append("PARTIAL SUCCESS. Some tests passed, review failures.")
        else:
            report_lines.append("ALL TESTS FAILED. Check endpoint configuration and authentication.")
        
        report_lines.extend([
            "",
            "=" * 60,
            "END OF REPORT",
            "=" * 60
        ])
        
        return "\n".join(report_lines)

async def main():
    """Main test function."""
    print("HackRX Endpoint Tester")
    print("=" * 50)
    print(f"Target: http://140.238.227.29:8000/api/v1/hackrx/run")
    print(f"Questions: 10 policy-related questions")
    print("=" * 50)
    
    results = {}
    
    try:
        async with HackRXEndpointTester() as tester:
            # Step 1: Health check
            print("\nStep 1: Health check...")
            results["health_check"] = await tester.health_check()
            
            # Step 2: Authentication test
            print("\nStep 2: Authentication test...")
            results["auth_test"] = await tester.test_authentication()
            
            # Only proceed if authentication works
            if results["auth_test"]["success"]:
                # Step 3: Quick subset test
                print("\nStep 3: Quick subset test...")
                results["subset_test"] = await tester.test_with_subset_questions(3)
                
                # Step 4: Full test if subset works
                if results["subset_test"]["success"]:
                    print("\nStep 4: Full test with all questions...")
                    results["full_test"] = await tester.test_with_all_questions()
                else:
                    print("\nSkipping full test due to subset test failure")
            else:
                print("\nSkipping further tests due to authentication failure")
            
            # Generate report
            report = tester.generate_report(results)
            
            # Save report
            report_filename = f"hackrx_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_filename, 'w') as f:
                f.write(report)
            
            print(f"\nReport saved to: {report_filename}")
            print("\n" + report)
            
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        print(f"\nERROR: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
