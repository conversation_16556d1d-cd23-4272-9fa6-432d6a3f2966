# Core FastAPI and web framework
fastapi
uvicorn[standard]
pydantic
pydantic-settings

# Azure OpenAI and LLM
openai
tiktoken

# Vector search and embeddings
faiss-cpu
numpy

# Document processing
PyPDF2
python-docx
pytesseract
pdf2image
Pillow

# Text processing and chunking
langchain
langchain-text-splitters

# Reranking and semantic chunking
sentence-transformers
torch
transformers
scikit-learn

# HTTP client for document download
aiohttp
aiofiles

# Environment and configuration
python-dotenv

# Logging and monitoring
structlog

# Development and testing
pytest
pytest-asyncio
httpx

# Additional dependencies for Python 3.12 compatibility
setuptools
requests

# For Python 3.12 compatibility (distutils replacement)
setuptools-scm