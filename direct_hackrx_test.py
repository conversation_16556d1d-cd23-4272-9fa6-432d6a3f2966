#!/usr/bin/env python3
"""
Direct test script for HackRX endpoint - no interactive input
"""

import asyncio
import aiohttp
import json
import logging
import sys
import time
import uuid
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('hackrx_direct_test.log', mode='w', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_hackrx_endpoint():
    """Direct test of the HackRX endpoint with correct URL."""
    
    # Configuration
    API_BASE_URL = "http://**************:8000"
    API_TOKEN = "7dJCrCMV0qWZAqTGLm6fUwwQgQ5RhZzP2SXbHLSmArM5tD0vEI1UdmWOAZjzZHtbNs7U3VczA7PlmI0cYcRrGQ"
    
    # Fixed endpoint URL based on routes.py analysis
    url = f"{API_BASE_URL}/api/v1/hackrx/run"
    
    payload = {
        "question": "What are the eligibility criteria for participating in the hackathon?",
        "session_id": f"test_session_{uuid.uuid4().hex[:8]}"
    }
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    logger.info("Starting HackRX endpoint test...")
    logger.info(f"URL: {url}")
    logger.info(f"Question: {payload['question']}")
    
    start_time = time.time()
    
    timeout = aiohttp.ClientTimeout(total=60, connect=10)
    
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                response_time = time.time() - start_time
                response_text = await response.text()
                
                logger.info(f"Status Code: {response.status}")
                logger.info(f"Response Time: {response_time:.2f}s")
                logger.info(f"Response Size: {len(response_text)} characters")
                
                if response.status == 200:
                    try:
                        response_data = json.loads(response_text)
                        logger.info("SUCCESS: Valid JSON response received")
                        logger.info(f"JSON Keys: {list(response_data.keys())}")
                        
                        # Check for expected fields
                        if 'result' in response_data:
                            logger.info(f"Result found: {response_data['result'][:200]}...")
                        if 'status' in response_data:
                            logger.info(f"Status: {response_data['status']}")
                        if 'job_id' in response_data:
                            logger.info(f"Job ID: {response_data['job_id']}")
                        if 'answers' in response_data:
                            answers = response_data['answers']
                            if isinstance(answers, list):
                                logger.info(f"Found {len(answers)} answers")
                                if answers:
                                    logger.info(f"First answer: {answers[0][:100]}...")
                            else:
                                logger.warning("'answers' field is not a list")
                        
                        # Save successful response
                        with open('successful_response.json', 'w', encoding='utf-8') as f:
                            json.dump(response_data, f, indent=2, ensure_ascii=False)
                        logger.info("Response saved to successful_response.json")
                        
                    except json.JSONDecodeError:
                        logger.warning("Response is not valid JSON")
                        logger.info(f"Raw response: {response_text}")
                        
                        # Save raw response
                        with open('raw_response.txt', 'w', encoding='utf-8') as f:
                            f.write(response_text)
                        logger.info("Raw response saved to raw_response.txt")
                        
                else:
                    logger.error(f"FAILED: HTTP {response.status}")
                    logger.error(f"Response: {response_text}")
                    
                    # Save error response
                    with open('error_response.txt', 'w', encoding='utf-8') as f:
                        f.write(f"Status: {response.status}\n")
                        f.write(f"Response: {response_text}")
                    logger.info("Error response saved to error_response.txt")
                
                return {
                    "success": response.status == 200,
                    "status_code": response.status,
                    "response_time": response_time,
                    "response_data": response_text
                }
                
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Request failed: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "response_time": response_time
        }

if __name__ == "__main__":
    try:
        result = asyncio.run(test_hackrx_endpoint())
        print(f"\nTest Result: {'SUCCESS' if result['success'] else 'FAILED'}")
        if 'status_code' in result:
            print(f"Status Code: {result['status_code']}")
        print(f"Response Time: {result.get('response_time', 0):.2f}s")
        
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        print(f"Error: {str(e)}")
