{"event_type": "request", "request_id": "5b2cf110-24ea-472a-8920-ecb7f5893518", "timestamp": "2025-08-02T04:39:08.193052", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://example.com/test.pdf", "questions_count": 1, "questions": ["What is this document about?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "5b2cf110-24ea-472a-8920-ecb7f5893518", "timestamp": "2025-08-02T04:39:08.193209", "success": true, "processing_time_seconds": 1.5, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["This is a test document."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "a79027ea-e437-4d21-9123-8358c376e417", "timestamp": "2025-08-02T05:05:32.415624", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://example.com/test.pdf", "questions_count": 1, "questions": ["What is this document about?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "a79027ea-e437-4d21-9123-8358c376e417", "timestamp": "2025-08-02T05:05:32.415772", "success": true, "processing_time_seconds": 1.5, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["This is a test document."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "8d1e747c-6219-4b4b-b4a6-7c0cf5e6c789", "timestamp": "2025-08-02T05:16:19.839597", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "questions": ["What data is available?"], "questions_truncated": false}}
{"event_type": "error", "request_id": "8d1e747c-6219-4b4b-b4a6-7c0cf5e6c789", "timestamp": "2025-08-02T05:16:19.839991", "error_type": "TypeError", "error_message": "QueryEngine.process_query_request() got an unexpected keyword argument 'request_id'", "processing_time_seconds": 0.0, "context": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4"}, "stack_trace": null}
{"event_type": "request", "request_id": "e5a00714-9bf6-47f1-b9b1-70aa385e160b", "timestamp": "2025-08-02T05:16:19.843112", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://example.com/sample-document.pdf", "questions_count": 3, "questions": ["What is this document about?", "What are the key points mentioned?", "Are there any important dates or deadlines?"], "questions_truncated": false}}
{"event_type": "error", "request_id": "e5a00714-9bf6-47f1-b9b1-70aa385e160b", "timestamp": "2025-08-02T05:16:19.843391", "error_type": "TypeError", "error_message": "QueryEngine.process_query_request() got an unexpected keyword argument 'request_id'", "processing_time_seconds": 0.0, "context": {"documents_url": "https://example.com/sample-document.pdf", "questions_count": 3, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4"}, "stack_trace": null}
{"event_type": "request", "request_id": "f69fad27-ec0b-4879-9095-7baaf4b485d0", "timestamp": "2025-08-02T05:16:30.249311", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "questions": ["What data is available?"], "questions_truncated": false}}
{"event_type": "error", "request_id": "f69fad27-ec0b-4879-9095-7baaf4b485d0", "timestamp": "2025-08-02T05:16:30.249617", "error_type": "TypeError", "error_message": "QueryEngine.process_query_request() got an unexpected keyword argument 'request_id'", "processing_time_seconds": 0.0, "context": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4"}, "stack_trace": null}
{"event_type": "request", "request_id": "7e9c7c62-25ef-4372-a141-b101446a7c2f", "timestamp": "2025-08-02T05:16:30.252705", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://example.com/sample-document.pdf", "questions_count": 3, "questions": ["What is this document about?", "What are the key points mentioned?", "Are there any important dates or deadlines?"], "questions_truncated": false}}
{"event_type": "error", "request_id": "7e9c7c62-25ef-4372-a141-b101446a7c2f", "timestamp": "2025-08-02T05:16:30.252984", "error_type": "TypeError", "error_message": "QueryEngine.process_query_request() got an unexpected keyword argument 'request_id'", "processing_time_seconds": 0.0, "context": {"documents_url": "https://example.com/sample-document.pdf", "questions_count": 3, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4"}, "stack_trace": null}
{"event_type": "request", "request_id": "d800ef51-62de-4e06-aa12-a08aa8b4054f", "timestamp": "2025-08-02T05:19:40.180246", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "questions": ["What data is available?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "d800ef51-62de-4e06-aa12-a08aa8b4054f", "timestamp": "2025-08-02T05:19:41.916761", "success": true, "processing_time_seconds": 1.736, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "ea94f54c-553b-4760-ad3c-f498d61a4af5", "timestamp": "2025-08-02T05:19:41.923209", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "questions": ["What data is available?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "ea94f54c-553b-4760-ad3c-f498d61a4af5", "timestamp": "2025-08-02T05:19:43.552295", "success": true, "processing_time_seconds": 1.629, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "11794187-e1f8-437d-b1cf-dd4a42085d65", "timestamp": "2025-08-02T05:19:43.558628", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://example.com/sample-document.pdf", "questions_count": 3, "questions": ["What is this document about?", "What are the key points mentioned?", "Are there any important dates or deadlines?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "11794187-e1f8-437d-b1cf-dd4a42085d65", "timestamp": "2025-08-02T05:19:44.556377", "success": true, "processing_time_seconds": 0.998, "cache_hits": 0, "response_data": {"answers_count": 3, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again.", "I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "367e8ab4-7163-43f8-ada4-c4d583c699ad", "timestamp": "2025-08-02T05:25:34.720958", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 2, "questions": ["What data is available in this document?", "What is the structure of the data?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "367e8ab4-7163-43f8-ada4-c4d583c699ad", "timestamp": "2025-08-02T05:25:36.462262", "success": true, "processing_time_seconds": 1.741, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again.", "I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "87b303f3-f5b1-4895-a272-8e4fff691361", "timestamp": "2025-08-02T05:25:36.466770", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "87b303f3-f5b1-4895-a272-8e4fff691361", "timestamp": "2025-08-02T05:33:23.682824", "success": true, "processing_time_seconds": 467.216, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["**Answer:**\n\n**The grace period for premium payment under the National Parivar Mediclaim Plus Policy...", "**Answer:**\n\n**Waiting Period for Pre-Existing Diseases (PED):**\n\n- **Standard Waiting Period:** Exp..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "cf6f47dc-be42-4346-9d8e-db7ef6026829", "timestamp": "2025-08-02T05:42:59.024245", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "questions": ["What data is available?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "cf6f47dc-be42-4346-9d8e-db7ef6026829", "timestamp": "2025-08-02T05:43:01.448769", "success": true, "processing_time_seconds": 2.424, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "b0ef4b49-c9b7-45a7-b276-d1af0be079f1", "timestamp": "2025-08-02T05:43:01.455398", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 1, "questions": ["What data is available?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "b0ef4b49-c9b7-45a7-b276-d1af0be079f1", "timestamp": "2025-08-02T05:43:05.145998", "success": true, "processing_time_seconds": 3.691, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "e12df1e2-88ae-4c68-a08e-b68ebace9476", "timestamp": "2025-08-02T05:43:05.152478", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://example.com/sample-document.pdf", "questions_count": 2, "questions": ["What is this document about?", "What are the key points mentioned?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "e12df1e2-88ae-4c68-a08e-b68ebace9476", "timestamp": "2025-08-02T05:43:06.154091", "success": true, "processing_time_seconds": 1.002, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again.", "I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "1d098137-c65f-45a3-a111-085901f104fe", "timestamp": "2025-08-02T06:18:37.142913", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://httpbin.org/json", "questions_count": 2, "questions": ["What data is available in this document?", "What is the structure of the data?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "1d098137-c65f-45a3-a111-085901f104fe", "timestamp": "2025-08-02T06:18:38.889383", "success": true, "processing_time_seconds": 1.746, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["I apologize, but I encountered an error while processing the document. Please try again.", "I apologize, but I encountered an error while processing the document. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "8fe4d6ce-2411-4f73-82cd-e268c71101eb", "timestamp": "2025-08-02T06:18:38.894093", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "8fe4d6ce-2411-4f73-82cd-e268c71101eb", "timestamp": "2025-08-02T06:19:12.267425", "success": true, "processing_time_seconds": 33.373, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["**Answer:**\n\nThe grace period for premium payment under the National Parivar Mediclaim Plus Policy i...", "**Answer:**  \nThe waiting period for coverage of pre-existing diseases (PED) is **thirty-six (36) mo..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "req_1754214566648", "timestamp": "2025-08-03T09:49:26.648612", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://example.com/test-document.pdf", "questions_count": 2, "questions": ["What is the test question 1?", "What is the test question 2?"], "questions_truncated": false}}
{"event_type": "error", "request_id": "req_1754214566648", "timestamp": "2025-08-03T09:49:26.650534", "error_type": "AttributeError", "error_message": "'DocumentProcessor' object has no attribute 'process_document'", "processing_time_seconds": null, "context": {"processing_time": 0.0019221305847167969}, "stack_trace": null}
{"event_type": "request", "request_id": "req_1754216644430", "timestamp": "2025-08-03T10:24:04.430333", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://example.com/test-document.pdf", "questions_count": 1, "questions": ["What is the test question?"], "questions_truncated": false}}
{"event_type": "error", "request_id": "req_1754216644430", "timestamp": "2025-08-03T10:24:05.477549", "error_type": "ClientResponseError", "error_message": "404, message='Not Found', url='https://example.com/test-document.pdf'", "processing_time_seconds": null, "context": {"processing_time": 1.0471999645233154}, "stack_trace": null}
