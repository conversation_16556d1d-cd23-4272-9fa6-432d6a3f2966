"""
Job management data models for the true webhook system.
"""

import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator


class JobStatus(str, Enum):
    """Job status enumeration."""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class JobPriority(int, Enum):
    """Job priority levels."""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    URGENT = 3


class JobRequest(BaseModel):
    """Request model for job submission."""
    documents: str = Field(..., description="URL to the document blob")
    questions: List[str] = Field(..., min_items=1, description="List of questions to answer")
    webhook_url: str = Field(..., description="URL to receive webhook notifications")
    webhook_secret: Optional[str] = Field(None, description="Secret for webhook HMAC signature")
    priority: JobPriority = Field(JobPriority.NORMAL, description="Job priority level")
    timeout_seconds: Optional[int] = Field(300, description="Job timeout in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('webhook_url')
    def validate_webhook_url(cls, v):
        """Validate webhook URL format."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Webhook URL must start with http:// or https://')
        return v
    
    @validator('timeout_seconds')
    def validate_timeout(cls, v):
        """Validate timeout is reasonable."""
        if v is not None and (v < 10 or v > 3600):
            raise ValueError('Timeout must be between 10 and 3600 seconds')
        return v


class JobResponse(BaseModel):
    """Response model for job submission."""
    job_id: str = Field(..., description="Unique job identifier")
    status: JobStatus = Field(JobStatus.QUEUED, description="Current job status")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    webhook_url: str = Field(..., description="Registered webhook URL")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Job creation timestamp")
    priority: JobPriority = Field(JobPriority.NORMAL, description="Job priority")


class JobStatusResponse(BaseModel):
    """Response model for job status queries."""
    job_id: str = Field(..., description="Unique job identifier")
    status: JobStatus = Field(..., description="Current job status")
    progress: Optional[float] = Field(None, description="Job progress percentage (0-100)")
    created_at: datetime = Field(..., description="Job creation timestamp")
    started_at: Optional[datetime] = Field(None, description="Job start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Job completion timestamp")
    processing_time: Optional[float] = Field(None, description="Total processing time in seconds")
    results: Optional[Dict[str, Any]] = Field(None, description="Job results if completed")
    error: Optional[str] = Field(None, description="Error message if failed")
    webhook_url: str = Field(..., description="Registered webhook URL")
    priority: JobPriority = Field(..., description="Job priority")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Job metadata")


class Job(BaseModel):
    """Internal job model for storage and processing."""
    job_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique job identifier")
    status: JobStatus = Field(JobStatus.QUEUED, description="Current job status")
    request: JobRequest = Field(..., description="Original job request")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Job creation timestamp")
    started_at: Optional[datetime] = Field(None, description="Job start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Job completion timestamp")
    progress: float = Field(0.0, description="Job progress percentage (0-100)")
    results: Optional[Dict[str, Any]] = Field(None, description="Job results")
    error: Optional[str] = Field(None, description="Error message if failed")
    worker_id: Optional[str] = Field(None, description="ID of worker processing the job")
    retry_count: int = Field(0, description="Number of retry attempts")
    max_retries: int = Field(3, description="Maximum retry attempts")
    processing_time: Optional[float] = Field(None, description="Total processing time in seconds")
    
    @property
    def is_terminal(self) -> bool:
        """Check if job is in a terminal state."""
        return self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]
    
    @property
    def is_expired(self) -> bool:
        """Check if job has expired based on timeout."""
        if not self.request.timeout_seconds:
            return False
        
        timeout_delta = timedelta(seconds=self.request.timeout_seconds)
        expiry_time = self.created_at + timeout_delta
        return datetime.utcnow() > expiry_time
    
    def to_status_response(self) -> JobStatusResponse:
        """Convert to status response model."""
        return JobStatusResponse(
            job_id=self.job_id,
            status=self.status,
            progress=self.progress,
            created_at=self.created_at,
            started_at=self.started_at,
            completed_at=self.completed_at,
            processing_time=self.processing_time,
            results=self.results,
            error=self.error,
            webhook_url=self.request.webhook_url,
            priority=self.request.priority,
            metadata=self.request.metadata
        )


class WebhookDeliveryStatus(str, Enum):
    """Webhook delivery status enumeration."""
    PENDING = "pending"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRYING = "retrying"


class WebhookDelivery(BaseModel):
    """Model for webhook delivery tracking."""
    delivery_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique delivery identifier")
    job_id: str = Field(..., description="Associated job ID")
    webhook_url: str = Field(..., description="Webhook URL")
    payload: Dict[str, Any] = Field(..., description="Webhook payload")
    signature: Optional[str] = Field(None, description="HMAC signature")
    status: WebhookDeliveryStatus = Field(WebhookDeliveryStatus.PENDING, description="Delivery status")
    attempts: int = Field(0, description="Number of delivery attempts")
    max_attempts: int = Field(3, description="Maximum delivery attempts")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Delivery creation timestamp")
    last_attempt: Optional[datetime] = Field(None, description="Last delivery attempt timestamp")
    next_attempt: Optional[datetime] = Field(None, description="Next scheduled attempt")
    delivered_at: Optional[datetime] = Field(None, description="Successful delivery timestamp")
    error: Optional[str] = Field(None, description="Last error message")
    response_status: Optional[int] = Field(None, description="HTTP response status code")
    response_body: Optional[str] = Field(None, description="HTTP response body")
    
    @property
    def is_terminal(self) -> bool:
        """Check if delivery is in a terminal state."""
        return self.status in [WebhookDeliveryStatus.DELIVERED, WebhookDeliveryStatus.FAILED]
    
    @property
    def should_retry(self) -> bool:
        """Check if delivery should be retried."""
        return (
            self.status in [WebhookDeliveryStatus.PENDING, WebhookDeliveryStatus.RETRYING] and
            self.attempts < self.max_attempts and
            (self.next_attempt is None or datetime.utcnow() >= self.next_attempt)
        )


class JobMetrics(BaseModel):
    """Model for job processing metrics."""
    total_jobs: int = Field(0, description="Total number of jobs")
    queued_jobs: int = Field(0, description="Number of queued jobs")
    processing_jobs: int = Field(0, description="Number of processing jobs")
    completed_jobs: int = Field(0, description="Number of completed jobs")
    failed_jobs: int = Field(0, description="Number of failed jobs")
    cancelled_jobs: int = Field(0, description="Number of cancelled jobs")
    average_processing_time: float = Field(0.0, description="Average processing time in seconds")
    success_rate: float = Field(0.0, description="Success rate percentage")
    webhook_delivery_rate: float = Field(0.0, description="Webhook delivery success rate")
    active_workers: int = Field(0, description="Number of active workers")


class WebhookPayload(BaseModel):
    """Standard webhook payload format."""
    job_id: str = Field(..., description="Job identifier")
    status: JobStatus = Field(..., description="Job status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Notification timestamp")
    results: Optional[Dict[str, Any]] = Field(None, description="Job results if completed")
    error: Optional[str] = Field(None, description="Error message if failed")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class JobQueue(BaseModel):
    """Model for job queue management."""
    queue_name: str = Field(..., description="Queue name")
    priority: JobPriority = Field(..., description="Queue priority")
    max_size: int = Field(1000, description="Maximum queue size")
    current_size: int = Field(0, description="Current queue size")
    processing_rate: float = Field(0.0, description="Jobs processed per minute")
    average_wait_time: float = Field(0.0, description="Average wait time in seconds")