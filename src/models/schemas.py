"""Pydantic models for API request/response schemas."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class QueryRequest(BaseModel):
    """Request model for hackathon API endpoint."""
    documents: str = Field(..., description="URL to the document blob")
    questions: List[str] = Field(..., min_items=1, description="List of questions to answer")


class QueryResponse(BaseModel):
    """Response model for hackathon API endpoint."""
    answers: List[str] = Field(..., description="List of answers corresponding to questions")


class DocumentChunk(BaseModel):
    """Model for document chunks."""
    id: str
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None


class RetrievalResult(BaseModel):
    """Model for retrieval results."""
    chunk: DocumentChunk
    score: float
    rank: int


class RankedChunk(BaseModel):
    """Model for reranked chunks with enhanced metadata."""
    chunk: DocumentChunk
    score: float
    rank: int
    pruned_content: Optional[str] = None
    confidence: Optional[float] = None
    relevance_explanation: Optional[str] = None
    adaptive_threshold: Optional[float] = None


class ProcessingStatus(BaseModel):
    """Model for processing status."""
    status: str
    message: str
    progress: Optional[float] = None


class ErrorResponse(BaseModel):
    """Model for error responses."""
    error: str
    details: Optional[str] = None


class SubQuery(BaseModel):
    """Model for decomposed sub-queries."""
    id: str
    original_question_id: str
    query_text: str
    decomposition_confidence: float
    processing_time: float


class GapAnalysisResult(BaseModel):
    """Model for knowledge gap analysis results."""
    is_sufficient: bool
    missing_information: str
    confidence_score: float
    analysis_time: float
    context_quality_score: float


class RetryResult(BaseModel):
    """Model for conditional retry results."""
    executed: bool
    gap_filler_queries: List[str]
    additional_context: List[str]
    processing_time: float
    success: bool


class ContextAggregation(BaseModel):
    """Model for context aggregation results."""
    initial_chunks: List[str]
    gap_filler_chunks: List[str]
    final_context: str
    deduplication_count: int
    aggregation_time: float

class GroundingValidation(BaseModel):
    """Model for grounding validation results."""
    is_grounded: bool
    confidence: float
    unsupported_claims: List[str] = Field(default_factory=list)
    supporting_evidence: List[str] = Field(default_factory=list)


class EnhancedQuery(BaseModel):
    """Model for HyDE-enhanced queries."""
    original_query: str
    hypothetical_documents: List[str] = Field(default_factory=list)
    original_embedding: Optional[List[float]] = None
    hyde_embeddings: List[List[float]] = Field(default_factory=list)
    domain: str = "general"
    enhancement_confidence: float = 0.0


class HybridRetrievalResult(BaseModel):
    """Model for hybrid retrieval results combining dense and sparse search."""
    chunk: DocumentChunk
    dense_score: float
    sparse_score: float
    rrf_score: float
    final_rank: int
    retrieval_method: str = "hybrid"  # "dense", "sparse", "hybrid"