"""Configuration settings for the LLM Query-Retrieval System."""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_VERSION: str = "v1"
    
    # Azure OpenAI Configuration
    AZURE_OPENAI_API_KEY: str
    AZURE_OPENAI_ENDPOINT: str
    AZURE_OPENAI_API_VERSION: str = "2024-12-01-preview"
    AZURE_OPENAI_MODEL: str = "gpt-4.1"
    AZURE_EMBEDDING_MODEL: str = "text-embedding-3-large"
    AZURE_MAX_TOKENS: int = 4000
    AZURE_TEMPERATURE: float = 0.1
    
    # Vector Store Configuration
    FAISS_INDEX_PATH: str = "data/faiss_index.idx"
    FAISS_METADATA_PATH: str = "data/faiss_metadata.json"
    EMBEDDING_DIMENSION: int = 3072  # text-embedding-3-large dimension
    
    # Document Processing
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    MAX_CHUNKS_PER_QUERY: int = 10
    
    # Semantic Chunking Configuration
    SEMANTIC_SIMILARITY_THRESHOLD: float = 0.7
    SENTENCE_TRANSFORMER_MODEL: str = "all-MiniLM-L6-v2"
    
    # Reranking Configuration
    RERANK_TOP_K: int = 20
    FINAL_TOP_K: int = 5
    PROVENCE_MODEL: str = "naver/provence-reranker-debertav3-v1"
    
    # Authentication
    BEARER_TOKEN: str = "0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378"
    
    # Mistral OCR Configuration (Azure AI)
    AZUREAI_ENDPOINT: Optional[str] = None
    AZUREAI_API_KEY: Optional[str] = None
    
    # OCR Configuration
    OCR_CONFIDENCE_THRESHOLD: float = 0.8
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FILE_PATH: str = "logs/requests.log"
    LOG_ROTATION_SIZE_MB: int = 100
    LOG_BACKUP_COUNT: int = 5
    STRUCTURED_LOGGING: bool = True
    
    # Document Cache Configuration
    DOCUMENT_CACHE_SIZE: int = 100
    DOCUMENT_CACHE_TTL_HOURS: int = 24
    
    # Model Cache Configuration
    MODEL_CACHE_ENABLED: bool = True
    
    # Adaptive Threshold Configuration
    MIN_CHUNKS: int = 3
    MAX_CHUNKS: int = 15
    CONFIDENCE_THRESHOLD: float = 0.7
    DYNAMIC_ADJUSTMENT: bool = True
    FALLBACK_CHUNKS: int = 5
    
    # Multiprocessing Configuration (imported from multiprocessing_config.py)
    MULTIPROCESSING_ENABLED: bool = True
    
    # Query Decomposition Configuration - optimized for speed
    DECOMPOSITION_MAX_SUBQUERIES: int = 3  # Reduced for faster processing
    DECOMPOSITION_TIMEOUT: int = 30  # seconds
    
    # Cohere Reranker Configuration (Azure)
    COHERE_RERANKER_ENDPOINT: str = "https://Cohere-rerank-v3-5-nyyox.eastus2.models.ai.azure.com/v1/rerank"
    COHERE_RERANKER_API_KEY: str = "ofbw1UWv2C6gqsUWnvXC9fmbWDqid25u"
    COHERE_MAX_CONCURRENT_REQUESTS: int = 10
    COHERE_CONNECTION_POOL_SIZE: int = 20
    
    # Gap Analysis Configuration
    GAP_ANALYSIS_TIMEOUT: int = 20  # seconds
    GAP_ANALYSIS_CONFIDENCE_THRESHOLD: float = 0.8
    
    # Parallel Sub-Query Processing Configuration
    MAX_CONCURRENT_SUBQUERIES: int = 16
    SUBQUERY_TIMEOUT: int = 30  # seconds
    
    # Conditional Retry Configuration
    MAX_RETRY_ITERATIONS: int = 1
    RETRY_TIMEOUT: int = 60  # seconds
    GAP_FILLER_MAX_QUERIES: int = 3
    
    # Context Aggregation Configuration
    DEDUPLICATION_SIMILARITY_THRESHOLD: float = 0.9
    MAX_FINAL_CONTEXT_CHUNKS: int = 20
    CONTEXT_FORMATTING_STYLE: str = "numbered"
    
    # Redis Configuration for Job Storage
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # Job Processing Configuration
    JOB_PROCESSOR_MAX_WORKERS: int = 4
    JOB_PROCESSOR_TIMEOUT: int = 300
    JOB_PROCESSOR_POLL_INTERVAL: float = 1.0
    JOB_TTL_DAYS: int = 7
    COMPLETED_JOB_TTL_HOURS: int = 24
    
    # Webhook Configuration
    WEBHOOK_TIMEOUT: int = 30
    WEBHOOK_MAX_RETRIES: int = 3
    WEBHOOK_INITIAL_RETRY_DELAY: int = 5
    WEBHOOK_MAX_RETRY_DELAY: int = 300
    WEBHOOK_REQUIRE_HTTPS: bool = False
    WEBHOOK_TTL_DAYS: int = 3
    
    # Maintenance Configuration
    MAINTENANCE_CLEANUP_INTERVAL: int = 3600  # 1 hour
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()