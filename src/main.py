"""Main FastAPI application entry point."""

import logging
import sys
from pathlib import Path
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.api.routes import router
from src.api.middleware import auth_middleware, logging_middleware, error_handling_middleware
from src.config import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('app.log')
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager with model loading and multiprocessing setup."""
    # Startup
    logger.info("Starting LLM Query-Retrieval System...")
    
    try:
        # Create data directory
        Path("data").mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
        
        # Initialize multiprocessing manager
        from src.services.multiprocessing_manager import mp_manager
        await mp_manager.initialize()
        logger.info("Multiprocessing manager initialized")
        
        # Load models into cache
        from src.services.model_cache import model_cache
        await model_cache.load_all_models()
        logger.info("Models loaded into cache")
        
        # Initialize request logger
        from src.services.logging_service import request_logger
        logger.info("Request logging service initialized")
        
        # Initialize job processing system
        from src.services.background_job_processor import background_job_processor
        await background_job_processor.start()
        logger.info("Background job processor started")
        
        # Initialize maintenance service
        from src.services.maintenance_service import maintenance_service
        await maintenance_service.start()
        logger.info("Maintenance service started")
        
        # Log system status
        logger.info("System startup complete with enhanced features:")
        logger.info("  ✓ Multiprocessing optimization enabled")
        logger.info("  ✓ Application-level model caching enabled")
        logger.info("  ✓ LRU document caching enabled")
        logger.info("  ✓ Comprehensive request logging enabled")
        logger.info("  ✓ Background job processing enabled")
        logger.info("  ✓ True webhook system enabled")
        
    except Exception as e:
        logger.error(f"Failed to initialize system: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down LLM Query-Retrieval System...")
    
    try:
        # Cleanup multiprocessing manager
        await mp_manager.cleanup()
        logger.info("Multiprocessing manager cleaned up")
        
        # Cleanup model cache
        await model_cache.cleanup_models()
        logger.info("Model cache cleaned up")
        
        # Stop maintenance service
        from src.services.maintenance_service import maintenance_service
        await maintenance_service.stop()
        logger.info("Maintenance service stopped")
        
        # Stop job processing system
        from src.services.background_job_processor import background_job_processor
        await background_job_processor.stop()
        logger.info("Background job processor stopped")
        
        logger.info("System shutdown complete")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="LLM-Powered Intelligent Query-Retrieval System",
    description="""
    An intelligent document processing and query system that:
    - Processes PDFs, DOCX, and email documents from URLs
    - Uses semantic search with FAISS vector storage
    - Implements reranking for improved relevance
    - Provides contextual answers using GPT-4
    - Specializes in insurance, legal, HR, and compliance domains
    - Includes webhook endpoints for external system integration
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware (order matters - last added is executed first)
app.middleware("http")(error_handling_middleware)
app.middleware("http")(auth_middleware)
app.middleware("http")(logging_middleware)

# Include API routes
app.include_router(router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "LLM-Powered Intelligent Query-Retrieval System",
        "version": "1.0.0",
        "docs": "/docs",
        "api": "/api/v1"
    }


if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"Starting server on {settings.API_HOST}:{settings.API_PORT}")
    
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    )