"""Core query engine that orchestrates the entire retrieval pipeline."""

import logging
from typing import List, Dict, Any
import asyncio

from src.services.document_processor import DocumentProcessor
from src.services.embedding_service import AzureEmbeddingService
from src.services.vector_store import VectorStore
from src.services.cohere_reranker_service import cohere_reranker
from src.services.llm_service import AzureLLMService
from src.models.schemas import QueryRequest, QueryResponse, RetrievalResult
from src.config import settings

logger = logging.getLogger(__name__)


class QueryEngine:
    """Main query engine that orchestrates the retrieval pipeline."""
    
    def __init__(self):
        self.document_processor = DocumentProcessor()
        self.embedding_service = AzureEmbeddingService()
        self.vector_store = VectorStore()
        self.reranker = cohere_reranker
        self.llm_service = AzureLLMService()
        
    async def process_query_request(self, request: QueryRequest, request_id: str = None) -> QueryResponse:
        """
        Process a complete query request from document URL and questions.
        
        Args:
            request: Query request containing document URL and questions
            request_id: Optional request ID for logging
            
        Returns:
            Query response with answers
        """
        try:
            log_prefix = f"[{request_id}] " if request_id else ""
            logger.info(f"{log_prefix}Processing query request with {len(request.questions)} questions")
            
            # Step 1: Process document
            logger.info(f"{log_prefix}Step 1: Processing document...")
            chunks = await self.document_processor.process_document_from_url(str(request.documents))
            
            if not chunks:
                raise ValueError("No content extracted from document")
            
            # Step 2: Generate embeddings
            logger.info(f"{log_prefix}Step 2: Generating embeddings...")
            chunks_with_embeddings = await self.embedding_service.generate_embeddings(chunks)
            
            # Step 3: Build vector index
            logger.info(f"{log_prefix}Step 3: Building vector index...")
            await self.vector_store.build_index(chunks_with_embeddings)
            
            # Step 4: Process each question
            logger.info(f"{log_prefix}Step 4: Processing questions...")
            answers = await self._process_questions(request.questions)
            
            logger.info(f"{log_prefix}Successfully processed {len(answers)} questions")
            return QueryResponse(answers=answers)
            
        except Exception as e:
            logger.error(f"Error processing query request: {str(e)}")
            # Return error responses for all questions
            error_message = "I apologize, but I encountered an error while processing the document. Please try again."
            return QueryResponse(answers=[error_message] * len(request.questions))
    
    async def _process_questions(self, questions: List[str]) -> List[str]:
        """Process multiple questions in parallel and return answers."""
        try:
            logger.info(f"Processing {len(questions)} questions in parallel")
            
            # Generate query embeddings for all questions in parallel
            query_embeddings = await asyncio.gather(*[
                self.embedding_service.generate_query_embedding(question)
                for question in questions
            ])
            
            # Retrieve context for each question in parallel
            retrieval_tasks = [
                self._retrieve_context(question, embedding)
                for question, embedding in zip(questions, query_embeddings)
            ]
            
            context_results_list = await asyncio.gather(*retrieval_tasks)
            
            # Batch rerank all questions at once for better performance
            logger.info("Batch reranking all questions...")
            all_ranked_chunks = await self.reranker.rerank_multiple_queries_batch(
                questions, context_results_list
            )
            
            # Generate answers in parallel using the ranked chunks
            logger.info("Generating answers in parallel...")
            answer_tasks = [
                self.llm_service.generate_answer_from_ranked_chunks(
                    question,
                    ranked_chunks,
                    domain_context=self._get_domain_context()
                )
                for question, ranked_chunks in zip(questions, all_ranked_chunks)
            ]
            
            answers = await asyncio.gather(*answer_tasks)
            
            logger.info(f"Successfully processed {len(answers)} questions in parallel")
            return answers
            
        except Exception as e:
            logger.error(f"Error processing questions: {str(e)}")
            return ["Error processing question. Please try again."] * len(questions)
    
    async def _process_single_question(self, question: str, context_results: List[RetrievalResult]) -> str:
        """Process a single question with reranking and answer generation."""
        try:
            # Convert retrieval results to ranked chunks using Cohere reranker
            # Convert retrieval results to document chunks for reranking
            document_chunks = []
            for result in context_results:
                if hasattr(result, 'chunk'):
                    document_chunks.append(result.chunk)
                else:
                    # Create DocumentChunk from retrieval result
                    from src.models.schemas import DocumentChunk
                    chunk = DocumentChunk(
                        id=f"chunk_{len(document_chunks)}",
                        content=str(result),
                        metadata={"source": "retrieval"}
                    )
                    document_chunks.append(chunk)
            
            # Use Cohere reranker for single query
            ranked_chunks = await self.reranker.rerank_single(question, document_chunks)
            
            # Generate answer using ranked chunks
            answer = await self.llm_service.generate_answer_from_ranked_chunks(
                question,
                ranked_chunks,
                domain_context=self._get_domain_context()
            )
            
            return answer
            
        except Exception as e:
            logger.error(f"Error processing single question: {str(e)}")
            return "Error processing question. Please try again."
    
    async def _retrieve_context(self, question: str, query_embedding: List[float]) -> List[RetrievalResult]:
        """Retrieve and rerank context for a single question."""
        try:
            # Step 1: Vector similarity search
            initial_results = await self.vector_store.search(
                query_embedding, 
                top_k=settings.RERANK_TOP_K
            )
            
            if not initial_results:
                logger.warning(f"No results found for question: {question[:50]}...")
                return []
            
            # Step 2: Return initial results for Provence processing
            # The reranking will be done later in the pipeline with pruning
            return initial_results
            
        except Exception as e:
            logger.error(f"Error retrieving context for question: {str(e)}")
            return []
    
    def _get_domain_context(self) -> str:
        """Get domain-specific context for better answer generation."""
        return """This document analysis focuses on insurance, legal, HR, and compliance domains. 
        
Key areas to consider:
- Insurance: Coverage terms, waiting periods, exclusions, claim procedures, premium details
- Legal: Contract clauses, terms and conditions, legal obligations, compliance requirements  
- HR: Employee benefits, policies, procedures, eligibility criteria
- Compliance: Regulatory requirements, standards, audit criteria, reporting obligations

When answering questions, be specific about:
- Exact waiting periods and time frames
- Coverage limits and sub-limits
- Conditions and eligibility requirements
- Exclusions and limitations
- Procedural requirements
- Applicable definitions and terms"""
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all services."""
        try:
            health_status = {
                "status": "healthy",
                "services": {
                    "document_processor": "healthy",
                    "embedding_service": "healthy", 
                    "vector_store": "healthy",
                    "reranker": "healthy",
                    "llm_service": "healthy"
                },
                "vector_store_stats": self.vector_store.get_index_stats()
            }
            
            return health_status
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }