{"event_type": "request", "request_id": "47c69918-94e0-4926-a7a1-23e592c950da", "timestamp": "2025-08-02T08:09:20.304534", "client_ip": "*************", "user_agent": "PostmanRuntime/7.44.1", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "47c69918-94e0-4926-a7a1-23e592c950da", "timestamp": "2025-08-02T08:09:59.387109", "success": true, "processing_time_seconds": 39.083, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["**Answer:**\n\nThe grace period for premium payment under the National Parivar Mediclaim Plus Policy i...", "**Answer:**\n\n**The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) m..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "ce307a55-575d-47f2-982c-b987b02dc67c", "timestamp": "2025-08-02T08:14:47.052419", "client_ip": "*************", "user_agent": "PostmanRuntime/7.44.1", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "ce307a55-575d-47f2-982c-b987b02dc67c", "timestamp": "2025-08-02T08:15:20.965728", "success": true, "processing_time_seconds": 33.913, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["**Answer:**\n\nThe grace period for premium payment under the National Parivar Mediclaim Plus Policy i...", "**Answer:**\n\nThe waiting period for coverage of pre-existing diseases (PED) is **thirty-six (36) mon..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "af162628-70d3-4bf9-ada4-510f713832ee", "timestamp": "2025-08-02T08:15:48.075954", "client_ip": "*************", "user_agent": "PostmanRuntime/7.44.1", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "af162628-70d3-4bf9-ada4-510f713832ee", "timestamp": "2025-08-02T08:16:25.171077", "success": true, "processing_time_seconds": 37.095, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["**Answer:**\n\nThe grace period for premium payment under the National Parivar Mediclaim Plus Policy i...", "**Answer:**\n\n**The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) m..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "94b65dcd-8d71-4793-a061-09376431b8ed", "timestamp": "2025-08-02T08:19:58.068756", "client_ip": "*************", "user_agent": "PostmanRuntime/7.44.1", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "94b65dcd-8d71-4793-a061-09376431b8ed", "timestamp": "2025-08-02T08:26:47.715086", "success": true, "processing_time_seconds": 409.646, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["**Answer:**\n\nThe grace period for premium payment under the National Parivar Mediclaim Plus Policy i...", "**Answer:**\n\n**Waiting Period for Pre-Existing Diseases (PED):**\n\n- **Standard Waiting Period:** Exp..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "cd21663e-4072-4a4d-9e7d-9e500d665562", "timestamp": "2025-08-03T03:56:53.731748", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "cd21663e-4072-4a4d-9e7d-9e500d665562", "timestamp": "2025-08-03T04:04:19.529340", "success": true, "processing_time_seconds": 445.798, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["**Answer:**\n\n**The grace period for premium payment under the National Parivar Mediclaim Plus Policy...", "**Answer:**\n\n**The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) m..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "13cbbe9d-b27a-4c5e-a567-5c9158a7ee72", "timestamp": "2025-08-03T10:04:48.200601", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754215488200", "timestamp": "2025-08-03T10:04:48.200995", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "error", "request_id": "req_1754215488200", "timestamp": "2025-08-03T10:04:48.201354", "error_type": "AttributeError", "error_message": "'DocumentProcessor' object has no attribute 'process_document'", "processing_time_seconds": null, "context": {"processing_time": 0.0003597736358642578}, "stack_trace": null}
{"event_type": "response", "request_id": "13cbbe9d-b27a-4c5e-a567-5c9158a7ee72", "timestamp": "2025-08-03T10:04:48.201597", "success": true, "processing_time_seconds": 0.001, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "I apologize, but I encountered an error while processing your question. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "5924ebdb-a116-4547-83a4-c40ec9f0d466", "timestamp": "2025-08-03T10:11:45.261065", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754215905261", "timestamp": "2025-08-03T10:11:45.261480", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "error", "request_id": "req_1754215905261", "timestamp": "2025-08-03T10:12:17.282859", "error_type": "AttributeError", "error_message": "'list' object has no attribute 'additional_context'", "processing_time_seconds": null, "context": {"processing_time": 32.02137541770935}, "stack_trace": null}
{"event_type": "response", "request_id": "5924ebdb-a116-4547-83a4-c40ec9f0d466", "timestamp": "2025-08-03T10:12:17.283157", "success": true, "processing_time_seconds": 32.022, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "I apologize, but I encountered an error while processing your question. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "1d805edb-403a-4daf-a55e-13b9c91f2c62", "timestamp": "2025-08-03T10:24:46.235779", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754216686236", "timestamp": "2025-08-03T10:24:46.236154", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754216686236", "timestamp": "2025-08-03T10:25:31.584298", "success": true, "processing_time_seconds": 45.348, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["Error processing questions. Please try again.", "Error processing questions. Please try again."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "1d805edb-403a-4daf-a55e-13b9c91f2c62", "timestamp": "2025-08-03T10:25:31.584613", "success": true, "processing_time_seconds": 45.349, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["Error processing questions. Please try again.", "Error processing questions. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "79815bfd-ce91-4e27-9187-b827699c0023", "timestamp": "2025-08-03T10:34:14.997954", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754217254998", "timestamp": "2025-08-03T10:34:14.998361", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754217254998", "timestamp": "2025-08-03T10:34:33.803706", "success": true, "processing_time_seconds": 18.805, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["Error processing questions. Please try again.", "Error processing questions. Please try again."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "79815bfd-ce91-4e27-9187-b827699c0023", "timestamp": "2025-08-03T10:34:33.804005", "success": true, "processing_time_seconds": 18.806, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["Error processing questions. Please try again.", "Error processing questions. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "359791a2-1a18-4b05-bc5e-df2726afd632", "timestamp": "2025-08-03T10:37:57.595559", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment?", "What is the waiting period for pre-existing diseases?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754217477595", "timestamp": "2025-08-03T10:37:57.595976", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment?", "What is the waiting period for pre-existing diseases?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754217477595", "timestamp": "2025-08-03T10:38:22.226005", "success": true, "processing_time_seconds": 24.63, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["Answer:\n\nThe provided context does **not contain information regarding the grace period for premium ...", "Answer:\n\nThe provided context does **not contain information regarding the waiting period for pre-ex..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "359791a2-1a18-4b05-bc5e-df2726afd632", "timestamp": "2025-08-03T10:38:22.226308", "success": true, "processing_time_seconds": 24.631, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["Answer:\n\nThe provided context does **not contain information regarding the grace period for premium ...", "Answer:\n\nThe provided context does **not contain information regarding the waiting period for pre-ex..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "d8c30341-4d48-416e-a9ef-b2d6aa954f52", "timestamp": "2025-08-03T10:38:44.726356", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754217524726", "timestamp": "2025-08-03T10:38:44.726694", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754217524726", "timestamp": "2025-08-03T10:39:32.459635", "success": true, "processing_time_seconds": 47.733, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["Answer:\n\nThe provided context does **not contain information regarding the grace period for premium ...", "Answer:\n\nThe provided context does **not specify the waiting period for pre-existing diseases (PED)*..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "d8c30341-4d48-416e-a9ef-b2d6aa954f52", "timestamp": "2025-08-03T10:39:32.459932", "success": true, "processing_time_seconds": 47.734, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["Answer:\n\nThe provided context does **not contain information regarding the grace period for premium ...", "Answer:\n\nThe provided context does **not specify the waiting period for pre-existing diseases (PED)*..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "2de51097-8808-4fdf-bfb1-102cb8fc54c1", "timestamp": "2025-08-03T10:49:27.485313", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754218167485", "timestamp": "2025-08-03T10:49:27.485728", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754218167485", "timestamp": "2025-08-03T10:50:11.220857", "success": true, "processing_time_seconds": 43.735, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "The information regarding the waiting period for pre-existing diseases (PED) to be covered is not av..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "2de51097-8808-4fdf-bfb1-102cb8fc54c1", "timestamp": "2025-08-03T10:50:11.221279", "success": true, "processing_time_seconds": 43.736, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "The information regarding the waiting period for pre-existing diseases (PED) to be covered is not av..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "e1d4be86-f6c5-4513-ac6d-dc6ecf18cc78", "timestamp": "2025-08-03T11:01:27.919915", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754218887920", "timestamp": "2025-08-03T11:01:27.920306", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "b11c07c8-41f7-46cc-8022-d51c0da7def4", "timestamp": "2025-08-03T11:11:13.421949", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754219473423", "timestamp": "2025-08-03T11:11:13.423974", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "99cdc020-f14e-4a99-a99b-33e9d943f1c3", "timestamp": "2025-08-03T11:25:05.110127", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754220305110", "timestamp": "2025-08-03T11:25:05.110537", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "5623fda5-ef53-4eaa-9aac-e43d1c635a41", "timestamp": "2025-08-03T11:39:56.502080", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754221196502", "timestamp": "2025-08-03T11:39:56.502503", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754221196502", "timestamp": "2025-08-03T11:48:19.399882", "success": true, "processing_time_seconds": 502.897, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "5623fda5-ef53-4eaa-9aac-e43d1c635a41", "timestamp": "2025-08-03T11:48:19.400234", "success": true, "processing_time_seconds": 502.898, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "1cc8d596-f1be-478b-a893-4e318ce88c02", "timestamp": "2025-08-03T12:07:08.554597", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754222828555", "timestamp": "2025-08-03T12:07:08.555012", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754222828555", "timestamp": "2025-08-03T12:09:53.310032", "success": true, "processing_time_seconds": 164.755, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The specific duration of the grace period for premium payment under the National Parivar Mediclaim P...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "1cc8d596-f1be-478b-a893-4e318ce88c02", "timestamp": "2025-08-03T12:09:53.310354", "success": true, "processing_time_seconds": 164.756, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The specific duration of the grace period for premium payment under the National Parivar Mediclaim P...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "defc4113-915e-4ecb-b978-0b9357d4b6c2", "timestamp": "2025-08-03T12:12:28.004965", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754223148005", "timestamp": "2025-08-03T12:12:28.005401", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754223148005", "timestamp": "2025-08-03T12:13:34.578659", "success": true, "processing_time_seconds": 66.573, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "defc4113-915e-4ecb-b978-0b9357d4b6c2", "timestamp": "2025-08-03T12:13:34.578961", "success": true, "processing_time_seconds": 66.574, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "bb68c87f-5a65-4ab4-9ef0-308ff24911fd", "timestamp": "2025-08-03T12:31:21.282362", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754224281282", "timestamp": "2025-08-03T12:31:21.282802", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754224281282", "timestamp": "2025-08-03T12:31:42.306473", "success": true, "processing_time_seconds": 21.024, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The information regarding the grace period for premium payment under the National Parivar Mediclaim ...", "The information regarding the waiting period for pre-existing diseases (PED) to be covered is not av..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "bb68c87f-5a65-4ab4-9ef0-308ff24911fd", "timestamp": "2025-08-03T12:31:42.306786", "success": true, "processing_time_seconds": 21.024, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The information regarding the grace period for premium payment under the National Parivar Mediclaim ...", "The information regarding the waiting period for pre-existing diseases (PED) to be covered is not av..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "af6d5aff-0d3d-4d00-9898-4062ab0b7d6a", "timestamp": "2025-08-03T12:38:39.552385", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754224719552", "timestamp": "2025-08-03T12:38:39.552784", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754224719552", "timestamp": "2025-08-03T12:39:24.964874", "success": true, "processing_time_seconds": 45.412, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "af6d5aff-0d3d-4d00-9898-4062ab0b7d6a", "timestamp": "2025-08-03T12:39:24.965176", "success": true, "processing_time_seconds": 45.413, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "198f4367-bf49-4c6c-807e-4585d07df4c0", "timestamp": "2025-08-03T12:39:44.653117", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754224784653", "timestamp": "2025-08-03T12:39:44.653525", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754224784653", "timestamp": "2025-08-03T12:46:51.788073", "success": true, "processing_time_seconds": 427.135, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "198f4367-bf49-4c6c-807e-4585d07df4c0", "timestamp": "2025-08-03T12:46:51.791001", "success": true, "processing_time_seconds": 427.138, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "e149017b-b4fc-4e24-8619-9183beba6568", "timestamp": "2025-08-03T13:02:40.312329", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754226160312", "timestamp": "2025-08-03T13:02:40.312751", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754226160312", "timestamp": "2025-08-03T13:03:27.916626", "success": true, "processing_time_seconds": 47.604, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "e149017b-b4fc-4e24-8619-9183beba6568", "timestamp": "2025-08-03T13:03:27.916913", "success": true, "processing_time_seconds": 47.605, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "a5d8d794-f9e9-42c2-89e2-729b715693b7", "timestamp": "2025-08-03T13:04:00.198268", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754226240198", "timestamp": "2025-08-03T13:04:00.198637", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754226240198", "timestamp": "2025-08-03T13:12:18.988159", "success": true, "processing_time_seconds": 498.79, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "I apologize, but I encountered an error while processing your question. Please try again."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "a5d8d794-f9e9-42c2-89e2-729b715693b7", "timestamp": "2025-08-03T13:12:18.988930", "success": true, "processing_time_seconds": 498.791, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["I apologize, but I encountered an error while processing your question. Please try again.", "I apologize, but I encountered an error while processing your question. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "a6e4bc49-c2d7-4835-8b50-e2b54fc6742e", "timestamp": "2025-08-03T13:31:31.301797", "client_ip": "**************", "user_agent": "curl/7.81.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754227891302", "timestamp": "2025-08-03T13:31:31.302214", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754227891302", "timestamp": "2025-08-03T13:32:18.386001", "success": true, "processing_time_seconds": 47.084, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "a6e4bc49-c2d7-4835-8b50-e2b54fc6742e", "timestamp": "2025-08-03T13:32:18.386306", "success": true, "processing_time_seconds": 47.084, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "5d393691-493d-4b7a-8b8b-9127e215f5d3", "timestamp": "2025-08-03T13:32:45.303896", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754227965304", "timestamp": "2025-08-03T13:32:45.304282", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754227965304", "timestamp": "2025-08-03T13:39:27.532608", "success": true, "processing_time_seconds": 402.228, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "5d393691-493d-4b7a-8b8b-9127e215f5d3", "timestamp": "2025-08-03T13:39:27.534214", "success": true, "processing_time_seconds": 402.23, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "975fb28e-86d7-470f-974c-0bd525656f1b", "timestamp": "2025-08-03T13:58:23.092842", "client_ip": "*************", "user_agent": "axios/1.10.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Arogya%20Sanjeevani%20Policy%20-%20CIN%20-%20U10200WB1906GOI001713%201.pdf?sv=2023-01-03&st=2025-07-21T08%3A29%3A02Z&se=2025-09-22T08%3A29%3A00Z&sr=b&sp=r&sig=nzrz1K9Iurt%2BBXom%2FB%2BMPTFMFP3PRnIvEsipAX10Ig4%3D", "questions_count": 4, "questions": ["When will my root canal claim of Rs 25,000 be settled?", "I have done an IVF for Rs 56,000. Is it covered?", "I did a cataract treatment of Rs 100,000. Will you settle the full Rs 100,000?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754229503093", "timestamp": "2025-08-03T13:58:23.093211", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Arogya%20Sanjeevani%20Policy%20-%20CIN%20-%20U10200WB1906GOI001713%201.pdf?sv=2023-01-03&st=2025-07-21T08%3A29%3A02Z&se=2025-09-22T08%3A29%3A00Z&sr=b&sp=r&sig=nzrz1K9Iurt%2BBXom%2FB%2BMPTFMFP3PRnIvEsipAX10Ig4%3D", "questions_count": 4, "questions": ["When will my root canal claim of Rs 25,000 be settled?", "I have done an IVF for Rs 56,000. Is it covered?", "I did a cataract treatment of Rs 100,000. Will you settle the full Rs 100,000?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "3316c263-1ec6-47d1-a7ba-d74a68a759cd", "timestamp": "2025-08-03T13:59:50.389491", "client_ip": "*************", "user_agent": "axios/1.10.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Arogya%20Sanjeevani%20Policy%20-%20CIN%20-%20U10200WB1906GOI001713%201.pdf?sv=2023-01-03&st=2025-07-21T08%3A29%3A02Z&se=2025-09-22T08%3A29%3A00Z&sr=b&sp=r&sig=nzrz1K9Iurt%2BBXom%2FB%2BMPTFMFP3PRnIvEsipAX10Ig4%3D", "questions_count": 1, "questions": ["I have raised a claim for hospitalization for Rs 200,000 with HDFC, and it's approved. My total expenses are Rs 250,000. Can I raise the remaining Rs 50,000 with you?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754229590390", "timestamp": "2025-08-03T13:59:50.390033", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Arogya%20Sanjeevani%20Policy%20-%20CIN%20-%20U10200WB1906GOI001713%201.pdf?sv=2023-01-03&st=2025-07-21T08%3A29%3A02Z&se=2025-09-22T08%3A29%3A00Z&sr=b&sp=r&sig=nzrz1K9Iurt%2BBXom%2FB%2BMPTFMFP3PRnIvEsipAX10Ig4%3D", "questions_count": 1, "questions": ["I have raised a claim for hospitalization for Rs 200,000 with HDFC, and it's approved. My total expenses are Rs 250,000. Can I raise the remaining Rs 50,000 with you?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "c53092c1-9c84-4fc1-bd82-b38d68aa1cad", "timestamp": "2025-08-03T14:00:38.098825", "client_ip": "*************", "user_agent": "axios/1.10.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Super_Splendor_(Feb_2023).pdf?sv=2023-01-03&st=2025-07-21T08%3A10%3A00Z&se=2025-09-22T08%3A10%3A00Z&sr=b&sp=r&sig=vhHrl63YtrEOCsAy%2BpVKr20b3ZUo5HMz1lF9%2BJh6LQ0%3D", "questions_count": 5, "questions": ["What is the ideal spark plug gap recommeded", "Does this comes in tubeless tyre version", "Is it compulsoury to have a disc brake"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754229638099", "timestamp": "2025-08-03T14:00:38.099224", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Super_Splendor_(Feb_2023).pdf?sv=2023-01-03&st=2025-07-21T08%3A10%3A00Z&se=2025-09-22T08%3A10%3A00Z&sr=b&sp=r&sig=vhHrl63YtrEOCsAy%2BpVKr20b3ZUo5HMz1lF9%2BJh6LQ0%3D", "questions_count": 5, "questions": ["What is the ideal spark plug gap recommeded", "Does this comes in tubeless tyre version", "Is it compulsoury to have a disc brake"], "questions_truncated": true}}
{"event_type": "request", "request_id": "8b7973d0-7b0f-4f33-a49f-122bd0a29c26", "timestamp": "2025-08-03T14:01:28.129034", "client_ip": "*************", "user_agent": "axios/1.10.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Family%20Medicare%20Policy%20(UIN-%20UIIHLIP22070V042122)%201.pdf?sv=2023-01-03&st=2025-07-22T10%3A17%3A39Z&se=2025-08-23T10%3A17%3A00Z&sr=b&sp=r&sig=dA7BEMIZg3WcePcckBOb4QjfxK%2B4rIfxBs2%2F%2BNwoPjQ%3D", "questions_count": 3, "questions": ["Is Non-infective Arthritis covered?", "I renewed my policy yesterday, and I have been a customer for the last 6 years. Can I raise a claim for Hydrocele?", "Is abortion covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754229688129", "timestamp": "2025-08-03T14:01:28.129478", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/Family%20Medicare%20Policy%20(UIN-%20UIIHLIP22070V042122)%201.pdf?sv=2023-01-03&st=2025-07-22T10%3A17%3A39Z&se=2025-08-23T10%3A17%3A00Z&sr=b&sp=r&sig=dA7BEMIZg3WcePcckBOb4QjfxK%2B4rIfxBs2%2F%2BNwoPjQ%3D", "questions_count": 3, "questions": ["Is Non-infective Arthritis covered?", "I renewed my policy yesterday, and I have been a customer for the last 6 years. Can I raise a claim for Hydrocele?", "Is abortion covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754229590390", "timestamp": "2025-08-03T14:02:31.340822", "success": true, "processing_time_seconds": 160.951, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["Yes, if the amount to be claimed exceeds the sum insured under a single policy, you have the right t..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "3316c263-1ec6-47d1-a7ba-d74a68a759cd", "timestamp": "2025-08-03T14:02:31.341304", "success": true, "processing_time_seconds": 160.952, "cache_hits": 0, "response_data": {"answers_count": 1, "answers_preview": ["Yes, if the amount to be claimed exceeds the sum insured under a single policy, you have the right t..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "c5ff9716-10d3-47e3-8494-b0681e60011d", "timestamp": "2025-08-03T14:02:31.369603", "client_ip": "*************", "user_agent": "axios/1.10.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/indian_constitution.pdf?sv=2023-01-03&st=2025-07-28T06%3A42%3A00Z&se=2026-11-29T06%3A42%3A00Z&sr=b&sp=r&sig=5Gs%2FOXqP3zY00lgciu4BZjDV5QjTDIx7fgnfdz6Pu24%3D", "questions_count": 10, "questions": ["What is the official name of India according to Article 1 of the Constitution?", "Which Article guarantees equality before the law and equal protection of laws to all persons?", "What is abolished by Article 17 of the Constitution?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754229751370", "timestamp": "2025-08-03T14:02:31.370068", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/indian_constitution.pdf?sv=2023-01-03&st=2025-07-28T06%3A42%3A00Z&se=2026-11-29T06%3A42%3A00Z&sr=b&sp=r&sig=5Gs%2FOXqP3zY00lgciu4BZjDV5QjTDIx7fgnfdz6Pu24%3D", "questions_count": 10, "questions": ["What is the official name of India according to Article 1 of the Constitution?", "Which Article guarantees equality before the law and equal protection of laws to all persons?", "What is abolished by Article 17 of the Constitution?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "b9d192d3-82c7-4d7e-aed0-d1e17270faed", "timestamp": "2025-08-03T14:03:49.126056", "client_ip": "*************", "user_agent": "axios/1.10.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/indian_constitution.pdf?sv=2023-01-03&st=2025-07-28T06%3A42%3A00Z&se=2026-11-29T06%3A42%3A00Z&sr=b&sp=r&sig=5Gs%2FOXqP3zY00lgciu4BZjDV5QjTDIx7fgnfdz6Pu24%3D", "questions_count": 10, "questions": ["If my car is stolen, what case will it be in law?", "If I am arrested without a warrant, is that legal?", "If someone denies me a job because of my caste, is that allowed?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754229829126", "timestamp": "2025-08-03T14:03:49.126479", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/indian_constitution.pdf?sv=2023-01-03&st=2025-07-28T06%3A42%3A00Z&se=2026-11-29T06%3A42%3A00Z&sr=b&sp=r&sig=5Gs%2FOXqP3zY00lgciu4BZjDV5QjTDIx7fgnfdz6Pu24%3D", "questions_count": 10, "questions": ["If my car is stolen, what case will it be in law?", "If I am arrested without a warrant, is that legal?", "If someone denies me a job because of my caste, is that allowed?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "49a054ee-ded8-4001-83de-a8cb05ac6708", "timestamp": "2025-08-03T14:05:04.679524", "client_ip": "*************", "user_agent": "axios/1.10.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/principia_newton.pdf?sv=2023-01-03&st=2025-07-28T07%3A20%3A32Z&se=2026-07-29T07%3A20%3A00Z&sr=b&sp=r&sig=V5I1QYyigoxeUMbnUKsdEaST99F5%2FDfo7wpKg9XXF5w%3D", "questions_count": 12, "questions": ["How does Newton define 'quantity of motion' and how is it distinct from 'force'?", "According to Newton, what are the three laws of motion and how do they apply in celestial mechanics?", "How does Newton derive Kepler's Second Law (equal areas in equal times) from his laws of motion and gravitation?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754229904679", "timestamp": "2025-08-03T14:05:04.679938", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/principia_newton.pdf?sv=2023-01-03&st=2025-07-28T07%3A20%3A32Z&se=2026-07-29T07%3A20%3A00Z&sr=b&sp=r&sig=V5I1QYyigoxeUMbnUKsdEaST99F5%2FDfo7wpKg9XXF5w%3D", "questions_count": 12, "questions": ["How does Newton define 'quantity of motion' and how is it distinct from 'force'?", "According to Newton, what are the three laws of motion and how do they apply in celestial mechanics?", "How does Newton derive Kepler's Second Law (equal areas in equal times) from his laws of motion and gravitation?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754229503093", "timestamp": "2025-08-03T14:12:41.800269", "success": true, "processing_time_seconds": 858.707, "cache_hits": 0, "response_data": {"answers_count": 4, "answers_preview": ["Your root canal claim of Rs 25,000 will be settled or rejected within 15 days from the date of recei...", "Based on the provided context, expenses related to assisted reproduction services including IVF are ..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "975fb28e-86d7-470f-974c-0bd525656f1b", "timestamp": "2025-08-03T14:12:41.800578", "success": true, "processing_time_seconds": 858.708, "cache_hits": 0, "response_data": {"answers_count": 4, "answers_preview": ["Your root canal claim of Rs 25,000 will be settled or rejected within 15 days from the date of recei...", "Based on the provided context, expenses related to assisted reproduction services including IVF are ..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "req_1754229688129", "timestamp": "2025-08-03T14:17:14.350440", "success": true, "processing_time_seconds": 946.221, "cache_hits": 0, "response_data": {"answers_count": 3, "answers_preview": ["Non-infective Arthritis is covered under Table B as indicated in the provided context.", "Based on the provided context, expenses related to the treatment of Hydrocele are excluded until the..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "8b7973d0-7b0f-4f33-a49f-122bd0a29c26", "timestamp": "2025-08-03T14:17:14.350999", "success": true, "processing_time_seconds": 946.222, "cache_hits": 0, "response_data": {"answers_count": 3, "answers_preview": ["Non-infective Arthritis is covered under Table B as indicated in the provided context.", "Based on the provided context, expenses related to the treatment of Hydrocele are excluded until the..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "req_1754229638099", "timestamp": "2025-08-03T14:17:20.161400", "success": true, "processing_time_seconds": 1002.062, "cache_hits": 0, "response_data": {"answers_count": 5, "answers_preview": ["The ideal spark plug gap recommended is 0.8-0.9 mm.", "Yes, according to the provided context, this comes with tubeless tyres."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "c53092c1-9c84-4fc1-bd82-b38d68aa1cad", "timestamp": "2025-08-03T14:17:20.161685", "success": true, "processing_time_seconds": 1002.063, "cache_hits": 0, "response_data": {"answers_count": 5, "answers_preview": ["The ideal spark plug gap recommended is 0.8-0.9 mm.", "Yes, according to the provided context, this comes with tubeless tyres."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "req_1754229751370", "timestamp": "2025-08-03T14:32:16.937517", "success": true, "processing_time_seconds": 1785.567, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["According to Article 1 of the Constitution, the official name of India is \"India, that is Bharat.\"", "Article 14 guarantees equality before the law and equal protection of the laws to all persons."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "c5ff9716-10d3-47e3-8494-b0681e60011d", "timestamp": "2025-08-03T14:32:16.937997", "success": true, "processing_time_seconds": 1785.568, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["According to Article 1 of the Constitution, the official name of India is \"India, that is Bharat.\"", "Article 14 guarantees equality before the law and equal protection of the laws to all persons."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "req_1754229829126", "timestamp": "2025-08-03T14:42:28.994079", "success": true, "processing_time_seconds": 2319.868, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The specific legal classification of car theft is not provided in the context above.", "The provided context does not specify whether arrest without a warrant is legal. It only states that..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "b9d192d3-82c7-4d7e-aed0-d1e17270faed", "timestamp": "2025-08-03T14:42:28.994426", "success": true, "processing_time_seconds": 2319.868, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The specific legal classification of car theft is not provided in the context above.", "The provided context does not specify whether arrest without a warrant is legal. It only states that..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
.0{"event_type": "response", "request_id": "req_1754229904679", "timestamp": "2025-08-03T14:56:11.799082", "success": true, "processing_time_seconds": 3067.119, "cache_hits": 0, "response_data": {"answers_count": 12, "answers_preview": ["Newton defines 'quantity of motion' as the measure arising from the velocity and quantity of matter ...", "The specific three laws of motion as stated by Newton are not directly listed in the provided contex..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "49a054ee-ded8-4001-83de-a8cb05ac6708", "timestamp": "2025-08-03T14:56:11.800820", "success": true, "processing_time_seconds": 3067.121, "cache_hits": 0, "response_data": {"answers_count": 12, "answers_preview": ["Newton defines 'quantity of motion' as the measure arising from the velocity and quantity of matter ...", "The specific three laws of motion as stated by Newton are not directly listed in the provided contex..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "cc8fa5af-49de-4dcf-926a-cc36cc8f7ab9", "timestamp": "2025-08-03T15:43:04.714310", "client_ip": "127.0.0.1", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754235784714", "timestamp": "2025-08-03T15:43:04.714685", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754235784714", "timestamp": "2025-08-03T15:43:47.548226", "success": true, "processing_time_seconds": 42.834, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "cc8fa5af-49de-4dcf-926a-cc36cc8f7ab9", "timestamp": "2025-08-03T15:43:47.548543", "success": true, "processing_time_seconds": 42.834, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "d1be4b37-ccbf-4954-aa85-d840b86f9abd", "timestamp": "2025-08-03T15:44:53.704699", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754235893705", "timestamp": "2025-08-03T15:44:53.705070", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 2, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754235893705", "timestamp": "2025-08-03T15:45:02.255023", "success": true, "processing_time_seconds": 8.55, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "d1be4b37-ccbf-4954-aa85-d840b86f9abd", "timestamp": "2025-08-03T15:45:02.255348", "success": true, "processing_time_seconds": 8.551, "cache_hits": 0, "response_data": {"answers_count": 2, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "e06f9b63-b83c-4e68-82ee-dbc42cfb1f6d", "timestamp": "2025-08-03T15:45:57.951643", "client_ip": "*************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754235957952", "timestamp": "2025-08-03T15:45:57.952058", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754235957952", "timestamp": "2025-08-03T15:54:04.432721", "success": true, "processing_time_seconds": 486.481, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "e06f9b63-b83c-4e68-82ee-dbc42cfb1f6d", "timestamp": "2025-08-03T15:54:04.433164", "success": true, "processing_time_seconds": 486.481, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy shall be thirt...", "Expenses related to the treatment of a pre-existing disease and its direct complications are exclude..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "5faf6771-7f84-4255-9774-bddecb4fa570", "timestamp": "2025-08-04T06:58:41.612170", "client_ip": "***********", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754290721612", "timestamp": "2025-08-04T06:58:41.612635", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754290721612", "timestamp": "2025-08-04T07:03:52.222056", "success": true, "processing_time_seconds": 310.609, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy shall b...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "5faf6771-7f84-4255-9774-bddecb4fa570", "timestamp": "2025-08-04T07:03:52.222401", "success": true, "processing_time_seconds": 310.61, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy shall b...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "415f9b0e-a489-4cd8-80d6-6502dfd06f62", "timestamp": "2025-08-04T07:16:41.747939", "client_ip": "***********", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754291801748", "timestamp": "2025-08-04T07:16:41.748366", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754291801748", "timestamp": "2025-08-04T07:17:54.003819", "success": true, "processing_time_seconds": 72.255, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "415f9b0e-a489-4cd8-80d6-6502dfd06f62", "timestamp": "2025-08-04T07:17:54.004149", "success": true, "processing_time_seconds": 72.256, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "edc4a2e2-fb6d-4509-b019-fb420bb97027", "timestamp": "2025-08-04T07:21:02.289037", "client_ip": "***********", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754292062289", "timestamp": "2025-08-04T07:21:02.289599", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754292062289", "timestamp": "2025-08-04T07:22:29.124156", "success": true, "processing_time_seconds": 86.835, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "edc4a2e2-fb6d-4509-b019-fb420bb97027", "timestamp": "2025-08-04T07:22:29.124552", "success": true, "processing_time_seconds": 86.835, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 12, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 8, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 8, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 8, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 2, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 12, "type": "ThreadPoolExecutor"}}, "configuration": {"system": {"cpu_cores_physical": 4, "cpu_cores_logical": 4, "memory_gb": 23.4}, "workers": {"document_processing": 12, "ocr_processing": 8, "embedding": 8, "vector_search": 2, "llm_api": 12, "pipeline": 4, "chunk_processing": 8}, "limits": {"max_workers_per_task": 12, "min_workers_per_task": 1, "max_memory_usage_percent": 80.0, "worker_memory_limit_mb": 1024}, "performance": {"chunk_size": 100, "timeout_seconds": 300}}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "6e29d905-f36e-422c-8f9c-5402e308967a", "timestamp": "2025-08-04T13:45:56.762016", "client_ip": "************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754315156762", "timestamp": "2025-08-04T13:45:56.762416", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754315156762", "timestamp": "2025-08-04T13:49:49.631817", "success": true, "processing_time_seconds": 232.869, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for coverage of pre-existing diseases (PED) is thirty-six (36) months of continuo..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "error", "request_id": "6e29d905-f36e-422c-8f9c-5402e308967a", "timestamp": "2025-08-04T13:49:49.632185", "error_type": "AttributeError", "error_message": "'MultiprocessingConfig' object has no attribute 'get_config_dict'", "processing_time_seconds": 232.87, "context": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "client_ip": "************", "user_agent": "PostmanRuntime/7.45.0"}, "stack_trace": null}
{"event_type": "request", "request_id": "90677dae-8a11-4436-9b70-547f60143b04", "timestamp": "2025-08-04T13:52:00.988574", "client_ip": "************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754315520988", "timestamp": "2025-08-04T13:52:00.988983", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754315520988", "timestamp": "2025-08-04T13:56:17.821571", "success": true, "processing_time_seconds": 256.833, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for coverage of pre-existing diseases (PED) is thirty-six (36) months of continuo..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "90677dae-8a11-4436-9b70-547f60143b04", "timestamp": "2025-08-04T13:56:17.821960", "success": true, "processing_time_seconds": 256.833, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for coverage of pre-existing diseases (PED) is thirty-six (36) months of continuo..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 2, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 4, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 4, "type": "ThreadPoolExecutor"}}, "configuration": {"max_workers": 8, "thread_pool_size": 16, "llm_workers": 4, "embedding_workers": 8, "document_workers": 2, "ocr_workers": 4, "chunk_size": 100, "timeout_seconds": 300, "enable_process_pools": true, "enable_thread_pools": true}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "10a4bb3e-165b-41df-9972-c82ad68d2b02", "timestamp": "2025-08-04T14:23:32.679807", "client_ip": "************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754317412680", "timestamp": "2025-08-04T14:23:32.680257", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754317412680", "timestamp": "2025-08-04T14:24:27.521009", "success": true, "processing_time_seconds": 54.841, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["Error processing questions. Please try again.", "Error processing questions. Please try again."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "10a4bb3e-165b-41df-9972-c82ad68d2b02", "timestamp": "2025-08-04T14:24:27.521381", "success": true, "processing_time_seconds": 54.842, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["Error processing questions. Please try again.", "Error processing questions. Please try again."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 2, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 4, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 4, "type": "ThreadPoolExecutor"}}, "configuration": {"max_workers": 8, "thread_pool_size": 16, "llm_workers": 4, "embedding_workers": 8, "document_workers": 2, "ocr_workers": 4, "chunk_size": 100, "timeout_seconds": 300, "enable_process_pools": true, "enable_thread_pools": true}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "17ea45b0-c499-41ee-b367-dc3383012097", "timestamp": "2025-08-04T14:41:22.622185", "client_ip": "************", "user_agent": "PostmanRuntime/7.45.0", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754318482622", "timestamp": "2025-08-04T14:41:22.622651", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754318482622", "timestamp": "2025-08-04T14:42:48.594167", "success": true, "processing_time_seconds": 85.972, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "17ea45b0-c499-41ee-b367-dc3383012097", "timestamp": "2025-08-04T14:42:48.594501", "success": true, "processing_time_seconds": 85.972, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for premium payment under the National Parivar Mediclaim Plus Policy is thirty days...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 2, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 4, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 4, "type": "ThreadPoolExecutor"}}, "configuration": {"max_workers": 8, "thread_pool_size": 16, "llm_workers": 4, "embedding_workers": 8, "document_workers": 2, "ocr_workers": 4, "chunk_size": 20, "timeout_seconds": 300, "enable_process_pools": true, "enable_thread_pools": true}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "c9bc017e-1f19-48d6-a7d0-75b80e5d3d0c", "timestamp": "2025-08-04T15:00:52.140331", "client_ip": "**************", "user_agent": "Python/3.12 aiohttp/3.12.15", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 3, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": false}}
{"event_type": "request", "request_id": "req_1754319652140", "timestamp": "2025-08-04T15:00:52.140729", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 3, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": false}}
{"event_type": "response", "request_id": "req_1754319652140", "timestamp": "2025-08-04T15:01:05.953778", "success": true, "processing_time_seconds": 13.813, "cache_hits": 0, "response_data": {"answers_count": 3, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "c9bc017e-1f19-48d6-a7d0-75b80e5d3d0c", "timestamp": "2025-08-04T15:01:05.954075", "success": true, "processing_time_seconds": 13.814, "cache_hits": 0, "response_data": {"answers_count": 3, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 2, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 4, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 4, "type": "ThreadPoolExecutor"}}, "configuration": {"max_workers": 8, "thread_pool_size": 16, "llm_workers": 4, "embedding_workers": 8, "document_workers": 2, "ocr_workers": 4, "chunk_size": 20, "timeout_seconds": 300, "enable_process_pools": true, "enable_thread_pools": true}}, "cache_efficiency": false}}
{"event_type": "request", "request_id": "e8d5b5c2-80dd-458e-90fa-e5f4decd326d", "timestamp": "2025-08-04T15:01:05.959544", "client_ip": "**************", "user_agent": "Python/3.12 aiohttp/3.12.15", "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "request", "request_id": "req_1754319665959", "timestamp": "2025-08-04T15:01:05.959874", "client_ip": null, "user_agent": null, "request_data": {"documents_url": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "questions_count": 10, "questions": ["What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?", "What is the waiting period for pre-existing diseases (PED) to be covered?", "Does this policy cover maternity expenses, and what are the conditions?"], "questions_truncated": true}}
{"event_type": "response", "request_id": "req_1754319665959", "timestamp": "2025-08-04T15:01:37.596070", "success": true, "processing_time_seconds": 31.636, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {}, "cache_efficiency": false}}
{"event_type": "response", "request_id": "e8d5b5c2-80dd-458e-90fa-e5f4decd326d", "timestamp": "2025-08-04T15:01:37.596404", "success": true, "processing_time_seconds": 31.637, "cache_hits": 0, "response_data": {"answers_count": 10, "answers_preview": ["The grace period for payment of the premium under the National Parivar Mediclaim Plus Policy is thir...", "The waiting period for pre-existing diseases (PED) to be covered is thirty-six (36) months of contin..."]}, "performance_metrics": {"worker_stats": {"initialized": true, "process_pools": {"document_processing": {"max_workers": 2, "type": "ProcessPoolExecutor"}, "ocr_processing": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "chunking": {"max_workers": 4, "type": "ProcessPoolExecutor"}, "reranking": {"max_workers": 4, "type": "ProcessPoolExecutor"}}, "thread_pools": {"embedding": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "vector_search": {"max_workers": 4, "type": "ThreadPoolExecutor"}, "llm_api": {"max_workers": 4, "type": "ThreadPoolExecutor"}}, "configuration": {"max_workers": 8, "thread_pool_size": 16, "llm_workers": 4, "embedding_workers": 8, "document_workers": 2, "ocr_workers": 4, "chunk_size": 20, "timeout_seconds": 300, "enable_process_pools": true, "enable_thread_pools": true}}, "cache_efficiency": false}}
