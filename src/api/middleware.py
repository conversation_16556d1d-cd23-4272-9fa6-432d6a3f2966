"""API middleware for authentication, logging, and error handling."""

import logging
import time
from typing import Callable
from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.security.utils import get_authorization_scheme_param

from src.config import settings

logger = logging.getLogger(__name__)

security = HTTPBearer()


async def auth_middleware(request: Request, call_next: Callable) -> Response:
    """Authentication middleware to validate bearer tokens."""
    
    # Skip auth for health check, docs, stats endpoints, and webhook info
    public_endpoints = [
        "/health", "/docs", "/redoc", "/openapi.json", 
        "/api/v1/health", "/", "/api/v1/",
        "/api/v1/stats/cache", "/api/v1/stats/workers", "/api/v1/stats/logs",
        "/api/v1/webhook/hackrx"  # GET webhook info endpoint is public
    ]
    if request.url.path in public_endpoints:
        return await call_next(request)
    
    # Extract authorization header
    authorization = request.headers.get("Authorization")
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header missing"
        )
    
    scheme, token = get_authorization_scheme_param(authorization)
    if scheme.lower() != "bearer":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication scheme"
        )
    
    if token != settings.BEARER_TOKEN:
        logger.warning(f"Invalid token attempt: {token[:10]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    return await call_next(request)


async def logging_middleware(request: Request, call_next: Callable) -> Response:
    """Logging middleware to track requests and responses."""
    
    start_time = time.time()
    
    # Identify webhook requests for better logging
    endpoint_type = "WEBHOOK" if "/webhook/" in request.url.path else "API"
    
    # Log request with endpoint type identification
    logger.info(f"[{endpoint_type}] Request: {request.method} {request.url.path}")
    
    # Process request
    response = await call_next(request)
    
    # Calculate processing time
    process_time = time.time() - start_time
    
    # Log response with endpoint type identification
    logger.info(f"[{endpoint_type}] Response: {response.status_code} - {process_time:.3f}s")
    
    # Add processing time header
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


async def error_handling_middleware(request: Request, call_next: Callable) -> Response:
    """Error handling middleware to catch and format exceptions."""
    
    try:
        return await call_next(request)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unhandled exception: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )