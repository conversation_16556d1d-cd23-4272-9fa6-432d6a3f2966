"""API routes for the LLM Query-Retrieval System."""

import logging
import time
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON>earer

from src.services.advanced_query_processor import AdvancedQueryProcessor
from src.models.schemas import QueryRequest, QueryResponse
from src.models.job_models import JobRequest, JobResponse, JobStatusResponse
from src.services.background_job_processor import background_job_processor
from src.config import settings
from src.services.logging_service import request_logger, generate_request_id
from src.services.multiprocessing_manager import mp_manager
from src.services.model_cache import model_cache
from src.services.document_cache import document_cache

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Security
security = HTTPBearer()

# Initialize advanced query processor (singleton pattern)
advanced_processor = AdvancedQueryProcessor()


@router.post("/hackrx/run", response_model=QueryResponse)
async def run_query(
    request: QueryRequest,
    http_request: Request,
    credentials: str = Depends(security)
) -> QueryResponse:
    """
    Main endpoint for processing document queries with comprehensive logging.
    
    This endpoint processes a document from a URL and answers multiple questions
    about its content using the LLM-powered retrieval system.
    """
    # Generate unique request ID
    request_id = generate_request_id()
    start_time = time.time()
    
    try:
        # Extract client information
        client_ip = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        # Log incoming request
        await request_logger.log_request(
            request=request,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        logger.info(f"[{request_id}] Received query request for document: {request.documents}")
        logger.info(f"[{request_id}] Number of questions: {len(request.questions)}")
        
        # Process the request through advanced pipeline
        response = await advanced_processor.process_request(request)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Get cache statistics
        cache_stats = document_cache.get_cache_stats()
        worker_stats = mp_manager.get_pool_stats()
        
        # Log successful response
        await request_logger.log_response(
            response=response,
            request_id=request_id,
            processing_time=processing_time,
            success=True,
            cache_hits=cache_stats.get('hits', 0),
            worker_stats=worker_stats
        )
        
        logger.info(f"[{request_id}] Successfully processed query request in {processing_time:.2f}s")
        return response
        
    except Exception as e:
        # Calculate processing time up to error
        processing_time = time.time() - start_time
        
        # Log error with context
        await request_logger.log_error(
            error=e,
            request_id=request_id,
            context={
                "documents_url": request.documents,
                "questions_count": len(request.questions),
                "client_ip": client_ip,
                "user_agent": user_agent
            },
            processing_time=processing_time
        )
        
        logger.error(f"[{request_id}] Error processing query request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing request: {str(e)}"
        )


@router.post("/webhook/hackrx", response_model=QueryResponse)
async def webhook_hackrx_run(
    request: QueryRequest,
    http_request: Request,
    credentials: str = Depends(security)
) -> QueryResponse:
    """
    Webhook endpoint for processing document queries with identical functionality to /hackrx/run.
    
    This webhook endpoint processes a document from a URL and answers multiple questions
    about its content using the same LLM-powered retrieval system as the main endpoint.
    External systems can integrate with this webhook for automated document processing.
    """
    # Generate unique request ID with webhook identifier
    request_id = generate_request_id()
    start_time = time.time()
    
    try:
        # Extract client information
        client_ip = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        # Log incoming webhook request
        await request_logger.log_request(
            request=request,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        logger.info(f"[WEBHOOK-{request_id}] Received webhook query request for document: {request.documents}")
        logger.info(f"[WEBHOOK-{request_id}] Number of questions: {len(request.questions)}")
        
        # Process the request through the same advanced pipeline
        response = await advanced_processor.process_request(request)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Get cache statistics
        cache_stats = document_cache.get_cache_stats()
        worker_stats = mp_manager.get_pool_stats()
        
        # Log successful webhook response
        await request_logger.log_response(
            response=response,
            request_id=request_id,
            processing_time=processing_time,
            success=True,
            cache_hits=cache_stats.get('hits', 0),
            worker_stats=worker_stats
        )
        
        logger.info(f"[WEBHOOK-{request_id}] Successfully processed webhook query request in {processing_time:.2f}s")
        return response
        
    except Exception as e:
        # Calculate processing time up to error
        processing_time = time.time() - start_time
        
        # Log webhook error with context
        await request_logger.log_error(
            error=e,
            request_id=request_id,
            context={
                "endpoint_type": "webhook",
                "documents_url": request.documents,
                "questions_count": len(request.questions),
                "client_ip": client_ip,
                "user_agent": user_agent
            },
            processing_time=processing_time
        )
        
        logger.error(f"[WEBHOOK-{request_id}] Error processing webhook query request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing webhook request: {str(e)}"
        )


@router.get("/webhook/hackrx")
async def webhook_info() -> Dict[str, Any]:
    """
    Webhook information endpoint providing usage instructions and documentation.
    
    Returns comprehensive information about the webhook endpoint including
    request/response formats, authentication requirements, and integration examples.
    """
    return {
        "name": "LLM Query-Retrieval Webhook",
        "version": "1.0.0",
        "description": "Webhook endpoint for automated document processing and query answering",
        "endpoint": "/api/v1/webhook/hackrx",
        "method": "POST",
        "authentication": {
            "type": "Bearer Token",
            "header": "Authorization: Bearer <your-token>",
            "required": True
        },
        "request_format": {
            "content_type": "application/json",
            "schema": {
                "documents": {
                    "type": "string",
                    "description": "URL to the document blob (PDF, DOCX, etc.)",
                    "required": True,
                    "example": "https://example.com/document.pdf"
                },
                "questions": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of questions to answer about the document",
                    "required": True,
                    "min_items": 1,
                    "example": [
                        "What is the main topic of this document?",
                        "What are the key findings mentioned?"
                    ]
                }
            }
        },
        "response_format": {
            "content_type": "application/json",
            "schema": {
                "answers": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of answers corresponding to the input questions",
                    "example": [
                        "The main topic is...",
                        "The key findings are..."
                    ]
                }
            }
        },
        "supported_document_types": [
            "PDF (.pdf)",
            "Microsoft Word (.docx)",
            "Email documents"
        ],
        "features": [
            "Advanced multi-query processing",
            "Semantic search with FAISS vector storage",
            "GPT-4 powered answer generation",
            "Document caching for improved performance",
            "Comprehensive error handling and logging"
        ],
        "error_codes": {
            "400": "Bad Request - Invalid request format",
            "401": "Unauthorized - Missing or invalid Bearer token",
            "422": "Unprocessable Entity - Request validation failed",
            "500": "Internal Server Error - Processing error"
        },
        "example_request": {
            "method": "POST",
            "url": "/api/v1/webhook/hackrx",
            "headers": {
                "Authorization": "Bearer your-token-here",
                "Content-Type": "application/json"
            },
            "body": {
                "documents": "https://example.com/sample-document.pdf",
                "questions": [
                    "What is the document about?",
                    "What are the main conclusions?"
                ]
            }
        },
        "example_response": {
            "answers": [
                "This document discusses...",
                "The main conclusions are..."
            ]
        },
        "integration_notes": [
            "Webhook uses identical processing pipeline as /api/v1/hackrx/run",
            "Responses are returned synchronously",
            "Document processing includes OCR for scanned documents",
            "Caching is applied for improved performance on repeated requests",
            "All requests are logged with unique request IDs for tracking"
        ]
    }


@router.post("/jobs", response_model=JobResponse)
async def submit_job(
    request: JobRequest,
    http_request: Request,
    credentials: str = Depends(security)
) -> JobResponse:
    """
    Submit a new job for asynchronous processing with webhook notification.
    
    This endpoint allows clients to submit document processing jobs that will be
    processed asynchronously in the background. When processing completes, a webhook
    notification will be sent to the specified URL.
    """
    request_id = generate_request_id()
    start_time = time.time()
    
    try:
        # Extract client information
        client_ip = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        logger.info(f"[JOB-{request_id}] Received job submission for document: {request.documents}")
        logger.info(f"[JOB-{request_id}] Questions: {len(request.questions)}, Webhook: {request.webhook_url}")
        
        # Submit job for processing
        job = await background_job_processor.submit_job(request)
        
        # Create response
        response = JobResponse(
            job_id=job.job_id,
            status=job.status,
            webhook_url=job.request.webhook_url,
            created_at=job.created_at,
            priority=job.request.priority
        )
        
        processing_time = time.time() - start_time
        logger.info(f"[JOB-{request_id}] Job {job.job_id} submitted successfully in {processing_time:.2f}s")
        
        return response
        
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"[JOB-{request_id}] Error submitting job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error submitting job: {str(e)}"
        )


@router.get("/jobs/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    credentials: str = Depends(security)
) -> JobStatusResponse:
    """
    Get the current status of a job.
    
    Returns detailed information about the job including current status,
    progress, results (if completed), and error information (if failed).
    """
    try:
        logger.info(f"Getting status for job {job_id}")
        
        # Get job from storage
        job = await background_job_processor.get_job_status(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        # Convert to status response
        response = job.to_status_response()
        
        logger.debug(f"Job {job_id} status: {job.status}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job status: {str(e)}"
        )


@router.delete("/jobs/{job_id}")
async def cancel_job(
    job_id: str,
    credentials: str = Depends(security)
) -> Dict[str, str]:
    """
    Cancel a job.
    
    Cancels a job if it's still queued or processing. Completed or failed
    jobs cannot be cancelled.
    """
    try:
        logger.info(f"Cancelling job {job_id}")
        
        success = await background_job_processor.cancel_job(job_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Job {job_id} cannot be cancelled (not found or already terminal)"
            )
        
        return {"message": f"Job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling job {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cancelling job: {str(e)}"
        )


@router.get("/jobs")
async def list_jobs(
    status_filter: str = None,
    limit: int = 50,
    credentials: str = Depends(security)
) -> Dict[str, Any]:
    """
    List jobs with optional status filtering.
    
    Returns a list of jobs, optionally filtered by status.
    """
    try:
        from src.models.job_models import JobStatus
        from src.services.job_storage import job_storage
        
        # Validate status filter
        if status_filter:
            try:
                status_enum = JobStatus(status_filter.lower())
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status filter: {status_filter}"
                )
            
            jobs = await job_storage.get_jobs_by_status(status_enum, limit)
        else:
            # Get jobs from all statuses
            all_jobs = []
            for job_status in JobStatus:
                jobs_for_status = await job_storage.get_jobs_by_status(job_status, limit // len(JobStatus))
                all_jobs.extend(jobs_for_status)
            
            # Sort by creation time (newest first)
            jobs = sorted(all_jobs, key=lambda j: j.created_at, reverse=True)[:limit]
        
        # Convert to response format
        job_responses = [job.to_status_response() for job in jobs]
        
        return {
            "jobs": job_responses,
            "count": len(job_responses),
            "status_filter": status_filter
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing jobs: {str(e)}"
        )


@router.get("/stats/jobs")
async def get_job_stats(
    credentials: str = Depends(security)
) -> Dict[str, Any]:
    """Get job processing statistics."""
    try:
        stats = await background_job_processor.get_processing_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting job stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job stats: {str(e)}"
        )


@router.get("/stats/webhooks")
async def get_webhook_stats(
    credentials: str = Depends(security)
) -> Dict[str, Any]:
    """Get webhook delivery statistics."""
    try:
        from src.services.webhook_delivery_service import webhook_delivery_service
        stats = await webhook_delivery_service.get_delivery_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting webhook stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting webhook stats: {str(e)}"
        )


@router.post("/admin/jobs/cleanup")
async def cleanup_expired_jobs(
    credentials: str = Depends(security)
) -> Dict[str, Any]:
    """Clean up expired jobs (admin endpoint)."""
    try:
        from src.services.job_storage import job_storage
        cleaned_count = await job_storage.cleanup_expired_jobs()
        return {
            "message": f"Cleaned up {cleaned_count} expired jobs",
            "cleaned_count": cleaned_count
        }
    except Exception as e:
        logger.error(f"Error cleaning up jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cleaning up jobs: {str(e)}"
        )


@router.get("/monitor/system")
async def system_monitor(
    credentials: str = Depends(security)
) -> Dict[str, Any]:
    """Comprehensive system monitoring endpoint."""
    try:
        # Get all system stats
        job_stats = await background_job_processor.get_processing_stats()
        
        from src.services.webhook_delivery_service import webhook_delivery_service
        webhook_stats = await webhook_delivery_service.get_delivery_stats()
        
        from src.services.job_storage import job_storage
        storage_health = await job_storage.health_check()
        
        cache_stats = {
            "document_cache": document_cache.get_cache_stats(),
            "model_cache": model_cache.get_model_info()
        }
        
        worker_stats = mp_manager.get_pool_stats()
        
        return {
            "timestamp": time.time(),
            "system_status": "operational",
            "job_processing": job_stats,
            "webhook_delivery": webhook_stats,
            "storage": storage_health,
            "caching": cache_stats,
            "workers": worker_stats
        }
        
    except Exception as e:
        logger.error(f"Error in system monitor: {e}")
        return {
            "timestamp": time.time(),
            "system_status": "error",
            "error": str(e)
        }


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Enhanced health check endpoint to verify system status including new services.
    """
    try:
        # Get basic health status from advanced processor
        health_status = await advanced_processor.health_check()
        
        # Add enhanced service status
        health_status.update({
            "multiprocessing": {
                "initialized": mp_manager._initialized,
                "worker_pools": len(mp_manager.process_pools) + len(mp_manager.thread_pools)
            },
            "model_cache": {
                "initialized": model_cache.is_initialized(),
                "models_loaded": len(model_cache.models)
            },
            "document_cache": {
                "size": len(document_cache.cache),
                "hit_rate": document_cache.get_cache_stats().get("hit_rate_percent", 0)
            },
            "logging": {
                "active": True,
                "log_file": str(request_logger.log_file_path)
            },
            "webhook": {
                "enabled": True,
                "endpoint": "/api/v1/webhook/hackrx",
                "info_endpoint": "/api/v1/webhook/hackrx (GET)",
                "authentication_required": True,
                "uses_same_pipeline": True
            },
            "job_processor": {
                "enabled": True,
                "endpoints": {
                    "submit": "/api/v1/jobs",
                    "status": "/api/v1/jobs/{job_id}",
                    "cancel": "/api/v1/jobs/{job_id} (DELETE)",
                    "list": "/api/v1/jobs"
                },
                "background_processing": True
            }
        })
        
        # Get job processor health
        try:
            job_processor_health = await background_job_processor.health_check()
            health_status["job_processor"] = job_processor_health
        except Exception as e:
            health_status["job_processor"] = {"status": "unhealthy", "error": str(e)}
        
        # Determine overall health
        all_healthy = (
            health_status["status"] == "healthy" and
            health_status["multiprocessing"]["initialized"] and
            health_status["model_cache"]["initialized"] and
            health_status["job_processor"]["status"] in ["healthy", "degraded"]
        )
        
        if all_healthy:
            health_status["enhanced_features"] = "operational"
            return health_status
        else:
            health_status["enhanced_features"] = "degraded"
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=health_status
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Health check failed: {str(e)}"
        )


@router.get("/stats/cache")
async def get_cache_stats() -> Dict[str, Any]:
    """Get document cache statistics."""
    try:
        return {
            "document_cache": document_cache.get_cache_stats(),
            "model_cache": model_cache.get_model_info()
        }
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting cache stats: {str(e)}"
        )


@router.get("/stats/workers")
async def get_worker_stats() -> Dict[str, Any]:
    """Get multiprocessing worker pool statistics."""
    try:
        return mp_manager.get_pool_stats()
    except Exception as e:
        logger.error(f"Error getting worker stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting worker stats: {str(e)}"
        )


@router.get("/stats/logs")
async def get_log_stats() -> Dict[str, Any]:
    """Get logging statistics."""
    try:
        return await request_logger.get_log_stats()
    except Exception as e:
        logger.error(f"Error getting log stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting log stats: {str(e)}"
        )


@router.post("/admin/cache/clear")
async def clear_caches() -> Dict[str, str]:
    """Clear all caches (admin endpoint)."""
    try:
        document_cache.clear_cache()
        return {"message": "All caches cleared successfully"}
    except Exception as e:
        logger.error(f"Error clearing caches: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error clearing caches: {str(e)}"
        )


@router.get("/stats/pipeline")
async def get_pipeline_stats() -> Dict[str, Any]:
    """Get advanced pipeline statistics and performance metrics."""
    try:
        return advanced_processor.get_pipeline_status()
    except Exception as e:
        logger.error(f"Error getting pipeline stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting pipeline stats: {str(e)}"
        )


@router.post("/admin/pipeline/configure")
async def configure_pipeline(
    enable_gap_analysis: bool = True,
    enable_retry: bool = True,
    max_retry_iterations: int = 1
) -> Dict[str, str]:
    """Configure advanced pipeline behavior (admin endpoint)."""
    try:
        advanced_processor.configure_pipeline(
            enable_gap_analysis=enable_gap_analysis,
            enable_retry=enable_retry,
            max_retry_iterations=max_retry_iterations
        )
        return {
            "message": "Pipeline configuration updated successfully",
            "gap_analysis": str(enable_gap_analysis),
            "retry": str(enable_retry),
            "max_retries": str(max_retry_iterations)
        }
    except Exception as e:
        logger.error(f"Error configuring pipeline: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error configuring pipeline: {str(e)}"
        )


@router.post("/admin/benchmark")
async def benchmark_pipeline(
    test_questions: List[str],
    document_url: str,
    iterations: int = 1
) -> Dict[str, Any]:
    """Benchmark the advanced pipeline performance (admin endpoint)."""
    try:
        if iterations > 10:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 10 iterations allowed for benchmarking"
            )
        
        benchmark_results = await advanced_processor.benchmark_pipeline(
            test_questions=test_questions,
            document_url=document_url,
            iterations=iterations
        )
        return benchmark_results
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running benchmark: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error running benchmark: {str(e)}"
        )


@router.get("/")
async def root() -> Dict[str, Any]:
    """
    Root endpoint with comprehensive API information.
    """
    return {
        "name": "Advanced LLM-Powered Intelligent Query-Retrieval System",
        "version": "2.0.0",
        "description": "Self-correcting, multi-query RAG system for processing documents and answering contextual queries",
        "features": [
            "Advanced multi-query architecture",
            "Query decomposition with GPT-4.1",
            "Parallel sub-query processing",
            "Knowledge gap analysis",
            "Conditional retry with gap filling",
            "Context aggregation and deduplication",
            "Azure Cohere rerank-v3-5 with dynamic thresholding",
            "Multiprocessing optimization",
            "Application-level model caching",
            "LRU document caching",
            "Comprehensive request logging"
        ],
        "pipeline_flow": [
            "1. Query Decomposition",
            "2. Parallel Sub-Query Processing", 
            "3. Gap Analysis",
            "4. Conditional Retry (max 1 iteration)",
            "5. Context Aggregation",
            "6. Answer Synthesis"
        ],
        "endpoints": {
            "main": "/api/v1/hackrx/run",
            "webhook": "/api/v1/webhook/hackrx",
            "webhook_info": "/api/v1/webhook/hackrx (GET)",
            "job_submit": "/api/v1/jobs",
            "job_status": "/api/v1/jobs/{job_id}",
            "job_cancel": "/api/v1/jobs/{job_id} (DELETE)",
            "job_list": "/api/v1/jobs",
            "health": "/api/v1/health",
            "cache_stats": "/api/v1/stats/cache",
            "worker_stats": "/api/v1/stats/workers",
            "log_stats": "/api/v1/stats/logs",
            "pipeline_stats": "/api/v1/stats/pipeline",
            "configure_pipeline": "/api/v1/admin/pipeline/configure",
            "benchmark": "/api/v1/admin/benchmark",
            "clear_caches": "/api/v1/admin/cache/clear",
            "docs": "/docs"
        }
    }