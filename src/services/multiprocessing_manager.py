"""Multiprocessing manager for coordinating worker pools across services."""

import asyncio
import logging
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from typing import Dict, Any, List, Callable, Optional
import multiprocessing as mp
from functools import partial

from multiprocessing_config import MultiprocessingConfig

logger = logging.getLogger(__name__)


class MultiprocessingManager:
    """Manages multiprocessing worker pools for different service types."""
    
    def __init__(self):
        self.config = MultiprocessingConfig()
        self.process_pools: Dict[str, ProcessPoolExecutor] = {}
        self.thread_pools: Dict[str, ThreadPoolExecutor] = {}
        self._initialized = False
        
    async def initialize(self):
        """Initialize all worker pools based on configuration."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing multiprocessing worker pools...")
            
            # Create process pools for CPU-intensive tasks
            self.process_pools = {
                'document_processing': ProcessPoolExecutor(
                    max_workers=self.config.get_optimal_workers('document_processing')
                ),
                'ocr_processing': ProcessPoolExecutor(
                    max_workers=self.config.get_optimal_workers('ocr')
                ),
                'chunking': ProcessPoolExecutor(
                    max_workers=self.config.get_optimal_workers('chunking')
                ),
                'reranking': ProcessPoolExecutor(
                    max_workers=self.config.get_optimal_workers('chunking')  # CPU intensive
                )
            }
            
            # Create thread pools for I/O-intensive tasks
            self.thread_pools = {
                'embedding': ThreadPoolExecutor(
                    max_workers=self.config.get_optimal_workers('embedding')
                ),
                'vector_search': ThreadPoolExecutor(
                    max_workers=self.config.get_optimal_workers('vector_search')
                ),
                'llm_api': ThreadPoolExecutor(
                    max_workers=self.config.get_optimal_workers('llm_api')
                )
            }
            
            self._initialized = True
            
            # Log configuration
            logger.info("Worker pools initialized:")
            for pool_name, pool in self.process_pools.items():
                logger.info(f"  Process pool '{pool_name}': {pool._max_workers} workers")
            for pool_name, pool in self.thread_pools.items():
                logger.info(f"  Thread pool '{pool_name}': {pool._max_workers} workers")
                
        except Exception as e:
            logger.error(f"Failed to initialize multiprocessing manager: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup all worker pools."""
        logger.info("Cleaning up multiprocessing worker pools...")
        
        # Shutdown process pools
        for pool_name, pool in self.process_pools.items():
            try:
                pool.shutdown(wait=True)
                logger.info(f"Process pool '{pool_name}' shutdown complete")
            except Exception as e:
                logger.error(f"Error shutting down process pool '{pool_name}': {e}")
        
        # Shutdown thread pools
        for pool_name, pool in self.thread_pools.items():
            try:
                pool.shutdown(wait=True)
                logger.info(f"Thread pool '{pool_name}' shutdown complete")
            except Exception as e:
                logger.error(f"Error shutting down thread pool '{pool_name}': {e}")
        
        self.process_pools.clear()
        self.thread_pools.clear()
        self._initialized = False
    
    async def execute_in_process_pool(
        self, 
        pool_name: str, 
        func: Callable, 
        *args, 
        **kwargs
    ) -> Any:
        """Execute a function in a process pool."""
        if not self._initialized:
            await self.initialize()
            
        if pool_name not in self.process_pools:
            raise ValueError(f"Process pool '{pool_name}' not found")
        
        pool = self.process_pools[pool_name]
        loop = asyncio.get_event_loop()
        
        try:
            # Create partial function with arguments
            partial_func = partial(func, *args, **kwargs)
            result = await loop.run_in_executor(pool, partial_func)
            return result
        except Exception as e:
            logger.error(f"Error executing in process pool '{pool_name}': {e}")
            raise
    
    async def execute_in_thread_pool(
        self, 
        pool_name: str, 
        func: Callable, 
        *args, 
        **kwargs
    ) -> Any:
        """Execute a function in a thread pool."""
        if not self._initialized:
            await self.initialize()
            
        if pool_name not in self.thread_pools:
            raise ValueError(f"Thread pool '{pool_name}' not found")
        
        pool = self.thread_pools[pool_name]
        loop = asyncio.get_event_loop()
        
        try:
            # Create partial function with arguments
            partial_func = partial(func, *args, **kwargs)
            result = await loop.run_in_executor(pool, partial_func)
            return result
        except Exception as e:
            logger.error(f"Error executing in thread pool '{pool_name}': {e}")
            raise
    
    async def execute_batch_in_process_pool(
        self, 
        pool_name: str, 
        func: Callable, 
        items: List[Any],
        batch_size: Optional[int] = None
    ) -> List[Any]:
        """Execute a batch of items in parallel using a process pool."""
        if not self._initialized:
            await self.initialize()
            
        if pool_name not in self.process_pools:
            raise ValueError(f"Process pool '{pool_name}' not found")
        
        pool = self.process_pools[pool_name]
        loop = asyncio.get_event_loop()
        
        # Use configured batch size if not provided
        if batch_size is None:
            batch_size = self.config.get_chunk_size(pool_name)
        
        try:
            # Process items in batches to avoid overwhelming the pool
            results = []
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                
                # Submit all tasks in the batch
                futures = []
                for item in batch:
                    if isinstance(item, (list, tuple)):
                        # Item is arguments tuple/list
                        partial_func = partial(func, *item)
                    else:
                        # Item is single argument
                        partial_func = partial(func, item)
                    
                    future = loop.run_in_executor(pool, partial_func)
                    futures.append(future)
                
                # Wait for batch completion
                batch_results = await asyncio.gather(*futures)
                results.extend(batch_results)
            
            return results
            
        except Exception as e:
            logger.error(f"Error executing batch in process pool '{pool_name}': {e}")
            raise
    
    async def execute_batch_in_thread_pool(
        self, 
        pool_name: str, 
        func: Callable, 
        items: List[Any],
        batch_size: Optional[int] = None
    ) -> List[Any]:
        """Execute a batch of items in parallel using a thread pool."""
        if not self._initialized:
            await self.initialize()
            
        if pool_name not in self.thread_pools:
            raise ValueError(f"Thread pool '{pool_name}' not found")
        
        pool = self.thread_pools[pool_name]
        loop = asyncio.get_event_loop()
        
        # Use configured batch size if not provided
        if batch_size is None:
            batch_size = self.config.get_chunk_size(pool_name)
        
        try:
            # Process items in batches
            results = []
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                
                # Submit all tasks in the batch
                futures = []
                for item in batch:
                    if isinstance(item, (list, tuple)):
                        # Item is arguments tuple/list
                        partial_func = partial(func, *item)
                    else:
                        # Item is single argument
                        partial_func = partial(func, item)
                    
                    future = loop.run_in_executor(pool, partial_func)
                    futures.append(future)
                
                # Wait for batch completion
                batch_results = await asyncio.gather(*futures)
                results.extend(batch_results)
            
            return results
            
        except Exception as e:
            logger.error(f"Error executing batch in thread pool '{pool_name}': {e}")
            raise
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get statistics about worker pools."""
        stats = {
            'initialized': self._initialized,
            'process_pools': {},
            'thread_pools': {},
            'configuration': self.config.get_config_dict()
        }
        
        for pool_name, pool in self.process_pools.items():
            stats['process_pools'][pool_name] = {
                'max_workers': pool._max_workers,
                'type': 'ProcessPoolExecutor'
            }
        
        for pool_name, pool in self.thread_pools.items():
            stats['thread_pools'][pool_name] = {
                'max_workers': pool._max_workers,
                'type': 'ThreadPoolExecutor'
            }
        
        return stats


# Global multiprocessing manager instance
mp_manager = MultiprocessingManager()


# Helper functions for common multiprocessing patterns
async def process_documents_parallel(func: Callable, documents: List[Any]) -> List[Any]:
    """Helper function for parallel document processing."""
    return await mp_manager.execute_batch_in_process_pool(
        'document_processing', func, documents
    )


async def process_ocr_parallel(func: Callable, items: List[Any]) -> List[Any]:
    """Helper function for parallel OCR processing."""
    return await mp_manager.execute_batch_in_process_pool(
        'ocr_processing', func, items
    )


async def process_embeddings_parallel(func: Callable, items: List[Any]) -> List[Any]:
    """Helper function for parallel embedding generation."""
    return await mp_manager.execute_batch_in_thread_pool(
        'embedding', func, items
    )


async def process_llm_parallel(func: Callable, items: List[Any]) -> List[Any]:
    """Helper function for parallel LLM API calls."""
    return await mp_manager.execute_batch_in_thread_pool(
        'llm_api', func, items
    )


async def process_chunking_parallel(func: Callable, items: List[Any]) -> List[Any]:
    """Helper function for parallel text chunking."""
    return await mp_manager.execute_batch_in_process_pool(
        'chunking', func, items
    )


async def process_reranking_parallel(func: Callable, items: List[Any]) -> List[Any]:
    """Helper function for parallel reranking operations."""
    return await mp_manager.execute_batch_in_process_pool(
        'reranking', func, items
    )