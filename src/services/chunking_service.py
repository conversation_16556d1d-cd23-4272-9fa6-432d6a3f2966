"""Semantic chunking service for intelligent document segmentation."""

import logging
import re
from typing import List, Dict, Any
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

from src.config import settings
from src.models.schemas import DocumentChunk
from src.services.multiprocessing_manager import mp_manager, process_chunking_parallel

logger = logging.getLogger(__name__)


class SemanticChunker:
    """Service for semantic chunking of documents with multiprocessing support."""
    
    def __init__(self):
        self.model = None
        self._load_sentence_transformer()
        
    def _load_sentence_transformer(self):
        """Load the sentence transformer model (fallback if cache not available)."""
        try:
            # Try to get from cache first
            from src.services.model_cache import get_sentence_transformer
            
            cached_model = get_sentence_transformer()
            if cached_model is not None:
                self.model = cached_model
                logger.info("Using cached sentence transformer model")
            else:
                # Fallback to loading directly
                self.model = SentenceTransformer(settings.SENTENCE_TRANSFORMER_MODEL)
                logger.info(f"Loaded sentence transformer model directly: {settings.SENTENCE_TRANSFORMER_MODEL}")
        except Exception as e:
            logger.error(f"Failed to load sentence transformer model: {e}")
            raise
    
    async def semantic_chunk(self, text: str, source_url: str = "") -> List[DocumentChunk]:
        """
        Perform semantic chunking on text.
        
        Args:
            text: Input text to chunk
            source_url: Source URL for metadata
            
        Returns:
            List of semantically coherent document chunks
        """
        try:
            # Split text into sentences
            sentences = self._split_into_sentences(text)
            
            if len(sentences) <= 1:
                # If only one sentence, return as single chunk
                return [DocumentChunk(
                    id=f"{hash(source_url)}_0",
                    content=text.strip(),
                    metadata={
                        "source": source_url,
                        "chunk_index": 0,
                        "total_chunks": 1,
                        "chunking_method": "semantic"
                    }
                )]
            
            # Calculate semantic similarities
            similarities = await self._calculate_semantic_similarity(sentences)
            
            # Group sentences by semantic similarity
            chunks = self._group_by_semantic_similarity(sentences, similarities, source_url)
            
            logger.info(f"Created {len(chunks)} semantic chunks from {len(sentences)} sentences")
            return chunks
            
        except Exception as e:
            logger.error(f"Error in semantic chunking: {e}")
            # Fallback to simple chunking
            return self._fallback_chunk(text, source_url)
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences using regex patterns."""
        # Enhanced sentence splitting pattern
        sentence_pattern = r'(?<=[.!?])\s+(?=[A-Z])'
        sentences = re.split(sentence_pattern, text)
        
        # Clean and filter sentences
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # Filter out very short sentences
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    async def _calculate_semantic_similarity(self, sentences: List[str]) -> List[float]:
        """Calculate semantic similarity between consecutive sentences."""
        if len(sentences) <= 1:
            return [1.0]
        
        try:
            # Generate embeddings for all sentences
            embeddings = self.model.encode(sentences)
            
            # Calculate cosine similarities between consecutive sentences
            similarities = []
            for i in range(len(sentences) - 1):
                similarity = cosine_similarity(
                    embeddings[i].reshape(1, -1),
                    embeddings[i + 1].reshape(1, -1)
                )[0][0]
                similarities.append(similarity)
            
            return similarities
            
        except Exception as e:
            logger.error(f"Error calculating semantic similarity: {e}")
            # Return default similarities
            return [0.5] * (len(sentences) - 1)
    
    def _group_by_semantic_similarity(
        self, 
        sentences: List[str], 
        similarities: List[float], 
        source_url: str
    ) -> List[DocumentChunk]:
        """Group sentences into chunks based on semantic similarity."""
        chunks = []
        current_chunk_sentences = [sentences[0]]
        
        for i, similarity in enumerate(similarities):
            if similarity >= settings.SEMANTIC_SIMILARITY_THRESHOLD:
                # High similarity - add to current chunk
                current_chunk_sentences.append(sentences[i + 1])
            else:
                # Low similarity - start new chunk
                if current_chunk_sentences:
                    chunk_content = " ".join(current_chunk_sentences)
                    if len(chunk_content.strip()) > 0:
                        chunk = DocumentChunk(
                            id=f"{hash(source_url)}_{len(chunks)}",
                            content=chunk_content.strip(),
                            metadata={
                                "source": source_url,
                                "chunk_index": len(chunks),
                                "sentence_count": len(current_chunk_sentences),
                                "chunking_method": "semantic",
                                "avg_similarity": np.mean([s for s in similarities[max(0, i-len(current_chunk_sentences)+1):i+1] if s is not None]) if i > 0 else 1.0
                            }
                        )
                        chunks.append(chunk)
                
                current_chunk_sentences = [sentences[i + 1]]
        
        # Add the last chunk
        if current_chunk_sentences:
            chunk_content = " ".join(current_chunk_sentences)
            if len(chunk_content.strip()) > 0:
                chunk = DocumentChunk(
                    id=f"{hash(source_url)}_{len(chunks)}",
                    content=chunk_content.strip(),
                    metadata={
                        "source": source_url,
                        "chunk_index": len(chunks),
                        "sentence_count": len(current_chunk_sentences),
                        "chunking_method": "semantic",
                        "total_chunks": len(chunks) + 1
                    }
                )
                chunks.append(chunk)
        
        # Update total_chunks in metadata
        for chunk in chunks:
            chunk.metadata["total_chunks"] = len(chunks)
        
        return chunks
    
    def _fallback_chunk(self, text: str, source_url: str) -> List[DocumentChunk]:
        """Fallback to simple text chunking if semantic chunking fails."""
        logger.warning("Using fallback chunking method")
        
        # Simple character-based chunking
        chunk_size = settings.CHUNK_SIZE
        overlap = settings.CHUNK_OVERLAP
        
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at word boundary
            if end < len(text):
                # Find the last space within the chunk
                last_space = text.rfind(' ', start, end)
                if last_space > start:
                    end = last_space
            
            chunk_content = text[start:end].strip()
            
            if chunk_content:
                chunk = DocumentChunk(
                    id=f"{hash(source_url)}_{chunk_index}",
                    content=chunk_content,
                    metadata={
                        "source": source_url,
                        "chunk_index": chunk_index,
                        "chunking_method": "fallback",
                        "start_pos": start,
                        "end_pos": end
                    }
                )
                chunks.append(chunk)
                chunk_index += 1
            
            start = end - overlap if end < len(text) else len(text)
        
        # Update total_chunks in metadata
        for chunk in chunks:
            chunk.metadata["total_chunks"] = len(chunks)
        
        return chunks
    
    def chunk_with_overlap(self, text: str, chunk_size: int, overlap: int, source_url: str = "") -> List[DocumentChunk]:
        """Traditional chunking with overlap for compatibility."""
        return self._fallback_chunk(text, source_url)
    
    async def semantic_chunk_parallel(self, text_list: List[str], source_urls: List[str] = None) -> List[List[DocumentChunk]]:
        """
        Perform semantic chunking on multiple texts in parallel.
        
        Args:
            text_list: List of texts to chunk
            source_urls: Optional list of source URLs for metadata
            
        Returns:
            List of chunk lists (one per text)
        """
        try:
            logger.info(f"Performing semantic chunking on {len(text_list)} texts in parallel")
            
            # Prepare arguments for parallel processing
            if source_urls is None:
                source_urls = [""] * len(text_list)
            
            chunk_args = list(zip(text_list, source_urls))
            
            # Use multiprocessing for parallel chunking
            results = await process_chunking_parallel(
                self._semantic_chunk_sync,
                chunk_args
            )
            
            logger.info(f"Successfully chunked {len(results)} texts in parallel")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel semantic chunking: {str(e)}")
            raise
    
    def _semantic_chunk_sync(self, args: tuple) -> List[DocumentChunk]:
        """
        Synchronous version of semantic chunking for multiprocessing.
        
        Args:
            args: Tuple of (text, source_url)
            
        Returns:
            List of document chunks
        """
        text, source_url = args
        
        # Import required modules in the worker process
        import asyncio
        
        # Create new event loop for this process
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Create a new chunker instance in this process
            chunker = SemanticChunker()
            
            # Run the async chunking in this process's event loop
            result = loop.run_until_complete(chunker.semantic_chunk(text, source_url))
            return result
        finally:
            loop.close()
    
    def get_cached_sentence_transformer(self):
        """Get cached sentence transformer model from application cache."""
        from src.services.model_cache import get_sentence_transformer
        
        cached_model = get_sentence_transformer()
        if cached_model is not None:
            return cached_model
        else:
            # Fallback to instance model if cache is not available
            logger.warning("Using fallback sentence transformer model (cache not available)")
            return self.model