"""Vector store service using FAISS for similarity search."""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import numpy as np
import faiss

from src.config import settings
from src.models.schemas import DocumentChunk, RetrievalResult
from src.services.multiprocessing_manager import mp_manager

logger = logging.getLogger(__name__)


class VectorStore:
    """FAISS-based vector store for similarity search with multiprocessing support."""
    
    def __init__(self):
        self.index: Optional[faiss.Index] = None
        self.chunks: List[DocumentChunk] = []
        self.index_path = Path(settings.FAISS_INDEX_PATH)
        self.metadata_path = Path(settings.FAISS_METADATA_PATH)
        
        # Ensure data directory exists
        self.index_path.parent.mkdir(parents=True, exist_ok=True)
        
    async def build_index(self, chunks: List[DocumentChunk]) -> None:
        """
        Build FAISS index from document chunks.
        
        Args:
            chunks: List of document chunks with embeddings
        """
        try:
            if not chunks:
                raise ValueError("No chunks provided for indexing")
            
            # Validate that all chunks have embeddings
            chunks_with_embeddings = [chunk for chunk in chunks if chunk.embedding is not None]
            if not chunks_with_embeddings:
                raise ValueError("No chunks with embeddings found")
            
            # Extract embeddings
            embeddings = np.array([chunk.embedding for chunk in chunks_with_embeddings], dtype=np.float32)
            
            # Create FAISS index
            dimension = embeddings.shape[1]
            self.index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embeddings)
            
            # Add embeddings to index
            self.index.add(embeddings)
            
            # Store chunks
            self.chunks = chunks_with_embeddings
            
            # Save index and metadata
            await self._save_index()
            
            logger.info(f"Built FAISS index with {len(chunks_with_embeddings)} chunks")
            
        except Exception as e:
            logger.error(f"Error building FAISS index: {str(e)}")
            raise
    
    async def search(self, query_embedding: List[float], top_k: int = None) -> List[RetrievalResult]:
        """
        Search for similar chunks using query embedding.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of top results to return
            
        Returns:
            List of retrieval results
        """
        try:
            if self.index is None:
                await self._load_index()
            
            if self.index is None:
                raise ValueError("No index available for search")
            
            top_k = top_k or settings.RERANK_TOP_K
            
            # Normalize query embedding
            query_vec = np.array([query_embedding], dtype=np.float32)
            faiss.normalize_L2(query_vec)
            
            # Search
            scores, indices = self.index.search(query_vec, min(top_k, len(self.chunks)))
            
            # Create retrieval results
            results = []
            for rank, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx >= 0 and idx < len(self.chunks):  # Valid index
                    result = RetrievalResult(
                        chunk=self.chunks[idx],
                        score=float(score),
                        rank=rank
                    )
                    results.append(result)
            
            logger.info(f"Retrieved {len(results)} results for query")
            return results
            
        except Exception as e:
            logger.error(f"Error searching vector store: {str(e)}")
            raise
    
    async def _save_index(self) -> None:
        """Save FAISS index and metadata to disk."""
        try:
            # Save FAISS index
            faiss.write_index(self.index, str(self.index_path))
            
            # Save metadata (convert numpy types to Python native types)
            metadata = {
                "chunks": [
                    {
                        "id": chunk.id,
                        "content": chunk.content,
                        "metadata": self._convert_to_serializable(chunk.metadata)
                    }
                    for chunk in self.chunks
                ],
                "index_size": int(self.index.ntotal),
                "dimension": int(self.index.d)
            }
            
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved index to {self.index_path} and metadata to {self.metadata_path}")
            
        except Exception as e:
            logger.error(f"Error saving index: {str(e)}")
            raise
    
    async def _load_index(self) -> None:
        """Load FAISS index and metadata from disk."""
        try:
            if not self.index_path.exists() or not self.metadata_path.exists():
                logger.warning("Index files not found")
                return
            
            # Load FAISS index
            self.index = faiss.read_index(str(self.index_path))
            
            # Load metadata
            with open(self.metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # Reconstruct chunks (without embeddings to save memory)
            self.chunks = [
                DocumentChunk(
                    id=chunk_data["id"],
                    content=chunk_data["content"],
                    metadata=chunk_data["metadata"]
                )
                for chunk_data in metadata["chunks"]
            ]
            
            logger.info(f"Loaded index with {len(self.chunks)} chunks")
            
        except Exception as e:
            logger.error(f"Error loading index: {str(e)}")
            raise
    
    def _convert_to_serializable(self, obj: Any) -> Any:
        """Convert numpy types and other non-serializable types to JSON-serializable types."""
        if isinstance(obj, dict):
            return {key: self._convert_to_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get statistics about the current index."""
        if self.index is None:
            return {"status": "no_index"}
        
        return {
            "status": "loaded",
            "total_vectors": int(self.index.ntotal),
            "dimension": int(self.index.d),
            "chunks_count": len(self.chunks)
        }
    
    async def search_parallel(self, query_embeddings: List[List[float]], top_k: int = None) -> List[List[RetrievalResult]]:
        """
        Search for similar chunks using multiple query embeddings in parallel.
        
        Args:
            query_embeddings: List of query embedding vectors
            top_k: Number of top results to return per query
            
        Returns:
            List of retrieval result lists (one per query)
        """
        try:
            if self.index is None:
                await self._load_index()
            
            if self.index is None:
                raise ValueError("No index available for search")
            
            logger.info(f"Performing parallel search for {len(query_embeddings)} queries")
            
            # Use thread pool for memory-intensive vector search operations
            search_args = [(embedding, top_k) for embedding in query_embeddings]
            
            results = await mp_manager.execute_batch_in_thread_pool(
                'vector_search',
                self._search_single_sync,
                search_args
            )
            
            logger.info(f"Successfully completed parallel search for {len(results)} queries")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel vector search: {str(e)}")
            raise
    
    def _search_single_sync(self, args: tuple) -> List[RetrievalResult]:
        """
        Synchronous version of single search for multiprocessing.
        
        Args:
            args: Tuple of (query_embedding, top_k)
            
        Returns:
            List of retrieval results
        """
        query_embedding, top_k = args
        
        try:
            if self.index is None:
                raise ValueError("No index available for search")
            
            top_k = top_k or settings.RERANK_TOP_K
            
            # Normalize query embedding
            query_vec = np.array([query_embedding], dtype=np.float32)
            faiss.normalize_L2(query_vec)
            
            # Search
            scores, indices = self.index.search(query_vec, min(top_k, len(self.chunks)))
            
            # Create retrieval results
            results = []
            for rank, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx >= 0 and idx < len(self.chunks):  # Valid index
                    result = RetrievalResult(
                        chunk=self.chunks[idx],
                        score=float(score),
                        rank=rank
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in sync vector search: {str(e)}")
            raise