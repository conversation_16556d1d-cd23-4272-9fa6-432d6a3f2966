"""
Background job processing system with multiprocessing optimization.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp

from src.models.job_models import Job, JobStatus, JobRequest, WebhookPayload
from src.models.schemas import QueryRequest, QueryResponse
from src.services.job_storage import job_storage
from src.services.advanced_query_processor import AdvancedQueryProcessor
from src.services.webhook_delivery_service import webhook_delivery_service
from src.config import settings

logger = logging.getLogger(__name__)


class BackgroundJobProcessor:
    """
    Background job processing system with multiprocessing worker pool.
    
    Features:
    - Multiprocessing worker pool for parallel job execution
    - Job queue management with priority support
    - Progress tracking and status updates
    - Automatic retry logic for failed jobs
    - Webhook notifications on completion
    """
    
    def __init__(self):
        # Worker pool configuration
        self.max_workers = getattr(settings, 'JOB_PROCESSOR_MAX_WORKERS', mp.cpu_count())
        self.worker_timeout = getattr(settings, 'JOB_PROCESSOR_TIMEOUT', 300)
        self.poll_interval = getattr(settings, 'JOB_PROCESSOR_POLL_INTERVAL', 1.0)
        
        # Processing state
        self.is_running = False
        self.active_workers = {}
        self.processed_jobs = 0
        self.failed_jobs = 0
        
        # Worker pools
        self.process_pool: Optional[ProcessPoolExecutor] = None
        self.thread_pool: Optional[ThreadPoolExecutor] = None
        
        # Advanced query processor for job execution
        self.query_processor = AdvancedQueryProcessor()
        
        logger.info(f"Background job processor initialized with {self.max_workers} workers")
    
    async def start(self):
        """Start the background job processing system."""
        if self.is_running:
            logger.warning("Job processor is already running")
            return
        
        try:
            # Initialize job storage
            await job_storage.initialize()
            
            # Initialize webhook delivery service
            await webhook_delivery_service.initialize()
            
            # Create worker pools
            self.process_pool = ProcessPoolExecutor(max_workers=self.max_workers)
            self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers * 2)
            
            self.is_running = True
            logger.info("Background job processor started")
            
            # Start main processing loop
            asyncio.create_task(self._processing_loop())
            
        except Exception as e:
            logger.error(f"Failed to start job processor: {e}")
            raise
    
    async def stop(self):
        """Stop the background job processing system."""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Stopping background job processor...")
        
        try:
            # Wait for active jobs to complete (with timeout)
            if self.active_workers:
                logger.info(f"Waiting for {len(self.active_workers)} active jobs to complete...")
                await asyncio.sleep(5)  # Give jobs time to finish
            
            # Shutdown worker pools
            if self.process_pool:
                self.process_pool.shutdown(wait=True)
                self.process_pool = None
            
            if self.thread_pool:
                self.thread_pool.shutdown(wait=True)
                self.thread_pool = None
            
            # Cleanup services
            await webhook_delivery_service.cleanup()
            await job_storage.cleanup()
            
            logger.info("Background job processor stopped")
            
        except Exception as e:
            logger.error(f"Error stopping job processor: {e}")
    
    async def submit_job(self, job_request: JobRequest) -> Job:
        """
        Submit a new job for processing.
        
        Args:
            job_request: Job request details
            
        Returns:
            Created job object
        """
        try:
            # Create job
            job = Job(request=job_request)
            
            # Store job
            await job_storage.store_job(job)
            
            # Add to queue
            await job_storage.enqueue_job(job)
            
            logger.info(f"Submitted job {job.job_id} for processing")
            return job
            
        except Exception as e:
            logger.error(f"Error submitting job: {e}")
            raise
    
    async def get_job_status(self, job_id: str) -> Optional[Job]:
        """
        Get current job status.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job object or None if not found
        """
        return await job_storage.get_job(job_id)
    
    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if successfully cancelled
        """
        try:
            job = await job_storage.get_job(job_id)
            if not job:
                return False
            
            if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                return False  # Already terminal
            
            # Update status
            await job_storage.update_job_status(job_id, JobStatus.CANCELLED)
            
            # Send webhook notification
            await self._send_webhook_notification(job_id, JobStatus.CANCELLED)
            
            logger.info(f"Cancelled job {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {e}")
            return False
    
    async def _processing_loop(self):
        """Main job processing loop."""
        logger.info("Started job processing loop")
        
        while self.is_running:
            try:
                # Get next job from queue
                job_id = await job_storage.dequeue_job()
                
                if job_id:
                    # Process job asynchronously
                    asyncio.create_task(self._process_job(job_id))
                else:
                    # No jobs available, wait before checking again
                    await asyncio.sleep(self.poll_interval)
                
            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                await asyncio.sleep(self.poll_interval)
        
        logger.info("Job processing loop stopped")
    
    async def _process_job(self, job_id: str):
        """
        Process a single job.
        
        Args:
            job_id: Job identifier
        """
        worker_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Get job details
            job = await job_storage.get_job(job_id)
            if not job:
                logger.error(f"Job {job_id} not found")
                return
            
            # Check if job is expired
            if job.is_expired:
                await job_storage.update_job_status(job_id, JobStatus.FAILED, error="Job expired")
                await self._send_webhook_notification(job_id, JobStatus.FAILED, error="Job expired")
                return
            
            # Mark job as processing
            self.active_workers[job_id] = worker_id
            await job_storage.update_job_status(
                job_id, 
                JobStatus.PROCESSING, 
                worker_id=worker_id,
                progress=10.0
            )
            
            logger.info(f"Processing job {job_id} with worker {worker_id}")
            
            # Execute job processing
            results = await self._execute_job(job)
            
            # Update job with results
            processing_time = time.time() - start_time
            await job_storage.update_job_status(
                job_id,
                JobStatus.COMPLETED,
                results=results,
                progress=100.0,
                processing_time=processing_time
            )
            
            # Send success webhook
            await self._send_webhook_notification(
                job_id, 
                JobStatus.COMPLETED, 
                results=results,
                processing_time=processing_time
            )
            
            self.processed_jobs += 1
            logger.info(f"Completed job {job_id} in {processing_time:.2f}s")
            
        except Exception as e:
            # Handle job failure
            processing_time = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"Job {job_id} failed: {error_message}")
            
            # Check if job should be retried
            job = await job_storage.get_job(job_id)
            if job and job.retry_count < job.max_retries:
                # Retry job
                await job_storage.update_job_status(
                    job_id,
                    JobStatus.QUEUED,
                    retry_count=job.retry_count + 1,
                    error=error_message
                )
                await job_storage.enqueue_job(job)
                logger.info(f"Retrying job {job_id} (attempt {job.retry_count + 1})")
            else:
                # Mark as failed
                await job_storage.update_job_status(
                    job_id,
                    JobStatus.FAILED,
                    error=error_message,
                    processing_time=processing_time
                )
                
                # Send failure webhook
                await self._send_webhook_notification(
                    job_id,
                    JobStatus.FAILED,
                    error=error_message,
                    processing_time=processing_time
                )
                
                self.failed_jobs += 1
        
        finally:
            # Remove from active workers
            self.active_workers.pop(job_id, None)
    
    async def _execute_job(self, job: Job) -> Dict[str, Any]:
        """
        Execute the actual job processing.
        
        Args:
            job: Job to execute
            
        Returns:
            Job results
        """
        try:
            # Update progress
            await job_storage.update_job_status(job.job_id, JobStatus.PROCESSING, progress=25.0)
            
            # Create query request from job
            query_request = QueryRequest(
                documents=job.request.documents,
                questions=job.request.questions
            )
            
            # Update progress
            await job_storage.update_job_status(job.job_id, JobStatus.PROCESSING, progress=50.0)
            
            # Process through advanced query processor
            response = await self.query_processor.process_request(query_request)
            
            # Update progress
            await job_storage.update_job_status(job.job_id, JobStatus.PROCESSING, progress=90.0)
            
            # Format results
            results = {
                "answers": response.answers,
                "questions_count": len(job.request.questions),
                "document_url": job.request.documents,
                "metadata": job.request.metadata
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Error executing job {job.job_id}: {e}")
            raise
    
    async def _send_webhook_notification(
        self,
        job_id: str,
        status: JobStatus,
        results: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
        processing_time: Optional[float] = None
    ):
        """
        Send webhook notification for job completion.
        
        Args:
            job_id: Job identifier
            status: Job status
            results: Job results if completed
            error: Error message if failed
            processing_time: Processing time in seconds
        """
        try:
            job = await job_storage.get_job(job_id)
            if not job:
                return
            
            # Create webhook payload
            payload = WebhookPayload(
                job_id=job_id,
                status=status,
                results=results,
                error=error,
                processing_time=processing_time,
                metadata=job.request.metadata
            )
            
            # Send webhook
            await webhook_delivery_service.send_webhook(
                job.request.webhook_url,
                payload.dict(),
                job.request.webhook_secret,
                job_id
            )
            
        except Exception as e:
            logger.error(f"Error sending webhook notification for job {job_id}: {e}")
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get current processing statistics.
        
        Returns:
            Processing statistics
        """
        try:
            # Get queue sizes
            queue_sizes = {}
            from src.models.job_models import JobPriority
            for priority in JobPriority:
                queue_sizes[priority.name.lower()] = await job_storage.get_queue_size(priority)
            
            total_queue_size = await job_storage.get_queue_size()
            
            # Get job metrics
            metrics = await job_storage.update_metrics()
            
            return {
                "is_running": self.is_running,
                "max_workers": self.max_workers,
                "active_workers": len(self.active_workers),
                "processed_jobs": self.processed_jobs,
                "failed_jobs": self.failed_jobs,
                "queue_sizes": queue_sizes,
                "total_queue_size": total_queue_size,
                "job_metrics": metrics.dict()
            }
            
        except Exception as e:
            logger.error(f"Error getting processing stats: {e}")
            return {"error": str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check health of job processing system.
        
        Returns:
            Health status
        """
        try:
            # Check job storage health
            storage_health = await job_storage.health_check()
            
            # Check webhook delivery health
            webhook_health = await webhook_delivery_service.health_check()
            
            # Check query processor health
            processor_health = await self.query_processor.health_check()
            
            overall_healthy = (
                self.is_running and
                storage_health.get("status") == "healthy" and
                webhook_health.get("status") == "healthy" and
                processor_health.get("overall_status") == "healthy"
            )
            
            return {
                "status": "healthy" if overall_healthy else "degraded",
                "is_running": self.is_running,
                "active_workers": len(self.active_workers),
                "max_workers": self.max_workers,
                "job_storage": storage_health,
                "webhook_delivery": webhook_health,
                "query_processor_status": processor_health.get("overall_status")
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global background job processor instance
background_job_processor = BackgroundJobProcessor()