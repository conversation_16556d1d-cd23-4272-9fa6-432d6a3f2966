"""Parallel Sub-Query Processor for concurrent RAG pipeline execution with hybrid retrieval and Cohere reranking."""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import numpy as np

from src.config import settings
from src.services.multiprocessing_manager import mp_manager
from src.models.schemas import DocumentChunk, RankedChunk, SubQuery
from src.services.cohere_reranker_service import cohere_reranker
# Hybrid vector store import removed
from src.services.embedding_service import AzureEmbeddingService

logger = logging.getLogger(__name__)


class ParallelSubQueryProcessor:
    """Service for orchestrating parallel processing of sub-queries through RAG pipeline."""
    
    def __init__(self):
        self.max_concurrent_queries = getattr(settings, 'MAX_CONCURRENT_SUBQUERIES', 16)
        self.timeout_per_query = getattr(settings, 'SUBQUERY_TIMEOUT', 30)
        
        # Performance tracking
        self.processed_queries = 0
        self.failed_queries = 0
        self.total_processing_time = 0.0
        
    async def process_subqueries_parallel_with_cohere(
        self,
        subqueries: List[str],
        document_index: Any,
        original_question: Optional[str] = None
    ) -> List[str]:
        """
        Process multiple sub-queries in parallel with Cohere reranking optimization.
        
        Args:
            subqueries: List of sub-queries to process
            document_index: FAISS index for document search
            original_question: Original question for context (optional)
            
        Returns:
            List of context strings from sub-query processing with Cohere reranking
        """
        if not subqueries:
            return []
        
        start_time = time.time()
        logger.info(f"Processing {len(subqueries)} sub-queries in parallel with Cohere reranking")
        
        try:
            # Step 1: Get initial chunks from vector search for all sub-queries
            all_chunks = await self._get_chunks_for_subqueries(subqueries, document_index)
            
            if not all_chunks:
                logger.warning("No chunks retrieved for sub-queries")
                return []
            
            # Step 2: Create SubQuery objects for Cohere reranker
            sub_query_objects = [
                SubQuery(
                    id=f"sq_{i}",
                    original_question_id="main",
                    query_text=query,
                    decomposition_confidence=1.0,
                    processing_time=0.0
                )
                for i, query in enumerate(subqueries)
            ]
            
            # Step 3: Use Cohere reranker for parallel reranking
            reranked_results = await cohere_reranker.rerank_parallel(sub_query_objects, all_chunks)
            
            # Step 4: Convert reranked results to context strings
            context_results = []
            for i, subquery in enumerate(subqueries):
                sub_query_id = f"sq_{i}"
                ranked_chunks = reranked_results.get(sub_query_id, [])
                
                if ranked_chunks:
                    # Combine top ranked chunks into context
                    context_texts = [chunk.chunk.content for chunk in ranked_chunks[:5]]  # Top 5 chunks
                    context = "\n\n".join(context_texts)
                    context_results.append(context)
                else:
                    logger.warning(f"No reranked results for sub-query {i}")
                    context_results.append("")
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.processed_queries += len(subqueries)
            self.total_processing_time += processing_time
            
            successful_count = len([ctx for ctx in context_results if ctx.strip()])
            logger.info(f"Cohere parallel processing completed: {successful_count}/{len(subqueries)} successful in {processing_time:.2f}s")
            
            # Filter out empty contexts
            valid_contexts = [ctx for ctx in context_results if ctx.strip()]
            return valid_contexts
            
        except Exception as e:
            logger.error(f"Error in parallel sub-query processing with Cohere: {e}")
            self.failed_queries += len(subqueries)
            # Fallback to original method
            return await self.process_subqueries_parallel(subqueries, document_index, original_question)

    async def process_subqueries_parallel(
        self, 
        subqueries: List[str], 
        document_index: Any,
        original_question: Optional[str] = None
    ) -> List[str]:
        """
        Process multiple sub-queries in parallel through the RAG pipeline.
        
        Args:
            subqueries: List of sub-queries to process
            document_index: FAISS index for document search
            original_question: Original question for context (optional)
            
        Returns:
            List of context strings from sub-query processing
        """
        if not subqueries:
            return []
        
        start_time = time.time()
        logger.info(f"Processing {len(subqueries)} sub-queries in parallel")
        
        try:
            # Process sub-queries with controlled concurrency
            semaphore = asyncio.Semaphore(self.max_concurrent_queries)
            
            async def process_with_semaphore(subquery: str, index: int) -> tuple:
                async with semaphore:
                    try:
                        context = await self.process_single_subquery(
                            subquery, document_index, index
                        )
                        return index, context, None
                    except Exception as e:
                        logger.error(f"Error processing sub-query {index}: {e}")
                        return index, "", str(e)
            
            # Create tasks for all sub-queries
            tasks = [
                process_with_semaphore(subquery, i) 
                for i, subquery in enumerate(subqueries)
            ]
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results and maintain order
            context_results = [""] * len(subqueries)
            successful_count = 0
            
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Sub-query processing exception: {result}")
                    self.failed_queries += 1
                    continue
                
                index, context, error = result
                if error:
                    logger.warning(f"Sub-query {index} failed: {error}")
                    self.failed_queries += 1
                else:
                    context_results[index] = context
                    successful_count += 1
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.processed_queries += len(subqueries)
            self.total_processing_time += processing_time
            
            logger.info(f"Parallel processing completed: {successful_count}/{len(subqueries)} successful in {processing_time:.2f}s")
            
            # Filter out empty contexts
            valid_contexts = [ctx for ctx in context_results if ctx.strip()]
            return valid_contexts
            
        except Exception as e:
            logger.error(f"Error in parallel sub-query processing: {e}")
            self.failed_queries += len(subqueries)
            return []
    
    async def _get_chunks_for_subqueries(
        self,
        subqueries: List[str],
        document_index: Any
    ) -> List[DocumentChunk]:
        """
        Get document chunks for all sub-queries using vector search.
        
        Args:
            subqueries: List of sub-queries
            document_index: FAISS vector store
            
        Returns:
            List of unique document chunks
        """
        try:
            from src.services.embedding_service import AzureEmbeddingService
            embedding_service = AzureEmbeddingService()
            
            all_chunks = []
            chunk_ids_seen = set()
            
            # Process each sub-query to get relevant chunks
            for subquery in subqueries:
                try:
                    # Generate embedding for sub-query
                    query_embedding = await embedding_service.generate_query_embedding(subquery)
                    
                    if query_embedding is None:
                        continue
                    
                    # Search for relevant chunks
                    retrieval_results = await document_index.search(query_embedding, top_k=15)
                    
                    # Add unique chunks
                    for result in retrieval_results:
                        chunk_id = getattr(result.chunk, 'id', str(hash(result.chunk.content)))
                        if chunk_id not in chunk_ids_seen:
                            chunk_ids_seen.add(chunk_id)
                            all_chunks.append(result.chunk)
                
                except Exception as e:
                    logger.warning(f"Error getting chunks for sub-query '{subquery}': {e}")
                    continue
            
            logger.info(f"Retrieved {len(all_chunks)} unique chunks for {len(subqueries)} sub-queries")
            return all_chunks
            
        except Exception as e:
            logger.error(f"Error in _get_chunks_for_subqueries: {e}")
            return []
    
    async def process_single_subquery(
        self, 
        subquery: str, 
        document_index: Any, 
        query_index: int = 0
    ) -> str:
        """
        Process a single sub-query through the RAG pipeline.
        
        Args:
            subquery: The sub-query to process
            document_index: FAISS index for document search
            query_index: Index of the query for tracking
            
        Returns:
            Context string from processing
        """
        try:
            logger.debug(f"Processing sub-query {query_index}: {subquery}")
            
            # Use the existing RAG pipeline components
            context = await self._get_pruned_context_for_query(subquery, document_index)
            
            logger.debug(f"Sub-query {query_index} processed successfully")
            return context
            
        except asyncio.TimeoutError:
            logger.error(f"Sub-query {query_index} timed out after {self.timeout_per_query}s")
            raise
        except Exception as e:
            logger.error(f"Error processing sub-query {query_index}: {e}")
            raise
    
    async def _get_pruned_context_for_query(self, subquery: str, document_index: Any) -> str:
        """
        Get pruned context for a sub-query using the full RAG pipeline with proper FAISS vector search.
        
        Args:
            subquery: The sub-query
            document_index: VectorStore instance with FAISS index
            
        Returns:
            Pruned context string
        """
        try:
            # Step 1: Generate query embedding using Azure OpenAI
            try:
                from src.services.embedding_service import AzureEmbeddingService
                embedding_service = AzureEmbeddingService()
                
                # Generate embedding for the sub-query
                query_embedding = await embedding_service.generate_query_embedding(subquery)
                
                if query_embedding is None:
                    logger.warning("Failed to generate query embedding, falling back to keyword matching")
                    return await self._fallback_keyword_search(subquery, document_index.chunks if hasattr(document_index, 'chunks') else [])
                
            except Exception as e:
                logger.warning(f"Embedding service failed: {e}, falling back to keyword matching")
                return await self._fallback_keyword_search(subquery, document_index.chunks if hasattr(document_index, 'chunks') else [])
            
            # Step 2: Use FAISS vector store for proper similarity search
            try:
                # Use the VectorStore's search method for optimized FAISS search
                retrieval_results = await document_index.search(query_embedding, top_k=20)
                
                # Apply similarity threshold filtering (0.6 for better recall)
                similarity_threshold = 0.6  # Lowered from 0.75 for better results
                high_quality_results = [
                    result for result in retrieval_results 
                    if result.score >= similarity_threshold
                ]
                
                # If no high-quality results, use top 8 results
                if not high_quality_results:
                    logger.info(f"No chunks met {similarity_threshold} similarity threshold, using top 8 by similarity")
                    high_quality_results = retrieval_results[:8]
                
                # Extract chunk texts (limit to 10 for performance)
                top_chunks = []
                for result in high_quality_results[:10]:
                    if hasattr(result.chunk, 'content'):
                        top_chunks.append(result.chunk.content)
                    else:
                        top_chunks.append(str(result.chunk))
                
                logger.debug(f"FAISS vector search: {len(retrieval_results)} total results, "
                            f"{len(high_quality_results)} above threshold, {len(top_chunks)} selected")
                
            except Exception as e:
                logger.warning(f"FAISS vector search failed: {e}, falling back to manual similarity")
                # Fallback to manual similarity calculation if FAISS fails
                return await self._manual_similarity_search(subquery, query_embedding, document_index)
            
            if not top_chunks:
                return f"No relevant context found for: {subquery}"
            
            logger.debug(f"Retrieved {len(top_chunks)} high-quality chunks (>0.75 similarity) for sub-query")
            
            # Step 3: Apply Cohere reranking with dynamic thresholding
            try:
                # Convert top chunks to DocumentChunk objects for Cohere reranker
                document_chunks = []
                for i, chunk_text in enumerate(top_chunks):
                    from src.models.schemas import DocumentChunk
                    doc_chunk = DocumentChunk(
                        id=f"chunk_{i}",
                        content=chunk_text,
                        metadata={"source": "vector_search"}
                    )
                    document_chunks.append(doc_chunk)
                
                # Rerank with Cohere
                cohere_ranked = await cohere_reranker.rerank_single(subquery, document_chunks)
                
                logger.debug(f"Cohere reranking: {len(top_chunks)} -> {len(cohere_ranked)} chunks")
                
                # Extract content from ranked chunks
                final_chunk_texts = []
                for ranked_chunk in cohere_ranked:
                    if hasattr(ranked_chunk, 'chunk') and hasattr(ranked_chunk.chunk, 'content'):
                        final_chunk_texts.append(ranked_chunk.chunk.content)
                    elif hasattr(ranked_chunk, 'content'):
                        final_chunk_texts.append(ranked_chunk.content)
                    else:
                        final_chunk_texts.append(str(ranked_chunk))
                
            except Exception as e:
                logger.warning(f"Cohere reranking failed, using top chunks: {e}")
                final_chunk_texts = top_chunks[:6]  # Fallback to top 6 chunks
            
            # Step 4: Combine into final context
            if final_chunk_texts:
                context = "\n\n".join(final_chunk_texts)
                logger.debug(f"Final context length: {len(context)} characters")
                return context
            else:
                # Final fallback
                return " ".join(top_chunks[:5])
                
        except Exception as e:
            logger.error(f"Error in RAG pipeline for sub-query: {e}")
            # Emergency fallback
            if hasattr(document_index, 'chunks') and document_index.chunks:
                emergency_chunks = []
                for chunk in document_index.chunks[:3]:
                    if hasattr(chunk, 'content'):
                        emergency_chunks.append(chunk.content)
                    elif isinstance(chunk, str):
                        emergency_chunks.append(chunk)
                    else:
                        emergency_chunks.append(str(chunk))
                return " ".join(emergency_chunks) if emergency_chunks else f"Error processing: {subquery}"
            return f"Error processing: {subquery}"
    
    def _apply_dynamic_threshold(self, ranked_chunks, min_chunks=3, max_chunks=8, threshold_percentile=0.6):
        """
        Apply dynamic thresholding to Qwen3 ranked chunks.
        
        Args:
            ranked_chunks: List of ranked chunks with scores
            min_chunks: Minimum number of chunks to return
            max_chunks: Maximum number of chunks to return
            threshold_percentile: Percentile threshold for score cutoff
            
        Returns:
            List of selected chunk texts
        """
        if not ranked_chunks:
            return []
        
        # Extract scores
        scores = []
        chunk_texts = []
        
        for ranked_chunk in ranked_chunks:
            if hasattr(ranked_chunk, 'score'):
                scores.append(ranked_chunk.score)
            else:
                scores.append(0.5)  # Default score
            
            if hasattr(ranked_chunk, 'chunk') and hasattr(ranked_chunk.chunk, 'content'):
                chunk_texts.append(ranked_chunk.chunk.content)
            elif hasattr(ranked_chunk, 'content'):
                chunk_texts.append(ranked_chunk.content)
            else:
                chunk_texts.append(str(ranked_chunk))
        
        if not scores:
            return chunk_texts[:min_chunks]
        
        # Calculate dynamic threshold
        sorted_scores = sorted(scores, reverse=True)
        threshold_idx = int(len(sorted_scores) * threshold_percentile)
        threshold_score = sorted_scores[min(threshold_idx, len(sorted_scores) - 1)]
        
        # Select chunks above threshold
        selected_chunks = []
        for i, score in enumerate(scores):
            if score >= threshold_score and len(selected_chunks) < max_chunks:
                selected_chunks.append(chunk_texts[i])
        
        # Ensure minimum chunks
        if len(selected_chunks) < min_chunks:
            selected_chunks = chunk_texts[:min_chunks]
        
        return selected_chunks
    

    
    async def _manual_similarity_search(self, subquery: str, query_embedding: List[float], document_index: Any) -> str:
        """
        Manual similarity search fallback when FAISS fails.
        
        Args:
            subquery: The sub-query
            query_embedding: Query embedding vector
            document_index: Document index with chunks
            
        Returns:
            Context string from manual similarity search
        """
        try:
            # Get chunks from document index
            if hasattr(document_index, 'chunks'):
                chunks = document_index.chunks
            else:
                logger.warning("Document index doesn't have chunks attribute")
                return f"Context for query: {subquery}"
            
            # Convert query embedding to numpy array
            query_vec = np.array(query_embedding, dtype=np.float32)
            
            # Calculate cosine similarity with all chunk embeddings
            chunk_similarities = []
            for i, chunk in enumerate(chunks):
                chunk_embedding = None
                chunk_text = ""
                
                # Extract chunk embedding and text
                if hasattr(chunk, 'embedding') and chunk.embedding is not None:
                    chunk_embedding = np.array(chunk.embedding, dtype=np.float32)
                    
                if hasattr(chunk, 'content'):
                    chunk_text = chunk.content
                elif isinstance(chunk, str):
                    chunk_text = chunk
                else:
                    chunk_text = str(chunk)
                
                # Skip chunks without embeddings
                if chunk_embedding is None:
                    logger.debug(f"Chunk {i} has no embedding, skipping")
                    continue
                
                # Calculate cosine similarity
                try:
                    cosine_sim = self._cosine_similarity(query_vec, chunk_embedding)
                    chunk_similarities.append((i, chunk_text, cosine_sim))
                except Exception as e:
                    logger.debug(f"Error calculating similarity for chunk {i}: {e}")
                    continue
            
            # Filter by similarity threshold and sort
            similarity_threshold = 0.5  # More reasonable threshold for manual search
            high_quality_chunks = [
                (i, chunk_text, score) for i, chunk_text, score in chunk_similarities 
                if score >= similarity_threshold
            ]
            
            # Sort by similarity score (descending) and limit to 10 chunks
            high_quality_chunks.sort(key=lambda x: x[2], reverse=True)
            selected_chunks = high_quality_chunks[:10]
            
            # Extract chunk texts
            top_chunks = [chunk_text for _, chunk_text, _ in selected_chunks]
            
            if not top_chunks:
                # If no chunks meet the threshold, take top 8 by similarity
                logger.info(f"No chunks met {similarity_threshold} similarity threshold, using top 8 by similarity")
                chunk_similarities.sort(key=lambda x: x[2], reverse=True)
                top_chunks = [chunk_text for _, chunk_text, _ in chunk_similarities[:8]]
            
            logger.debug(f"Manual similarity search: {len(chunk_similarities)} chunks processed, "
                        f"{len(high_quality_chunks)} above threshold, {len(top_chunks)} selected")
            
            return "\n\n".join(top_chunks) if top_chunks else f"No relevant context found for: {subquery}"
            
        except Exception as e:
            logger.error(f"Error in manual similarity search: {e}")
            return await self._fallback_keyword_search(subquery, document_index.chunks if hasattr(document_index, 'chunks') else [])
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """
        Calculate cosine similarity between two vectors.
        
        Args:
            vec1: First vector
            vec2: Second vector
            
        Returns:
            Cosine similarity score (0-1)
        """
        try:
            # Normalize vectors
            vec1_norm = vec1 / np.linalg.norm(vec1)
            vec2_norm = vec2 / np.linalg.norm(vec2)
            
            # Calculate cosine similarity
            similarity = np.dot(vec1_norm, vec2_norm)
            
            # Ensure result is in [0, 1] range
            return max(0.0, min(1.0, float(similarity)))
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
    
    async def _fallback_keyword_search(self, subquery: str, chunks: List[Any]) -> str:
        """
        Fallback keyword-based search when vector search fails.
        
        Args:
            subquery: The sub-query
            chunks: List of document chunks
            
        Returns:
            Context string from keyword matching
        """
        try:
            query_words = set(subquery.lower().split())
            similarity_threshold = 0.1  # Very low threshold for keyword matching
            max_chunks = 8
            
            # Score chunks based on keyword overlap
            chunk_scores = []
            for i, chunk in enumerate(chunks):
                chunk_text = ""
                if hasattr(chunk, 'content'):
                    chunk_text = chunk.content
                elif isinstance(chunk, str):
                    chunk_text = chunk
                else:
                    chunk_text = str(chunk)
                
                # Calculate keyword overlap score
                chunk_words = set(chunk_text.lower().split())
                if not chunk_words:
                    continue
                
                intersection = len(query_words.intersection(chunk_words))
                overlap_ratio = intersection / len(query_words) if query_words else 0
                
                chunk_scores.append((i, chunk_text, overlap_ratio))
            
            # Filter by threshold and sort
            high_quality_chunks = [
                (i, chunk_text, score) for i, chunk_text, score in chunk_scores 
                if score >= similarity_threshold
            ]
            
            high_quality_chunks.sort(key=lambda x: x[2], reverse=True)
            selected_chunks = high_quality_chunks[:max_chunks]
            
            # Extract chunk texts
            top_chunks = [chunk_text for _, chunk_text, _ in selected_chunks]
            
            if not top_chunks:
                # Final fallback - take top 5 chunks by score
                chunk_scores.sort(key=lambda x: x[2], reverse=True)
                top_chunks = [chunk_text for _, chunk_text, _ in chunk_scores[:5]]
            
            logger.debug(f"Keyword fallback search: {len(top_chunks)} chunks selected")
            
            if top_chunks:
                return "\n\n".join(top_chunks)
            else:
                return f"No relevant context found for: {subquery}"
                
        except Exception as e:
            logger.error(f"Error in fallback keyword search: {e}")
            return f"Error processing: {subquery}"
    
    async def aggregate_subquery_results(self, results: List[str]) -> str:
        """
        Aggregate results from multiple sub-query executions.
        
        Args:
            results: List of context strings from sub-queries
            
        Returns:
            Aggregated context string
        """
        if not results:
            return ""
        
        # Filter out empty results
        valid_results = [result for result in results if result.strip()]
        
        if not valid_results:
            return ""
        
        # Simple aggregation - combine all contexts
        # In a more sophisticated implementation, this could include deduplication
        aggregated = "\n\n---\n\n".join(valid_results)
        
        logger.info(f"Aggregated {len(valid_results)} sub-query results into final context")
        return aggregated
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for the processor.
        
        Returns:
            Dictionary with performance statistics
        """
        total_queries = self.processed_queries + self.failed_queries
        success_rate = self.processed_queries / max(total_queries, 1) * 100
        avg_processing_time = self.total_processing_time / max(self.processed_queries, 1)
        
        return {
            "total_queries_processed": total_queries,
            "successful_queries": self.processed_queries,
            "failed_queries": self.failed_queries,
            "success_rate_percent": round(success_rate, 2),
            "total_processing_time_seconds": round(self.total_processing_time, 2),
            "average_processing_time_seconds": round(avg_processing_time, 3),
            "max_concurrent_queries": self.max_concurrent_queries,
            "timeout_per_query_seconds": self.timeout_per_query
        }
    
    def reset_stats(self):
        """Reset performance statistics."""
        self.processed_queries = 0
        self.failed_queries = 0
        self.total_processing_time = 0.0
        logger.info("Performance statistics reset")
    
    def configure_concurrency(self, max_concurrent: int, timeout_per_query: int):
        """
        Configure concurrency settings.
        
        Args:
            max_concurrent: Maximum number of concurrent sub-queries
            timeout_per_query: Timeout per sub-query in seconds
        """
        self.max_concurrent_queries = max_concurrent
        self.timeout_per_query = timeout_per_query
        logger.info(f"Concurrency configured: max_concurrent={max_concurrent}, timeout={timeout_per_query}s")
    
    async def process_with_load_balancing(
        self, 
        subqueries: List[str], 
        document_indices: List[Any],
        load_balance_strategy: str = "round_robin"
    ) -> List[str]:
        """
        Process sub-queries with load balancing across multiple document indices.
        
        Args:
            subqueries: List of sub-queries to process
            document_indices: List of document indices to balance load across
            load_balance_strategy: Strategy for load balancing ("round_robin", "random")
            
        Returns:
            List of context strings
        """
        if not subqueries or not document_indices:
            return []
        
        logger.info(f"Processing {len(subqueries)} sub-queries with load balancing across {len(document_indices)} indices")
        
        # Assign sub-queries to indices based on strategy
        assignments = []
        for i, subquery in enumerate(subqueries):
            if load_balance_strategy == "round_robin":
                index_idx = i % len(document_indices)
            elif load_balance_strategy == "random":
                import random
                index_idx = random.randint(0, len(document_indices) - 1)
            else:
                index_idx = 0  # Default to first index
            
            assignments.append((subquery, document_indices[index_idx], i))
        
        # Process with assigned indices
        semaphore = asyncio.Semaphore(self.max_concurrent_queries)
        
        async def process_assigned(assignment: tuple) -> tuple:
            subquery, doc_index, original_idx = assignment
            async with semaphore:
                try:
                    context = await self.process_single_subquery(subquery, doc_index, original_idx)
                    return original_idx, context, None
                except Exception as e:
                    return original_idx, "", str(e)
        
        # Execute all assignments
        tasks = [process_assigned(assignment) for assignment in assignments]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Reconstruct results in original order
        context_results = [""] * len(subqueries)
        for result in results:
            if isinstance(result, Exception):
                continue
            
            idx, context, error = result
            if not error:
                context_results[idx] = context
        
        return [ctx for ctx in context_results if ctx.strip()]