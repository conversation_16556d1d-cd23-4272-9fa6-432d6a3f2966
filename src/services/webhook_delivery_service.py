"""
Webhook delivery service with retry logic and HMAC signatures.
"""

import asyncio
import hashlib
import hmac
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
import aiohttp
import ssl

from src.models.job_models import WebhookDelivery, WebhookDeliveryStatus
from src.services.job_storage import job_storage
from src.config import settings

logger = logging.getLogger(__name__)


class WebhookDeliveryService:
    """
    Webhook delivery service with comprehensive retry logic and security features.
    
    Features:
    - HMAC-SHA256 signature generation
    - Exponential backoff retry logic
    - SSL/TLS validation
    - Delivery status tracking
    - Dead letter queue for failed deliveries
    """
    
    def __init__(self):
        # Configuration
        self.timeout = getattr(settings, 'WEBHOOK_TIMEOUT', 30)
        self.max_retries = getattr(settings, 'WEBHOOK_MAX_RETRIES', 3)
        self.initial_retry_delay = getattr(settings, 'WEBHOOK_INITIAL_RETRY_DELAY', 5)
        self.max_retry_delay = getattr(settings, 'WEBHOOK_MAX_RETRY_DELAY', 300)
        self.require_https = getattr(settings, 'WEBHOOK_REQUIRE_HTTPS', False)
        
        # HTTP session
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Delivery tracking
        self.delivered_count = 0
        self.failed_count = 0
        self.retry_count = 0
        
        # Background retry task
        self.retry_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        logger.info("Webhook delivery service initialized")
    
    async def initialize(self):
        """Initialize the webhook delivery service."""
        try:
            # Create HTTP session with SSL context
            ssl_context = ssl.create_default_context()
            if not self.require_https:
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
            
            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=100,
                limit_per_host=20,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    "User-Agent": "LLM-Query-Webhook-Service/1.0",
                    "Content-Type": "application/json"
                }
            )
            
            self.is_running = True
            
            # Start background retry task
            self.retry_task = asyncio.create_task(self._retry_loop())
            
            logger.info("Webhook delivery service started")
            
        except Exception as e:
            logger.error(f"Failed to initialize webhook delivery service: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup webhook delivery service."""
        self.is_running = False
        
        if self.retry_task:
            self.retry_task.cancel()
            try:
                await self.retry_task
            except asyncio.CancelledError:
                pass
        
        if self.session:
            await self.session.close()
            self.session = None
        
        logger.info("Webhook delivery service stopped")
    
    async def send_webhook(
        self,
        webhook_url: str,
        payload: Dict[str, Any],
        secret: Optional[str] = None,
        job_id: Optional[str] = None
    ) -> bool:
        """
        Send webhook notification with retry logic.
        
        Args:
            webhook_url: Target webhook URL
            payload: Webhook payload
            secret: Optional secret for HMAC signature
            job_id: Associated job ID
            
        Returns:
            True if delivery was successful or queued for retry
        """
        try:
            # Validate webhook URL
            if not self._validate_webhook_url(webhook_url):
                logger.error(f"Invalid webhook URL: {webhook_url}")
                return False
            
            # Create webhook delivery record
            delivery = WebhookDelivery(
                job_id=job_id or "unknown",
                webhook_url=webhook_url,
                payload=payload,
                max_attempts=self.max_retries
            )
            
            # Generate HMAC signature if secret provided
            if secret:
                delivery.signature = self._generate_signature(payload, secret)
            
            # Store delivery record
            await job_storage.store_webhook_delivery(delivery)
            
            # Attempt immediate delivery
            success = await self._deliver_webhook(delivery)
            
            if success:
                self.delivered_count += 1
                logger.info(f"Webhook delivered successfully to {webhook_url}")
            else:
                # Schedule for retry
                delivery.status = WebhookDeliveryStatus.RETRYING
                delivery.next_attempt = datetime.utcnow() + timedelta(seconds=self.initial_retry_delay)
                await job_storage.store_webhook_delivery(delivery)
                logger.info(f"Webhook delivery failed, scheduled for retry: {webhook_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending webhook to {webhook_url}: {e}")
            return False
    
    async def _deliver_webhook(self, delivery: WebhookDelivery) -> bool:
        """
        Attempt to deliver a webhook.
        
        Args:
            delivery: Webhook delivery record
            
        Returns:
            True if successful
        """
        try:
            if not self.session:
                logger.error("HTTP session not initialized")
                return False
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "X-Webhook-Timestamp": str(int(time.time())),
                "X-Webhook-Job-ID": delivery.job_id
            }
            
            # Add HMAC signature if available
            if delivery.signature:
                headers["X-Webhook-Signature"] = delivery.signature
            
            # Prepare payload
            payload_json = json.dumps(delivery.payload, default=str)
            
            # Update delivery attempt
            delivery.attempts += 1
            delivery.last_attempt = datetime.utcnow()
            delivery.status = WebhookDeliveryStatus.PENDING
            
            # Make HTTP request
            async with self.session.post(
                delivery.webhook_url,
                data=payload_json,
                headers=headers
            ) as response:
                delivery.response_status = response.status
                delivery.response_body = await response.text()
                
                # Check if delivery was successful
                if 200 <= response.status < 300:
                    delivery.status = WebhookDeliveryStatus.DELIVERED
                    delivery.delivered_at = datetime.utcnow()
                    await job_storage.store_webhook_delivery(delivery)
                    return True
                else:
                    delivery.error = f"HTTP {response.status}: {delivery.response_body[:200]}"
                    await job_storage.store_webhook_delivery(delivery)
                    return False
        
        except asyncio.TimeoutError:
            delivery.error = "Request timeout"
            await job_storage.store_webhook_delivery(delivery)
            return False
        
        except Exception as e:
            delivery.error = str(e)
            await job_storage.store_webhook_delivery(delivery)
            logger.error(f"Error delivering webhook to {delivery.webhook_url}: {e}")
            return False
    
    async def _retry_loop(self):
        """Background loop for retrying failed webhook deliveries."""
        logger.info("Started webhook retry loop")
        
        while self.is_running:
            try:
                # Get pending deliveries
                pending_deliveries = await job_storage.get_pending_webhook_deliveries(limit=50)
                
                if pending_deliveries:
                    logger.debug(f"Processing {len(pending_deliveries)} pending webhook deliveries")
                    
                    # Process deliveries concurrently
                    tasks = []
                    for delivery in pending_deliveries:
                        if delivery.should_retry:
                            tasks.append(self._retry_delivery(delivery))
                    
                    if tasks:
                        await asyncio.gather(*tasks, return_exceptions=True)
                
                # Wait before next check
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in webhook retry loop: {e}")
                await asyncio.sleep(10)
        
        logger.info("Webhook retry loop stopped")
    
    async def _retry_delivery(self, delivery: WebhookDelivery):
        """
        Retry a failed webhook delivery.
        
        Args:
            delivery: Webhook delivery to retry
        """
        try:
            # Check if we should retry now
            if delivery.next_attempt and datetime.utcnow() < delivery.next_attempt:
                return
            
            logger.debug(f"Retrying webhook delivery {delivery.delivery_id} (attempt {delivery.attempts + 1})")
            
            # Attempt delivery
            success = await self._deliver_webhook(delivery)
            
            if success:
                self.delivered_count += 1
                logger.info(f"Webhook retry successful: {delivery.webhook_url}")
            else:
                self.retry_count += 1
                
                # Check if we should continue retrying
                if delivery.attempts >= delivery.max_attempts:
                    # Mark as permanently failed
                    delivery.status = WebhookDeliveryStatus.FAILED
                    await job_storage.store_webhook_delivery(delivery)
                    self.failed_count += 1
                    logger.warning(f"Webhook delivery permanently failed after {delivery.attempts} attempts: {delivery.webhook_url}")
                else:
                    # Schedule next retry with exponential backoff
                    delay = min(
                        self.initial_retry_delay * (2 ** (delivery.attempts - 1)),
                        self.max_retry_delay
                    )
                    delivery.next_attempt = datetime.utcnow() + timedelta(seconds=delay)
                    delivery.status = WebhookDeliveryStatus.RETRYING
                    await job_storage.store_webhook_delivery(delivery)
                    logger.info(f"Webhook delivery scheduled for retry in {delay}s: {delivery.webhook_url}")
        
        except Exception as e:
            logger.error(f"Error retrying webhook delivery {delivery.delivery_id}: {e}")
    
    def _validate_webhook_url(self, url: str) -> bool:
        """
        Validate webhook URL format and security requirements.
        
        Args:
            url: Webhook URL to validate
            
        Returns:
            True if valid
        """
        try:
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # Check HTTPS requirement
            if self.require_https and parsed.scheme != 'https':
                return False
            
            # Check hostname
            if not parsed.hostname:
                return False
            
            # Prevent SSRF attacks - block private/local addresses
            hostname = parsed.hostname.lower()
            if hostname in ['localhost', '127.0.0.1', '0.0.0.0']:
                return False
            
            # Block private IP ranges (basic check)
            if hostname.startswith(('10.', '172.', '192.168.')):
                return False
            
            return True
            
        except Exception:
            return False
    
    def _generate_signature(self, payload: Dict[str, Any], secret: str) -> str:
        """
        Generate HMAC-SHA256 signature for webhook payload.
        
        Args:
            payload: Webhook payload
            secret: Secret key
            
        Returns:
            HMAC signature
        """
        try:
            payload_json = json.dumps(payload, sort_keys=True, default=str)
            signature = hmac.new(
                secret.encode('utf-8'),
                payload_json.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            return f"sha256={signature}"
            
        except Exception as e:
            logger.error(f"Error generating webhook signature: {e}")
            return ""
    
    def verify_signature(self, payload: str, signature: str, secret: str) -> bool:
        """
        Verify webhook signature.
        
        Args:
            payload: Raw payload string
            signature: Received signature
            secret: Secret key
            
        Returns:
            True if signature is valid
        """
        try:
            if not signature.startswith('sha256='):
                return False
            
            expected_signature = hmac.new(
                secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            received_signature = signature[7:]  # Remove 'sha256=' prefix
            
            return hmac.compare_digest(expected_signature, received_signature)
            
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False
    
    async def get_delivery_stats(self) -> Dict[str, Any]:
        """
        Get webhook delivery statistics.
        
        Returns:
            Delivery statistics
        """
        try:
            # Get pending deliveries count
            pending_deliveries = await job_storage.get_pending_webhook_deliveries(limit=1000)
            pending_count = len(pending_deliveries)
            
            # Calculate success rate
            total_attempts = self.delivered_count + self.failed_count
            success_rate = (self.delivered_count / total_attempts * 100) if total_attempts > 0 else 0
            
            return {
                "delivered_count": self.delivered_count,
                "failed_count": self.failed_count,
                "retry_count": self.retry_count,
                "pending_count": pending_count,
                "success_rate": round(success_rate, 2),
                "is_running": self.is_running
            }
            
        except Exception as e:
            logger.error(f"Error getting delivery stats: {e}")
            return {"error": str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check webhook delivery service health.
        
        Returns:
            Health status
        """
        try:
            # Check if service is running
            if not self.is_running or not self.session:
                return {
                    "status": "unhealthy",
                    "error": "Service not running"
                }
            
            # Get delivery stats
            stats = await self.get_delivery_stats()
            
            # Check if retry loop is running
            retry_loop_healthy = self.retry_task and not self.retry_task.done()
            
            return {
                "status": "healthy" if retry_loop_healthy else "degraded",
                "retry_loop_running": retry_loop_healthy,
                "session_initialized": self.session is not None,
                "delivery_stats": stats
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global webhook delivery service instance
webhook_delivery_service = WebhookDeliveryService()