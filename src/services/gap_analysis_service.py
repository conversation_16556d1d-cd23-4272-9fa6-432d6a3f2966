"""Knowledge Gap Analysis Service for analyzing context sufficiency using GPT-4.1 with Chain-of-Thought reasoning."""

import json
import logging
import time
from typing import List, Dict, Any, Optional
from openai import AsyncAzureOpenAI
from cachetools import LRUCache

from src.config import settings
from src.models.schemas import GapAnalysisResult
from src.services.multiprocessing_manager import mp_manager, process_llm_parallel
# Chain-of-thought import removed

logger = logging.getLogger(__name__)


class KnowledgeGapAnalyzer:
    """Service for analyzing retrieved context sufficiency using GPT-4.1 with Chain-of-Thought reasoning."""
    
    def __init__(self):
        self.client = AsyncAzureOpenAI(
            api_key=settings.AZURE_OPENAI_API_KEY,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT
        )
        self.model = settings.AZURE_OPENAI_MODEL
# Chain-of-thought manager removed
        
        # Cache for similar context analysis results
        self.gap_analysis_cache = LRUCache(maxsize=200)
        
        # Configuration
        self.timeout = getattr(settings, 'GAP_ANALYSIS_TIMEOUT', 20)
        self.confidence_threshold = getattr(settings, 'GAP_ANALYSIS_CONFIDENCE_THRESHOLD', 0.8)
        
    async def analyze_context_sufficiency(
        self, 
        user_question: str, 
        context: str
    ) -> GapAnalysisResult:
        """
        Analyze whether the provided context is sufficient to answer the user question.
        
        Args:
            user_question: The original user question
            context: The retrieved context to analyze
            
        Returns:
            GapAnalysisResult with sufficiency analysis
        """
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(user_question, context)
            if cache_key in self.gap_analysis_cache:
                logger.info(f"Using cached gap analysis for question: {user_question[:50]}...")
                cached_result = self.gap_analysis_cache[cache_key]
                # Update analysis time for cached result
                cached_result.analysis_time = time.time() - start_time
                return cached_result
            
            # Call LLM for gap analysis
            analysis_result = await self._call_analyst_llm(user_question, context)
            
            # Create GapAnalysisResult
            gap_result = GapAnalysisResult(
                is_sufficient=analysis_result.get("is_sufficient", False),
                missing_information=analysis_result.get("missing_information", ""),
                confidence_score=analysis_result.get("confidence_score", 0.5),
                analysis_time=time.time() - start_time,
                context_quality_score=self._calculate_context_quality(context)
            )
            
            # Cache the result
            self.gap_analysis_cache[cache_key] = gap_result
            
            logger.info(f"Gap analysis completed in {gap_result.analysis_time:.2f}s - Sufficient: {gap_result.is_sufficient}")
            return gap_result
            
        except Exception as e:
            logger.error(f"Error in gap analysis: {str(e)}")
            # Return fallback result
            return GapAnalysisResult(
                is_sufficient=True,  # Assume sufficient to avoid unnecessary retries
                missing_information="",
                confidence_score=0.3,  # Low confidence for fallback
                analysis_time=time.time() - start_time,
                context_quality_score=0.5
            )
    
    async def analyze_multiple_contexts(
        self, 
        questions: List[str], 
        contexts: List[str]
    ) -> List[GapAnalysisResult]:
        """
        Analyze multiple question-context pairs in parallel.
        
        Args:
            questions: List of user questions
            contexts: List of contexts corresponding to each question
            
        Returns:
            List of gap analysis results
        """
        try:
            if len(questions) != len(contexts):
                raise ValueError("Number of questions must match number of contexts")
            
            logger.info(f"Analyzing {len(questions)} question-context pairs in parallel")
            
            # Prepare arguments for parallel processing
            analysis_args = list(zip(questions, contexts))
            
            # Use thread pool for I/O-bound LLM API calls
            results = await process_llm_parallel(
                self._analyze_context_sufficiency_sync,
                analysis_args
            )
            
            logger.info(f"Parallel gap analysis completed for {len(results)} pairs")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel gap analysis: {str(e)}")
            # Return fallback results
            return [
                GapAnalysisResult(
                    is_sufficient=True,
                    missing_information="",
                    confidence_score=0.3,
                    analysis_time=0.0,
                    context_quality_score=0.5
                )
                for _ in questions
            ]
    
    async def _call_analyst_llm(self, question: str, context: str) -> Dict[str, Any]:
        """
        Call GPT-4.1 to analyze context sufficiency.
        
        Args:
            question: The user question
            context: The context to analyze
            
        Returns:
            Dictionary with analysis results
        """
        try:
            prompt = self._format_gap_analysis_prompt(question, context)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_gap_analysis_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent analysis
                max_tokens=800,
                timeout=self.timeout
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse the JSON response
            analysis_result = self._parse_gap_analysis_response(response_text)
            
            # Log token usage
            if response.usage:
                logger.info(f"Gap analysis LLM tokens used - Prompt: {response.usage.prompt_tokens}, "
                           f"Completion: {response.usage.completion_tokens}, "
                           f"Total: {response.usage.total_tokens}")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error calling gap analysis LLM: {str(e)}")
            # Return fallback analysis
            return {
                "is_sufficient": True,
                "missing_information": "",
                "confidence_score": 0.3
            }
    
    def _format_gap_analysis_prompt(self, question: str, context: str) -> str:
        """
        Format the prompt for gap analysis.
        
        Args:
            question: The user question
            context: The context to analyze
            
        Returns:
            Formatted prompt
        """
        return f"""You are a meticulous fact-checker analyzing whether provided context is sufficient to fully and accurately answer a user question.

Your task:
1. Carefully examine the provided context
2. Determine if it contains all necessary information to completely answer the question
3. Identify any specific missing information if the context is insufficient
4. Provide a confidence score for your analysis

Guidelines:
- Be thorough but practical in your analysis
- Consider domain-specific requirements (insurance, legal, HR, compliance)
- Look for specific details like amounts, dates, conditions, exclusions, procedures
- If context partially answers the question but lacks key details, mark as insufficient
- If context fully addresses all aspects of the question, mark as sufficient

Original User Question: {question}

Provided Context: {context}

Analyze the context and respond in JSON format with these exact keys:
- "is_sufficient": boolean (true if context fully answers the question, false if missing key information)
- "missing_information": string (if insufficient, describe specifically what information is missing; if sufficient, use empty string "")
- "confidence_score": number between 0.0 and 1.0 (your confidence in this analysis)

Return only the JSON object, no additional text."""
    
    def _get_gap_analysis_system_prompt(self) -> str:
        """Get the system prompt for gap analysis."""
        return """You are an expert fact-checker and information analyst specializing in insurance, legal, HR, and compliance domains.

Your role is to:
1. Meticulously analyze provided context against user questions
2. Identify information gaps that would prevent complete answers
3. Be precise about what specific information is missing
4. Consider domain-specific requirements and nuances
5. Provide accurate confidence assessments

You excel at:
- Recognizing when context lacks specific details (amounts, dates, conditions)
- Understanding domain-specific information requirements
- Distinguishing between partial and complete information
- Identifying missing procedural or conditional information

Always respond with valid JSON format and be thorough in your analysis."""
    
    def _parse_gap_analysis_response(self, response: str) -> Dict[str, Any]:
        """
        Parse the LLM response to extract gap analysis results.
        
        Args:
            response: Raw response from LLM
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Clean up response
            response = response.strip()
            
            # Remove any markdown code block formatting
            if response.startswith("```json"):
                response = response[7:]
            if response.startswith("```"):
                response = response[3:]
            if response.endswith("```"):
                response = response[:-3]
            
            response = response.strip()
            
            # Parse JSON
            analysis_result = json.loads(response)
            
            # Validate required fields
            required_fields = ["is_sufficient", "missing_information", "confidence_score"]
            if not all(field in analysis_result for field in required_fields):
                logger.error(f"Missing required fields in gap analysis response: {response}")
                return self._create_fallback_analysis()
            
            # Validate data types
            if not isinstance(analysis_result["is_sufficient"], bool):
                logger.error(f"Invalid is_sufficient type: {type(analysis_result['is_sufficient'])}")
                return self._create_fallback_analysis()
            
            if not isinstance(analysis_result["missing_information"], str):
                logger.error(f"Invalid missing_information type: {type(analysis_result['missing_information'])}")
                return self._create_fallback_analysis()
            
            confidence = analysis_result["confidence_score"]
            if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                logger.error(f"Invalid confidence_score: {confidence}")
                analysis_result["confidence_score"] = 0.5  # Default confidence
            
            return analysis_result
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse gap analysis JSON: {e}")
            logger.error(f"Response was: {response}")
            return self._create_fallback_analysis()
        except Exception as e:
            logger.error(f"Error parsing gap analysis response: {e}")
            return self._create_fallback_analysis()
    
    def _create_fallback_analysis(self) -> Dict[str, Any]:
        """Create fallback analysis when parsing fails."""
        return {
            "is_sufficient": True,  # Assume sufficient to avoid unnecessary retries
            "missing_information": "",
            "confidence_score": 0.3
        }
    
    def _calculate_context_quality(self, context: str) -> float:
        """
        Calculate a quality score for the provided context.
        
        Args:
            context: The context to evaluate
            
        Returns:
            Quality score between 0.0 and 1.0
        """
        if not context or len(context.strip()) == 0:
            return 0.0
        
        # Basic quality indicators
        word_count = len(context.split())
        sentence_count = len([s for s in context.split('.') if s.strip()])
        
        # Quality factors
        length_score = min(word_count / 100, 1.0)  # Normalize to 100 words
        structure_score = min(sentence_count / 5, 1.0)  # Normalize to 5 sentences
        
        # Check for domain-specific terms
        domain_terms = [
            'policy', 'coverage', 'premium', 'claim', 'benefit', 'exclusion',
            'waiting', 'period', 'condition', 'limit', 'deductible', 'copay'
        ]
        domain_score = sum(1 for term in domain_terms if term.lower() in context.lower()) / len(domain_terms)
        
        # Weighted average
        quality_score = (length_score * 0.4 + structure_score * 0.3 + domain_score * 0.3)
        
        return min(quality_score, 1.0)
    
    def _generate_cache_key(self, question: str, context: str) -> str:
        """
        Generate a cache key for question-context pair.
        
        Args:
            question: The user question
            context: The context
            
        Returns:
            Cache key
        """
        import hashlib
        combined = f"{question.lower().strip()}|{context[:500].lower().strip()}"  # Use first 500 chars of context
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _analyze_context_sufficiency_sync(self, question: str, context: str) -> GapAnalysisResult:
        """
        Synchronous version of context sufficiency analysis for multiprocessing.
        
        Args:
            question: The user question
            context: The context to analyze
            
        Returns:
            Gap analysis result
        """
        
        import asyncio
        
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async analysis in this thread's event loop
            result = loop.run_until_complete(
                self.analyze_context_sufficiency(question, context)
            )
            return result
        finally:
            loop.close()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            "cache_size": len(self.gap_analysis_cache),
            "cache_maxsize": self.gap_analysis_cache.maxsize,
            "cache_hits": getattr(self.gap_analysis_cache, 'hits', 0),
            "cache_misses": getattr(self.gap_analysis_cache, 'misses', 0)
        }
    
    def clear_cache(self) -> None:
        """Clear the gap analysis cache."""
        self.gap_analysis_cache.clear()
        logger.info("Gap analysis cache cleared")
    
    def get_configuration(self) -> Dict[str, Any]:
        """
        Get current configuration.
        
        Returns:
            Dictionary with configuration settings
        """
        return {
            "model": self.model,
            "timeout_seconds": self.timeout,
            "confidence_threshold": self.confidence_threshold,
            "cache_maxsize": self.gap_analysis_cache.maxsize
        }
    
    def optimize_cache_for_workload(self, expected_questions_per_hour: int):
        """
        Optimize cache settings based on expected workload.
        
        Args:
            expected_questions_per_hour: Expected number of questions per hour
        """
        # Adjust cache size based on workload
        if expected_questions_per_hour > 1000:
            # High volume workload
            new_cache_size = 500
        elif expected_questions_per_hour > 100:
            # Medium volume workload
            new_cache_size = 300
        else:
            # Low volume workload
            new_cache_size = 100
        
        # Create new cache with optimized size
        old_cache = self.gap_analysis_cache
        self.gap_analysis_cache = LRUCache(maxsize=new_cache_size)
        
        # Transfer most recent items to new cache
        if old_cache:
            items_to_transfer = min(len(old_cache), new_cache_size)
            for key in list(old_cache.keys())[-items_to_transfer:]:
                self.gap_analysis_cache[key] = old_cache[key]
        
        logger.info(f"Cache optimized for {expected_questions_per_hour} questions/hour - new size: {new_cache_size}")
    
    async def batch_analyze_with_smart_caching(
        self, 
        questions: List[str], 
        contexts: List[str]
    ) -> List[GapAnalysisResult]:
        """
        Analyze multiple contexts with smart caching and batching optimization.
        
        Args:
            questions: List of user questions
            contexts: List of contexts corresponding to each question
            
        Returns:
            List of gap analysis results
        """
        if len(questions) != len(contexts):
            raise ValueError("Number of questions must match number of contexts")
        
        # Separate cached and non-cached items
        cached_results = {}
        non_cached_pairs = []
        non_cached_indices = []
        
        for i, (question, context) in enumerate(zip(questions, contexts)):
            cache_key = self._generate_cache_key(question, context)
            if cache_key in self.gap_analysis_cache:
                cached_results[i] = self.gap_analysis_cache[cache_key]
            else:
                non_cached_pairs.append((question, context))
                non_cached_indices.append(i)
        
        logger.info(f"Cache hit rate: {len(cached_results)}/{len(questions)} ({len(cached_results)/len(questions)*100:.1f}%)")
        
        # Process non-cached items in parallel
        if non_cached_pairs:
            non_cached_questions, non_cached_contexts = zip(*non_cached_pairs)
            non_cached_results = await self.analyze_multiple_contexts(
                list(non_cached_questions), 
                list(non_cached_contexts)
            )
        else:
            non_cached_results = []
        
        # Combine results in original order
        final_results = [None] * len(questions)
        
        # Fill in cached results
        for index, result in cached_results.items():
            final_results[index] = result
        
        # Fill in non-cached results
        for i, result in enumerate(non_cached_results):
            original_index = non_cached_indices[i]
            final_results[original_index] = result
        
        return final_results
    
    def analyze_cache_performance(self) -> Dict[str, Any]:
        """
        Analyze cache performance and provide optimization recommendations.
        
        Returns:
            Dictionary with cache performance analysis
        """
        stats = self.get_cache_stats()
        
        total_requests = stats.get("cache_hits", 0) + stats.get("cache_misses", 0)
        hit_rate = stats.get("cache_hits", 0) / max(total_requests, 1)
        
        # Performance analysis
        performance_analysis = {
            "current_stats": stats,
            "hit_rate_percent": round(hit_rate * 100, 2),
            "total_requests": total_requests,
            "cache_efficiency": "high" if hit_rate > 0.7 else "medium" if hit_rate > 0.4 else "low"
        }
        
        # Recommendations
        recommendations = []
        if hit_rate < 0.3:
            recommendations.append("Consider increasing cache size")
        if stats["cache_size"] < stats["cache_maxsize"] * 0.5:
            recommendations.append("Cache is underutilized - consider reducing size")
        if total_requests > 1000 and hit_rate > 0.8:
            recommendations.append("Excellent cache performance - consider increasing size for even better results")
        
        performance_analysis["recommendations"] = recommendations
        
        return performance_analysis
    
    async def preload_common_analyses(self, common_question_context_pairs: List[tuple]):
        """
        Preload cache with common question-context pairs.
        
        Args:
            common_question_context_pairs: List of (question, context) tuples to preload
        """
        logger.info(f"Preloading cache with {len(common_question_context_pairs)} common analyses")
        
        for question, context in common_question_context_pairs:
            try:
                result = await self.analyze_context_sufficiency(question, context)
                logger.debug(f"Preloaded analysis for: {question[:50]}...")
            except Exception as e:
                logger.warning(f"Failed to preload analysis for question: {e}")
        
        logger.info("Cache preloading completed")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics.
        
        Returns:
            Dictionary with performance metrics
        """
        cache_stats = self.get_cache_stats()
        cache_analysis = self.analyze_cache_performance()
        
        return {
            "cache_performance": cache_analysis,
            "configuration": self.get_configuration(),
            "memory_usage": self._estimate_memory_usage(),
            "optimization_status": self._get_optimization_status()
        }
    
    def _estimate_memory_usage(self) -> Dict[str, Any]:
        """Estimate memory usage of the gap analysis service."""
        import sys
        
        # Estimate cache memory usage
        cache_items = len(self.gap_analysis_cache)
        estimated_item_size = 500  # Rough estimate per cached item in bytes
        cache_memory_bytes = cache_items * estimated_item_size
        
        return {
            "cache_items": cache_items,
            "estimated_cache_memory_kb": round(cache_memory_bytes / 1024, 2),
            "cache_utilization_percent": round(cache_items / self.gap_analysis_cache.maxsize * 100, 2)
        }
    
    def _get_optimization_status(self) -> Dict[str, Any]:
        """Get current optimization status."""
        cache_stats = self.get_cache_stats()
        total_requests = cache_stats.get("cache_hits", 0) + cache_stats.get("cache_misses", 0)
        
        return {
            "cache_optimized": total_requests > 10 and cache_stats.get("cache_hits", 0) / max(total_requests, 1) > 0.5,
            "timeout_optimized": self.timeout <= 30,  # Reasonable timeout
            "confidence_threshold_set": self.confidence_threshold > 0.0,
            "ready_for_production": all([
                total_requests > 10,
                cache_stats.get("cache_hits", 0) / max(total_requests, 1) > 0.3,
                self.timeout <= 30
            ])
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check of the gap analysis service.
        
        Returns:
            Health check results
        """
        health_status = {
            "service_name": "KnowledgeGapAnalyzer",
            "status": "healthy",
            "checks": {},
            "timestamp": time.time()
        }
        
        try:
            # Test basic functionality
            test_question = "What is the test question?"
            test_context = "This is a test context for health check."
            
            start_time = time.time()
            test_result = await self.analyze_context_sufficiency(test_question, test_context)
            response_time = time.time() - start_time
            
            health_status["checks"]["basic_functionality"] = {
                "status": "pass",
                "response_time_seconds": round(response_time, 3)
            }
            
            # Check cache functionality
            cache_stats = self.get_cache_stats()
            health_status["checks"]["cache_functionality"] = {
                "status": "pass",
                "cache_size": cache_stats["cache_size"],
                "cache_maxsize": cache_stats["cache_maxsize"]
            }
            
            # Check configuration
            config = self.get_configuration()
            health_status["checks"]["configuration"] = {
                "status": "pass",
                "timeout_seconds": config["timeout_seconds"],
                "model": config["model"]
            }
            
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
            health_status["checks"]["basic_functionality"] = {
                "status": "fail",
                "error": str(e)
            }
        
        return health_status