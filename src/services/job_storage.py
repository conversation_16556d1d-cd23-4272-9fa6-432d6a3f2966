"""
Redis-based job storage service for the webhook system.
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import redis.asyncio as redis
from redis.asyncio import Redis

from src.models.job_models import Job, JobStatus, WebhookDelivery, JobMetrics, JobPriority
from src.config import settings

logger = logging.getLogger(__name__)


class JobStorage:
    """
    Redis-based job storage with TTL management and queue operations.
    """
    
    def __init__(self):
        # Redis configuration
        self.redis_host = getattr(settings, 'REDIS_HOST', 'localhost')
        self.redis_port = getattr(settings, 'REDIS_PORT', 6379)
        self.redis_db = getattr(settings, 'REDIS_DB', 0)
        self.redis_password = getattr(settings, 'REDIS_PASSWORD', None)
        
        # TTL settings
        self.job_ttl_days = getattr(settings, 'JOB_TTL_DAYS', 7)
        self.completed_job_ttl_hours = getattr(settings, 'COMPLETED_JOB_TTL_HOURS', 24)
        self.webhook_ttl_days = getattr(settings, 'WEBHOOK_TTL_DAYS', 3)
        
        # Redis client
        self.redis: Optional[Redis] = None
        
        # Key prefixes
        self.job_prefix = "job:"
        self.job_results_prefix = "job_results:"
        self.webhook_prefix = "webhook:"
        self.queue_prefix = "queue:"
        self.metrics_key = "job_metrics"
        
        logger.info("Job storage initialized")
    
    async def initialize(self):
        """Initialize Redis connection."""
        try:
            self.redis = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                password=self.redis_password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            await self.redis.ping()
            logger.info(f"Connected to Redis at {self.redis_host}:{self.redis_port}")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup Redis connection."""
        if self.redis:
            await self.redis.close()
            self.redis = None
    
    # Job operations
    
    async def store_job(self, job: Job) -> bool:
        """
        Store a job in Redis with appropriate TTL.
        
        Args:
            job: Job to store
            
        Returns:
            True if successful
        """
        try:
            job_key = f"{self.job_prefix}{job.job_id}"
            job_data = job.dict()
            
            # Convert datetime objects to ISO strings
            for field in ['created_at', 'started_at', 'completed_at']:
                if job_data.get(field):
                    job_data[field] = job_data[field].isoformat()
            
            # Store job data
            await self.redis.hset(job_key, mapping={
                "data": json.dumps(job_data),
                "status": job.status.value,
                "priority": job.request.priority.value,
                "created_at": job.created_at.isoformat()
            })
            
            # Set TTL based on job status
            if job.is_terminal:
                ttl_seconds = self.completed_job_ttl_hours * 3600
            else:
                ttl_seconds = self.job_ttl_days * 24 * 3600
            
            await self.redis.expire(job_key, ttl_seconds)
            
            # Add to status index
            status_key = f"jobs_by_status:{job.status.value}"
            await self.redis.sadd(status_key, job.job_id)
            await self.redis.expire(status_key, ttl_seconds)
            
            logger.debug(f"Stored job {job.job_id} with status {job.status}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing job {job.job_id}: {e}")
            return False
    
    async def get_job(self, job_id: str) -> Optional[Job]:
        """
        Retrieve a job by ID.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job object or None if not found
        """
        try:
            job_key = f"{self.job_prefix}{job_id}"
            job_data = await self.redis.hget(job_key, "data")
            
            if not job_data:
                return None
            
            # Parse job data
            data = json.loads(job_data)
            
            # Convert ISO strings back to datetime objects
            for field in ['created_at', 'started_at', 'completed_at']:
                if data.get(field):
                    data[field] = datetime.fromisoformat(data[field])
            
            return Job(**data)
            
        except Exception as e:
            logger.error(f"Error retrieving job {job_id}: {e}")
            return None
    
    async def update_job_status(self, job_id: str, status: JobStatus, **kwargs) -> bool:
        """
        Update job status and other fields.
        
        Args:
            job_id: Job identifier
            status: New status
            **kwargs: Additional fields to update
            
        Returns:
            True if successful
        """
        try:
            job = await self.get_job(job_id)
            if not job:
                return False
            
            # Update job fields
            job.status = status
            for key, value in kwargs.items():
                if hasattr(job, key):
                    setattr(job, key, value)
            
            # Update timestamps
            if status == JobStatus.PROCESSING and not job.started_at:
                job.started_at = datetime.utcnow()
            elif status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                job.completed_at = datetime.utcnow()
                if job.started_at:
                    job.processing_time = (job.completed_at - job.started_at).total_seconds()
            
            # Store updated job
            return await self.store_job(job)
            
        except Exception as e:
            logger.error(f"Error updating job {job_id} status: {e}")
            return False
    
    async def get_jobs_by_status(self, status: JobStatus, limit: int = 100) -> List[Job]:
        """
        Get jobs by status.
        
        Args:
            status: Job status to filter by
            limit: Maximum number of jobs to return
            
        Returns:
            List of jobs
        """
        try:
            status_key = f"jobs_by_status:{status.value}"
            job_ids = await self.redis.smembers(status_key)
            
            jobs = []
            for job_id in list(job_ids)[:limit]:
                job = await self.get_job(job_id)
                if job:
                    jobs.append(job)
            
            # Sort by priority and creation time
            jobs.sort(key=lambda j: (-j.request.priority.value, j.created_at))
            return jobs
            
        except Exception as e:
            logger.error(f"Error getting jobs by status {status}: {e}")
            return []
    
    async def delete_job(self, job_id: str) -> bool:
        """
        Delete a job and its associated data.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if successful
        """
        try:
            job_key = f"{self.job_prefix}{job_id}"
            results_key = f"{self.job_results_prefix}{job_id}"
            
            # Remove from status indexes
            for status in JobStatus:
                status_key = f"jobs_by_status:{status.value}"
                await self.redis.srem(status_key, job_id)
            
            # Delete job data
            await self.redis.delete(job_key, results_key)
            
            logger.debug(f"Deleted job {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting job {job_id}: {e}")
            return False
    
    # Queue operations
    
    async def enqueue_job(self, job: Job) -> bool:
        """
        Add job to processing queue based on priority.
        
        Args:
            job: Job to enqueue
            
        Returns:
            True if successful
        """
        try:
            queue_key = f"{self.queue_prefix}{job.request.priority.name.lower()}"
            
            # Add job to priority queue with timestamp as score
            score = job.created_at.timestamp()
            await self.redis.zadd(queue_key, {job.job_id: score})
            
            logger.debug(f"Enqueued job {job.job_id} in {job.request.priority.name} priority queue")
            return True
            
        except Exception as e:
            logger.error(f"Error enqueuing job {job.job_id}: {e}")
            return False
    
    async def dequeue_job(self, priority: Optional[JobPriority] = None) -> Optional[str]:
        """
        Get next job from queue.
        
        Args:
            priority: Specific priority queue to check (None for all)
            
        Returns:
            Job ID or None if no jobs available
        """
        try:
            # Define queue order (highest priority first)
            if priority:
                queues = [f"{self.queue_prefix}{priority.name.lower()}"]
            else:
                queues = [
                    f"{self.queue_prefix}urgent",
                    f"{self.queue_prefix}high", 
                    f"{self.queue_prefix}normal",
                    f"{self.queue_prefix}low"
                ]
            
            # Check each queue in priority order
            for queue_key in queues:
                # Get oldest job from queue
                result = await self.redis.zpopmin(queue_key, count=1)
                if result:
                    job_id = result[0][0]  # (job_id, score) tuple
                    logger.debug(f"Dequeued job {job_id} from queue")
                    return job_id
            
            return None
            
        except Exception as e:
            logger.error(f"Error dequeuing job: {e}")
            return None
    
    async def get_queue_size(self, priority: Optional[JobPriority] = None) -> int:
        """
        Get total queue size.
        
        Args:
            priority: Specific priority queue (None for all)
            
        Returns:
            Queue size
        """
        try:
            if priority:
                queue_key = f"{self.queue_prefix}{priority.name.lower()}"
                return await self.redis.zcard(queue_key)
            else:
                total = 0
                for p in JobPriority:
                    queue_key = f"{self.queue_prefix}{p.name.lower()}"
                    total += await self.redis.zcard(queue_key)
                return total
                
        except Exception as e:
            logger.error(f"Error getting queue size: {e}")
            return 0
    
    # Webhook delivery operations
    
    async def store_webhook_delivery(self, delivery: WebhookDelivery) -> bool:
        """
        Store webhook delivery record.
        
        Args:
            delivery: Webhook delivery to store
            
        Returns:
            True if successful
        """
        try:
            webhook_key = f"{self.webhook_prefix}{delivery.delivery_id}"
            delivery_data = delivery.dict()
            
            # Convert datetime objects to ISO strings
            for field in ['created_at', 'last_attempt', 'next_attempt', 'delivered_at']:
                if delivery_data.get(field):
                    delivery_data[field] = delivery_data[field].isoformat()
            
            await self.redis.hset(webhook_key, mapping={
                "data": json.dumps(delivery_data),
                "status": delivery.status.value,
                "job_id": delivery.job_id
            })
            
            # Set TTL
            ttl_seconds = self.webhook_ttl_days * 24 * 3600
            await self.redis.expire(webhook_key, ttl_seconds)
            
            # Add to job's webhook list
            job_webhooks_key = f"job_webhooks:{delivery.job_id}"
            await self.redis.sadd(job_webhooks_key, delivery.delivery_id)
            await self.redis.expire(job_webhooks_key, ttl_seconds)
            
            return True
            
        except Exception as e:
            logger.error(f"Error storing webhook delivery {delivery.delivery_id}: {e}")
            return False
    
    async def get_webhook_delivery(self, delivery_id: str) -> Optional[WebhookDelivery]:
        """
        Get webhook delivery by ID.
        
        Args:
            delivery_id: Delivery identifier
            
        Returns:
            WebhookDelivery object or None
        """
        try:
            webhook_key = f"{self.webhook_prefix}{delivery_id}"
            delivery_data = await self.redis.hget(webhook_key, "data")
            
            if not delivery_data:
                return None
            
            data = json.loads(delivery_data)
            
            # Convert ISO strings back to datetime objects
            for field in ['created_at', 'last_attempt', 'next_attempt', 'delivered_at']:
                if data.get(field):
                    data[field] = datetime.fromisoformat(data[field])
            
            return WebhookDelivery(**data)
            
        except Exception as e:
            logger.error(f"Error retrieving webhook delivery {delivery_id}: {e}")
            return None
    
    async def get_pending_webhook_deliveries(self, limit: int = 100) -> List[WebhookDelivery]:
        """
        Get pending webhook deliveries that should be retried.
        
        Args:
            limit: Maximum number of deliveries to return
            
        Returns:
            List of webhook deliveries
        """
        try:
            # Scan for webhook keys
            deliveries = []
            async for key in self.redis.scan_iter(match=f"{self.webhook_prefix}*"):
                if len(deliveries) >= limit:
                    break
                
                delivery_id = key.replace(self.webhook_prefix, "")
                delivery = await self.get_webhook_delivery(delivery_id)
                
                if delivery and delivery.should_retry:
                    deliveries.append(delivery)
            
            # Sort by next attempt time
            deliveries.sort(key=lambda d: d.next_attempt or datetime.min)
            return deliveries
            
        except Exception as e:
            logger.error(f"Error getting pending webhook deliveries: {e}")
            return []
    
    # Metrics operations
    
    async def update_metrics(self) -> JobMetrics:
        """
        Update and return current job metrics.
        
        Returns:
            Current job metrics
        """
        try:
            metrics = JobMetrics()
            
            # Count jobs by status
            for status in JobStatus:
                status_key = f"jobs_by_status:{status.value}"
                count = await self.redis.scard(status_key)
                
                if status == JobStatus.QUEUED:
                    metrics.queued_jobs = count
                elif status == JobStatus.PROCESSING:
                    metrics.processing_jobs = count
                elif status == JobStatus.COMPLETED:
                    metrics.completed_jobs = count
                elif status == JobStatus.FAILED:
                    metrics.failed_jobs = count
                elif status == JobStatus.CANCELLED:
                    metrics.cancelled_jobs = count
            
            metrics.total_jobs = (
                metrics.queued_jobs + metrics.processing_jobs + 
                metrics.completed_jobs + metrics.failed_jobs + metrics.cancelled_jobs
            )
            
            # Calculate success rate
            if metrics.total_jobs > 0:
                metrics.success_rate = (metrics.completed_jobs / metrics.total_jobs) * 100
            
            # Store metrics
            await self.redis.hset(self.metrics_key, mapping=metrics.dict())
            await self.redis.expire(self.metrics_key, 3600)  # 1 hour TTL
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error updating metrics: {e}")
            return JobMetrics()
    
    async def cleanup_expired_jobs(self) -> int:
        """
        Clean up expired jobs and associated data.
        
        Returns:
            Number of jobs cleaned up
        """
        try:
            cleaned_count = 0
            
            # Scan for job keys
            async for key in self.redis.scan_iter(match=f"{self.job_prefix}*"):
                job_id = key.replace(self.job_prefix, "")
                job = await self.get_job(job_id)
                
                if job and job.is_expired:
                    await self.delete_job(job_id)
                    cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} expired jobs")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired jobs: {e}")
            return 0
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check Redis connection health.
        
        Returns:
            Health status information
        """
        try:
            # Test Redis connection
            await self.redis.ping()
            
            # Get basic stats
            info = await self.redis.info()
            
            return {
                "status": "healthy",
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory_human": info.get("used_memory_human"),
                "total_commands_processed": info.get("total_commands_processed")
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global job storage instance
job_storage = JobStorage()