"""LRU document cache for processed documents using cachetools."""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import hashlib
import pickle
from dataclasses import dataclass
from cachetools import LRUCache

from src.models.schemas import DocumentChunk

logger = logging.getLogger(__name__)


@dataclass
class CachedDocument:
    """Represents a cached document with metadata."""
    faiss_index: Any
    chunks: List[DocumentChunk]
    metadata: Dict[str, Any]
    cached_at: datetime
    access_count: int
    cache_key: str


class DocumentCache:
    """LRU cache for processed documents using cachetools."""
    
    def __init__(self, maxsize: int = 100, ttl_hours: int = 24):
        """
        Initialize document cache.
        
        Args:
            maxsize: Maximum number of documents to cache
            ttl_hours: Time-to-live for cached documents in hours
        """
        self.cache = LRUCache(maxsize=maxsize)
        self.ttl_hours = ttl_hours
        self.access_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
        
        logger.info(f"Document cache initialized with maxsize={maxsize}, ttl={ttl_hours}h")
    
    def _generate_cache_key(self, url: str) -> str:
        """Generate a cache key from document URL."""
        # Use SHA-256 hash of URL for consistent key generation
        return hashlib.sha256(url.encode('utf-8')).hexdigest()[:16]
    
    async def get_cached_document(self, url: str) -> Optional[CachedDocument]:
        """
        Retrieve a cached document by URL.
        
        Args:
            url: Document URL
            
        Returns:
            CachedDocument if found and valid, None otherwise
        """
        try:
            self.access_stats['total_requests'] += 1
            cache_key = self._generate_cache_key(url)
            
            cached_doc = self.cache.get(cache_key)
            
            if cached_doc is None:
                self.access_stats['misses'] += 1
                logger.debug(f"Cache miss for document: {url}")
                return None
            
            # Check TTL
            if self._is_expired(cached_doc):
                logger.debug(f"Cache entry expired for document: {url}")
                del self.cache[cache_key]
                self.access_stats['misses'] += 1
                return None
            
            # Update access count and stats
            cached_doc.access_count += 1
            self.access_stats['hits'] += 1
            
            logger.debug(f"Cache hit for document: {url} (accessed {cached_doc.access_count} times)")
            return cached_doc
            
        except Exception as e:
            logger.error(f"Error retrieving cached document for {url}: {e}")
            self.access_stats['misses'] += 1
            return None
    
    async def cache_document(
        self, 
        url: str, 
        faiss_index: Any, 
        chunks: List[DocumentChunk],
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Cache a processed document.
        
        Args:
            url: Document URL
            faiss_index: FAISS index for the document
            chunks: Document chunks
            metadata: Additional metadata
        """
        try:
            cache_key = self._generate_cache_key(url)
            
            # Check if we're evicting an existing entry
            if cache_key in self.cache:
                logger.debug(f"Updating existing cache entry for: {url}")
            elif len(self.cache) >= self.cache.maxsize:
                self.access_stats['evictions'] += 1
                logger.debug(f"Cache full, will evict LRU entry for: {url}")
            
            cached_doc = CachedDocument(
                faiss_index=faiss_index,
                chunks=chunks,
                metadata=metadata or {},
                cached_at=datetime.utcnow(),
                access_count=0,
                cache_key=cache_key
            )
            
            # Add processing metadata
            cached_doc.metadata.update({
                'url': url,
                'cached_at': cached_doc.cached_at.isoformat(),
                'chunks_count': len(chunks),
                'cache_key': cache_key
            })
            
            self.cache[cache_key] = cached_doc
            
            logger.info(f"Cached document: {url} ({len(chunks)} chunks)")
            
        except Exception as e:
            logger.error(f"Error caching document {url}: {e}")
    
    def _is_expired(self, cached_doc: CachedDocument) -> bool:
        """Check if a cached document has expired."""
        if self.ttl_hours <= 0:
            return False  # No expiration
        
        age_hours = (datetime.utcnow() - cached_doc.cached_at).total_seconds() / 3600
        return age_hours > self.ttl_hours
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.access_stats['total_requests']
        hit_rate = (self.access_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_size': len(self.cache),
            'max_size': self.cache.maxsize,
            'hit_rate_percent': round(hit_rate, 2),
            'total_requests': total_requests,
            'hits': self.access_stats['hits'],
            'misses': self.access_stats['misses'],
            'evictions': self.access_stats['evictions'],
            'ttl_hours': self.ttl_hours,
            'memory_usage': self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> Dict[str, Any]:
        """Estimate memory usage of the cache."""
        try:
            total_chunks = 0
            total_docs = len(self.cache)
            
            for cached_doc in self.cache.values():
                total_chunks += len(cached_doc.chunks)
            
            # Rough estimation
            estimated_mb = (total_chunks * 0.001) + (total_docs * 0.1)  # Very rough estimate
            
            return {
                'estimated_mb': round(estimated_mb, 2),
                'total_documents': total_docs,
                'total_chunks': total_chunks
            }
            
        except Exception as e:
            logger.error(f"Error estimating memory usage: {e}")
            return {'error': str(e)}
    
    def clear_cache(self) -> None:
        """Clear all cached documents."""
        try:
            cache_size = len(self.cache)
            self.cache.clear()
            
            # Reset stats
            self.access_stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'total_requests': 0
            }
            
            logger.info(f"Cache cleared. Removed {cache_size} documents")
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
    
    def remove_document(self, url: str) -> bool:
        """
        Remove a specific document from cache.
        
        Args:
            url: Document URL to remove
            
        Returns:
            True if document was removed, False if not found
        """
        try:
            cache_key = self._generate_cache_key(url)
            
            if cache_key in self.cache:
                del self.cache[cache_key]
                logger.info(f"Removed document from cache: {url}")
                return True
            else:
                logger.debug(f"Document not found in cache: {url}")
                return False
                
        except Exception as e:
            logger.error(f"Error removing document from cache {url}: {e}")
            return False
    
    def get_cached_urls(self) -> List[str]:
        """Get list of all cached document URLs."""
        try:
            urls = []
            for cached_doc in self.cache.values():
                url = cached_doc.metadata.get('url', 'unknown')
                urls.append(url)
            return urls
            
        except Exception as e:
            logger.error(f"Error getting cached URLs: {e}")
            return []
    
    def get_document_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a cached document.
        
        Args:
            url: Document URL
            
        Returns:
            Document information or None if not cached
        """
        try:
            cache_key = self._generate_cache_key(url)
            cached_doc = self.cache.get(cache_key)
            
            if cached_doc is None:
                return None
            
            return {
                'url': url,
                'cache_key': cache_key,
                'cached_at': cached_doc.cached_at.isoformat(),
                'access_count': cached_doc.access_count,
                'chunks_count': len(cached_doc.chunks),
                'expired': self._is_expired(cached_doc),
                'metadata': cached_doc.metadata
            }
            
        except Exception as e:
            logger.error(f"Error getting document info for {url}: {e}")
            return None
    
    async def cleanup_expired(self) -> int:
        """
        Remove expired documents from cache.
        
        Returns:
            Number of documents removed
        """
        try:
            if self.ttl_hours <= 0:
                return 0  # No expiration configured
            
            expired_keys = []
            
            for cache_key, cached_doc in self.cache.items():
                if self._is_expired(cached_doc):
                    expired_keys.append(cache_key)
            
            # Remove expired documents
            for cache_key in expired_keys:
                del self.cache[cache_key]
            
            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired documents from cache")
            
            return len(expired_keys)
            
        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")
            return 0


# Global document cache instance
document_cache = DocumentCache(
    maxsize=100,  # Cache up to 100 documents
    ttl_hours=24  # Documents expire after 24 hours
)


# Helper functions for easy access
async def get_cached_document(url: str) -> Optional[CachedDocument]:
    """Get a cached document by URL."""
    return await document_cache.get_cached_document(url)


async def cache_document(url: str, faiss_index: Any, chunks: List[DocumentChunk], metadata: Optional[Dict[str, Any]] = None) -> None:
    """Cache a processed document."""
    await document_cache.cache_document(url, faiss_index, chunks, metadata)


def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics."""
    return document_cache.get_cache_stats()


def clear_document_cache() -> None:
    """Clear the document cache."""
    document_cache.clear_cache()