"""Qwen3 L1 Reranker Service using Qwen3-Reranker-0.6B model for improved relevance scoring."""

import logging
import time
from typing import List, Dict, Any, Optional, Tuple
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

from src.config import settings
from src.models.schemas import RankedChunk, DocumentChunk
from src.services.multiprocessing_manager import mp_manager, process_reranking_parallel

logger = logging.getLogger(__name__)


class Qwen3RerankerService:
    """Service for L1 reranking using Qwen3-Reranker-0.6B model."""
    
    def __init__(self):
        self.model_name = getattr(settings, 'QWEN3_RERANKER_MODEL', 'Qwen/Qwen3-Reranker-0.6B')
        self.max_length = getattr(settings, 'QWEN3_MAX_LENGTH', 8192)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Model and tokenizer will be loaded from cache
        self.model = None
        self.tokenizer = None
        self.token_true_id = None
        self.token_false_id = None
        self.prefix_tokens = None
        self.suffix_tokens = None
        
        # Default instruction for reranking
        self.default_instruction = 'Given a web search query, retrieve relevant passages that answer the query'
        
        # Fallback configuration
        self.use_provence_fallback = True
        self._custom_batch_size = None
        
        # Initialize model components
        self._initialize_model_components()
        
    def _initialize_model_components(self):
        """Initialize model components from cache or load them."""
        try:
            # Try to get from model cache first
            cached_model = self.get_cached_qwen3_model()
            cached_tokenizer = self.get_cached_qwen3_tokenizer()
            
            if cached_model and cached_tokenizer:
                self.model = cached_model
                self.tokenizer = cached_tokenizer
                logger.info("Using cached Qwen3 model and tokenizer")
            else:
                # Load model and tokenizer
                logger.info(f"Loading Qwen3 model: {self.model_name}")
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, padding_side='left')
                self.model = AutoModelForCausalLM.from_pretrained(self.model_name).eval()
                
                # Move to appropriate device
                self.model = self.model.to(self.device)
                logger.info(f"Qwen3 model loaded on device: {self.device}")
            
            # Initialize token IDs and prefixes
            self.token_false_id = self.tokenizer.convert_tokens_to_ids("no")
            self.token_true_id = self.tokenizer.convert_tokens_to_ids("yes")
            
            # Define system prompt and format
            prefix = "<|im_start|>system\nJudge whether the Document meets the requirements based on the Query and the Instruct provided. Note that the answer can only be \"yes\" or \"no\".<|im_end|>\n<|im_start|>user\n"
            suffix = "<|im_end|>\n<|im_start|>assistant\n<think>\n\n</think>\n\n"
            
            self.prefix_tokens = self.tokenizer.encode(prefix, add_special_tokens=False)
            self.suffix_tokens = self.tokenizer.encode(suffix, add_special_tokens=False)
            
            logger.info("Qwen3 reranker service initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Qwen3 reranker: {str(e)}")
            raise
    
    def get_cached_qwen3_model(self):
        """Get cached Qwen3 model from model cache."""
        try:
            from src.services.model_cache import get_qwen3_reranker
            return get_qwen3_reranker()
        except Exception:
            return None
    
    def get_cached_qwen3_tokenizer(self):
        """Get cached Qwen3 tokenizer from model cache."""
        try:
            from src.services.model_cache import get_qwen3_tokenizer
            return get_qwen3_tokenizer()
        except Exception:
            return None
    
    def _format_instruction(self, instruction: Optional[str], query: str, doc: str) -> str:
        """
        Format instruction for Qwen3 model.
        
        Args:
            instruction: Task instruction (optional)
            query: Search query
            doc: Document content
            
        Returns:
            Formatted instruction string
        """
        if instruction is None:
            instruction = self.default_instruction
        
        return f"<Instruct>: {instruction}\n<Query>: {query}\n<Document>: {doc}"
    
    def _process_inputs(self, pairs: List[str]) -> Dict[str, torch.Tensor]:
        """
        Process input pairs for the model.
        
        Args:
            pairs: List of formatted instruction strings
            
        Returns:
            Processed inputs as tensors
        """
        try:
            # Tokenize inputs
            inputs = self.tokenizer(
                pairs, 
                padding=False, 
                truncation='longest_first',
                return_attention_mask=False, 
                max_length=self.max_length - len(self.prefix_tokens) - len(self.suffix_tokens)
            )
            
            # Add prefix and suffix tokens
            for i, token_ids in enumerate(inputs['input_ids']):
                inputs['input_ids'][i] = self.prefix_tokens + token_ids + self.suffix_tokens
            
            # Pad inputs
            inputs = self.tokenizer.pad(inputs, padding=True, return_tensors="pt", max_length=self.max_length)
            
            # Move to device
            for key in inputs:
                inputs[key] = inputs[key].to(self.device)
            
            return inputs
            
        except Exception as e:
            logger.error(f"Error processing inputs: {str(e)}")
            raise
    
    @torch.no_grad()
    def _compute_logits(self, inputs: Dict[str, torch.Tensor]) -> List[float]:
        """
        Compute relevance scores from model logits.
        
        Args:
            inputs: Processed model inputs
            
        Returns:
            List of relevance scores
        """
        try:
            # Get model outputs
            outputs = self.model(**inputs)
            batch_scores = outputs.logits[:, -1, :]
            
            # Extract true/false logits
            true_vector = batch_scores[:, self.token_true_id]
            false_vector = batch_scores[:, self.token_false_id]
            
            # Stack and apply softmax
            batch_scores = torch.stack([false_vector, true_vector], dim=1)
            batch_scores = torch.nn.functional.log_softmax(batch_scores, dim=1)
            
            # Get probability scores for "yes" (relevant)
            scores = batch_scores[:, 1].exp().tolist()
            
            return scores
            
        except Exception as e:
            logger.error(f"Error computing logits: {str(e)}")
            raise
    
    async def rerank_chunks(
        self, 
        query: str, 
        chunks: List[str], 
        instruction: Optional[str] = None
    ) -> List[RankedChunk]:
        """
        Rerank chunks using Qwen3 model.
        
        Args:
            query: Search query
            chunks: List of document chunks to rerank
            instruction: Optional task instruction
            
        Returns:
            List of ranked chunks with scores
        """
        try:
            if not chunks:
                return []
            
            start_time = time.time()
            logger.info(f"Reranking {len(chunks)} chunks with Qwen3")
            
            # Format inputs
            pairs = [
                self._format_instruction(instruction, query, chunk)
                for chunk in chunks
            ]
            
            # Process in batches to manage memory
            batch_size = self._calculate_optimal_batch_size(len(chunks))
            all_scores = []
            
            for i in range(0, len(pairs), batch_size):
                batch_pairs = pairs[i:i + batch_size]
                batch_inputs = self._process_inputs(batch_pairs)
                batch_scores = self._compute_logits(batch_inputs)
                all_scores.extend(batch_scores)
            
            # Create ranked chunks
            ranked_chunks = []
            for i, (chunk, score) in enumerate(zip(chunks, all_scores)):
                # Create DocumentChunk
                doc_chunk = DocumentChunk(
                    id=f"chunk_{i}",
                    content=chunk,
                    metadata={"original_index": i}
                )
                
                # Create RankedChunk
                ranked_chunk = RankedChunk(
                    chunk=doc_chunk,
                    score=score,
                    rank=i + 1,  # Will be updated after sorting
                    confidence=score,  # Use score as confidence
                    relevance_explanation=f"Qwen3 relevance score: {score:.4f}",
                    adaptive_threshold=None  # Will be set by adaptive reranker
                )
                ranked_chunks.append(ranked_chunk)
            
            # Sort by score (descending)
            ranked_chunks.sort(key=lambda x: x.score, reverse=True)
            
            # Update ranks
            for i, chunk in enumerate(ranked_chunks):
                chunk.rank = i + 1
            
            processing_time = time.time() - start_time
            logger.info(f"Qwen3 reranking completed in {processing_time:.2f}s")
            
            return ranked_chunks
            
        except Exception as e:
            logger.error(f"Error in Qwen3 reranking: {str(e)}")
            # Return chunks with default scores as fallback
            return self._create_fallback_ranking(chunks)
    
    def _calculate_optimal_batch_size(self, num_chunks: int) -> int:
        """
        Calculate optimal batch size based on available memory and number of chunks.
        
        Args:
            num_chunks: Total number of chunks
            
        Returns:
            Optimal batch size
        """
        # Base batch size depending on device
        if self.device.type == 'cuda':
            base_batch_size = 8  # Conservative for GPU memory
        else:
            base_batch_size = 4   # Even more conservative for CPU
        
        # Adjust based on number of chunks
        if num_chunks <= 10:
            return min(num_chunks, base_batch_size)
        elif num_chunks <= 50:
            return base_batch_size
        else:
            return max(2, base_batch_size // 2)  # Reduce for large batches
    
    def _create_fallback_ranking(self, chunks: List[str]) -> List[RankedChunk]:
        """
        Create fallback ranking when Qwen3 processing fails.
        
        Args:
            chunks: List of chunks
            
        Returns:
            List of ranked chunks with default scores
        """
        ranked_chunks = []
        for i, chunk in enumerate(chunks):
            doc_chunk = DocumentChunk(
                id=f"fallback_chunk_{i}",
                content=chunk,
                metadata={"original_index": i, "fallback": True}
            )
            
            # Assign decreasing scores
            score = 1.0 - (i * 0.1)  # Simple decreasing score
            
            ranked_chunk = RankedChunk(
                chunk=doc_chunk,
                score=max(0.1, score),  # Minimum score of 0.1
                rank=i + 1,
                confidence=0.5,  # Low confidence for fallback
                relevance_explanation="Fallback ranking (Qwen3 failed)",
                adaptive_threshold=None
            )
            ranked_chunks.append(ranked_chunk)
        
        return ranked_chunks
    
    async def rerank_batches_parallel(
        self, 
        queries: List[str], 
        chunk_lists: List[List[str]],
        instruction: Optional[str] = None
    ) -> List[List[RankedChunk]]:
        """
        Rerank multiple query-chunk pairs in parallel.
        
        Args:
            queries: List of search queries
            chunk_lists: List of chunk lists corresponding to each query
            instruction: Optional task instruction
            
        Returns:
            List of ranked chunk lists
        """
        try:
            logger.info(f"Parallel reranking for {len(queries)} queries")
            
            # Prepare arguments for parallel processing
            rerank_args = [
                (query, chunks, instruction)
                for query, chunks in zip(queries, chunk_lists)
            ]
            
            # Use multiprocessing for CPU-intensive reranking
            results = await process_reranking_parallel(
                self._rerank_chunks_sync,
                rerank_args
            )
            
            logger.info(f"Parallel reranking completed for {len(results)} queries")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel reranking: {str(e)}")
            # Fallback: process sequentially
            results = []
            for query, chunks in zip(queries, chunk_lists):
                result = await self.rerank_chunks(query, chunks, instruction)
                results.append(result)
            return results
    
    def _rerank_chunks_sync(self, args: Tuple[str, List[str], Optional[str]]) -> List[RankedChunk]:
        """
        Synchronous version of chunk reranking for multiprocessing.
        
        Args:
            args: Tuple of (query, chunks, instruction)
            
        Returns:
            List of ranked chunks
        """
        query, chunks, instruction = args
        
        import asyncio
        
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async reranking in this thread's event loop
            result = loop.run_until_complete(
                self.rerank_chunks(query, chunks, instruction)
            )
            return result
        finally:
            loop.close()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "max_length": self.max_length,
            "device": str(self.device),
            "model_loaded": self.model is not None,
            "tokenizer_loaded": self.tokenizer is not None,
            "token_true_id": self.token_true_id,
            "token_false_id": self.token_false_id
        }
    
    def optimize_memory_usage(self):
        """Optimize memory usage by clearing cache if needed."""
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                logger.info("CUDA cache cleared for memory optimization")
        except Exception as e:
            logger.warning(f"Could not clear CUDA cache: {e}")
    
    def validate_model_setup(self) -> bool:
        """
        Validate that the model is properly set up.
        
        Returns:
            True if model is ready for use
        """
        try:
            required_components = [
                self.model,
                self.tokenizer,
                self.token_true_id,
                self.token_false_id,
                self.prefix_tokens,
                self.suffix_tokens
            ]
            
            if not all(comp is not None for comp in required_components):
                logger.error("Some Qwen3 model components are not initialized")
                return False
            
            # Test with a simple input
            test_pair = self._format_instruction(None, "test query", "test document")
            test_inputs = self._process_inputs([test_pair])
            test_scores = self._compute_logits(test_inputs)
            
            if not test_scores or not isinstance(test_scores[0], (int, float)):
                logger.error("Qwen3 model test failed")
                return False
            
            logger.info("Qwen3 model validation successful")
            return True
            
        except Exception as e:
            logger.error(f"Qwen3 model validation failed: {e}")
            return False
    
    def enable_fallback_to_provence(self, enable: bool = True):
        """
        Enable or disable fallback to Provence reranker when Qwen3 fails.
        
        Args:
            enable: Whether to enable Provence fallback
        """
        self.use_provence_fallback = enable
        logger.info(f"Provence fallback {'enabled' if enable else 'disabled'}")
    
    async def rerank_with_fallback(
        self, 
        query: str, 
        chunks: List[str], 
        instruction: Optional[str] = None
    ) -> List[RankedChunk]:
        """
        Rerank chunks with automatic fallback to Provence reranker if Qwen3 fails.
        
        Args:
            query: Search query
            chunks: List of document chunks to rerank
            instruction: Optional task instruction
            
        Returns:
            List of ranked chunks with scores
        """
        try:
            # First try Qwen3 reranking
            if self.validate_model_setup():
                return await self.rerank_chunks(query, chunks, instruction)
            else:
                logger.warning("Qwen3 model not properly set up, falling back to Provence")
                return await self._fallback_to_provence(query, chunks)
                
        except Exception as e:
            logger.error(f"Qwen3 reranking failed: {e}")
            logger.info("Attempting fallback to Provence reranker")
            return await self._fallback_to_provence(query, chunks)
    
    async def _fallback_to_provence(self, query: str, chunks: List[str]) -> List[RankedChunk]:
        """
        Fallback to Provence reranker when Qwen3 fails.
        
        Args:
            query: Search query
            chunks: List of chunks to rerank
            
        Returns:
            List of ranked chunks from Provence reranker
        """
        try:
            from src.services.reranker import AdaptiveNaverProvenceReranker
            
            provence_reranker = AdaptiveNaverProvenceReranker()
            
            # Convert chunks to the format expected by Provence reranker
            doc_chunks = []
            for i, chunk in enumerate(chunks):
                doc_chunk = DocumentChunk(
                    id=f"chunk_{i}",
                    content=chunk,
                    metadata={"original_index": i}
                )
                doc_chunks.append(doc_chunk)
            
            # Use Provence reranker - it expects strings, not DocumentChunk objects
            ranked_chunks = await provence_reranker.adaptive_rerank(query, chunks)
            
            # Update metadata to indicate fallback was used
            for chunk in ranked_chunks:
                chunk.chunk.metadata["reranker_used"] = "provence_fallback"
                chunk.relevance_explanation = f"Provence fallback score: {chunk.score:.4f}"
            
            logger.info(f"Successfully used Provence fallback for {len(ranked_chunks)} chunks")
            return ranked_chunks
            
        except Exception as e:
            logger.error(f"Provence fallback also failed: {e}")
            # Final fallback to simple ranking
            return self._create_fallback_ranking(chunks)
    
    def optimize_for_batch_processing(self, batch_size: Optional[int] = None):
        """
        Optimize the service for batch processing.
        
        Args:
            batch_size: Optional custom batch size
        """
        if batch_size:
            self._custom_batch_size = batch_size
            logger.info(f"Custom batch size set to {batch_size}")
        
        # Optimize memory usage
        self.optimize_memory_usage()
        
        # Pre-warm the model with a small test
        try:
            test_pair = self._format_instruction(None, "test", "test")
            test_inputs = self._process_inputs([test_pair])
            _ = self._compute_logits(test_inputs)
            logger.info("Model pre-warmed for batch processing")
        except Exception as e:
            logger.warning(f"Model pre-warming failed: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for the reranker.
        
        Returns:
            Dictionary with performance stats
        """
        return {
            "model_info": self.get_model_info(),
            "device": str(self.device),
            "max_length": self.max_length,
            "model_validated": self.validate_model_setup(),
            "memory_usage": self._get_memory_usage(),
            "batch_size_calculator": "dynamic"
        }
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        stats = {
            "process_memory_mb": round(memory_info.rss / 1024 / 1024, 2),
            "process_memory_percent": round(process.memory_percent(), 2)
        }
        
        # Add GPU memory if available
        if torch.cuda.is_available():
            try:
                gpu_memory = torch.cuda.memory_allocated() / 1024 / 1024
                gpu_memory_cached = torch.cuda.memory_reserved() / 1024 / 1024
                stats.update({
                    "gpu_memory_allocated_mb": round(gpu_memory, 2),
                    "gpu_memory_cached_mb": round(gpu_memory_cached, 2)
                })
            except Exception:
                pass
        
        return stats
    
    async def benchmark_performance(self, test_queries: List[str], test_chunks: List[List[str]]) -> Dict[str, Any]:
        """
        Benchmark the performance of the reranker.
        
        Args:
            test_queries: List of test queries
            test_chunks: List of chunk lists for each query
            
        Returns:
            Benchmark results
        """
        import time
        
        if len(test_queries) != len(test_chunks):
            raise ValueError("Number of queries must match number of chunk lists")
        
        logger.info(f"Starting performance benchmark with {len(test_queries)} queries")
        
        start_time = time.time()
        successful_rerankings = 0
        failed_rerankings = 0
        total_chunks_processed = 0
        processing_times = []
        
        for query, chunks in zip(test_queries, test_chunks):
            chunk_start_time = time.time()
            try:
                result = await self.rerank_with_fallback(query, chunks)
                successful_rerankings += 1
                total_chunks_processed += len(chunks)
                processing_times.append(time.time() - chunk_start_time)
            except Exception as e:
                failed_rerankings += 1
                logger.error(f"Benchmark reranking failed: {e}")
        
        total_time = time.time() - start_time
        
        return {
            "total_queries": len(test_queries),
            "successful_rerankings": successful_rerankings,
            "failed_rerankings": failed_rerankings,
            "success_rate_percent": round(successful_rerankings / len(test_queries) * 100, 2),
            "total_chunks_processed": total_chunks_processed,
            "total_time_seconds": round(total_time, 2),
            "average_time_per_query": round(sum(processing_times) / max(len(processing_times), 1), 3),
            "chunks_per_second": round(total_chunks_processed / total_time, 2),
            "memory_stats": self._get_memory_usage()
        }