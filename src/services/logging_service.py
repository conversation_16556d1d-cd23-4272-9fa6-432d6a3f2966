"""Request and response logging service for monitoring and debugging."""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import aiofiles
from logging.handlers import RotatingFileHandler

from src.config import settings
from src.models.schemas import QueryRequest, QueryResponse

logger = logging.getLogger(__name__)


class RequestLogger:
    """Service for logging all incoming requests and responses with structured format."""
    
    def __init__(self):
        self.log_file_path = Path(settings.LOG_FILE_PATH if hasattr(settings, 'LOG_FILE_PATH') else "logs/requests.log")
        self.log_level = getattr(settings, 'LOG_LEVEL', 'INFO')
        self.log_rotation_size_mb = getattr(settings, 'LOG_ROTATION_SIZE_MB', 100)
        self.log_backup_count = getattr(settings, 'LOG_BACKUP_COUNT', 5)
        self.structured_logging = getattr(settings, 'STRUCTURED_LOGGING', True)
        
        # Ensure log directory exists
        self.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Setup structured logger
        self._setup_structured_logger()
        
    def _setup_structured_logger(self):
        """Setup structured logging with rotation."""
        try:
            # Create structured logger
            self.structured_logger = logging.getLogger('request_logger')
            self.structured_logger.setLevel(getattr(logging, self.log_level.upper()))
            
            # Remove existing handlers to avoid duplicates
            for handler in self.structured_logger.handlers[:]:
                self.structured_logger.removeHandler(handler)
            
            # Create rotating file handler
            handler = RotatingFileHandler(
                self.log_file_path,
                maxBytes=self.log_rotation_size_mb * 1024 * 1024,  # Convert MB to bytes
                backupCount=self.log_backup_count,
                encoding='utf-8'
            )
            
            # Set formatter for structured logging
            if self.structured_logging:
                formatter = logging.Formatter('%(message)s')  # JSON will be the message
            else:
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            
            handler.setFormatter(formatter)
            self.structured_logger.addHandler(handler)
            
            # Prevent propagation to root logger
            self.structured_logger.propagate = False
            
            logger.info(f"Request logger initialized with file: {self.log_file_path}")
            
        except Exception as e:
            logger.error(f"Failed to setup structured logger: {e}")
            raise
    
    def generate_request_id(self) -> str:
        """Generate a unique request ID."""
        return str(uuid.uuid4())
    
    async def log_request(
        self, 
        request: QueryRequest, 
        request_id: str,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """
        Log incoming request details.
        
        Args:
            request: The incoming request
            request_id: Unique request identifier
            client_ip: Client IP address
            user_agent: Client user agent
        """
        try:
            log_entry = {
                "event_type": "request",
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "client_ip": client_ip,
                "user_agent": user_agent,
                "request_data": {
                    "documents_url": request.documents,
                    "questions_count": len(request.questions),
                    "questions": request.questions[:3] if len(request.questions) > 3 else request.questions,  # Log first 3 questions
                    "questions_truncated": len(request.questions) > 3
                }
            }
            
            await self._write_log_entry(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging request {request_id}: {e}")
    
    async def log_response(
        self, 
        response: QueryResponse, 
        request_id: str,
        processing_time: float,
        success: bool = True,
        cache_hits: int = 0,
        worker_stats: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log response details and processing metrics.
        
        Args:
            response: The response object
            request_id: Unique request identifier
            processing_time: Total processing time in seconds
            success: Whether the request was successful
            cache_hits: Number of cache hits during processing
            worker_stats: Statistics about worker pool usage
        """
        try:
            log_entry = {
                "event_type": "response",
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "success": success,
                "processing_time_seconds": round(processing_time, 3),
                "cache_hits": cache_hits,
                "response_data": {
                    "answers_count": len(response.answers),
                    "answers_preview": [
                        answer[:100] + "..." if len(answer) > 100 else answer 
                        for answer in response.answers[:2]  # Preview first 2 answers
                    ]
                },
                "performance_metrics": {
                    "worker_stats": worker_stats or {},
                    "cache_efficiency": cache_hits > 0
                }
            }
            
            await self._write_log_entry(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging response {request_id}: {e}")
    
    async def log_error(
        self, 
        error: Exception, 
        request_id: str,
        context: Dict[str, Any],
        processing_time: Optional[float] = None
    ) -> None:
        """
        Log error details with context.
        
        Args:
            error: The exception that occurred
            request_id: Unique request identifier
            context: Additional context about the error
            processing_time: Time spent before error occurred
        """
        try:
            log_entry = {
                "event_type": "error",
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "error_type": type(error).__name__,
                "error_message": str(error),
                "processing_time_seconds": round(processing_time, 3) if processing_time else None,
                "context": context,
                "stack_trace": self._get_stack_trace(error) if logger.isEnabledFor(logging.DEBUG) else None
            }
            
            await self._write_log_entry(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging error for request {request_id}: {e}")
    
    async def log_performance_metrics(
        self,
        request_id: str,
        metrics: Dict[str, Any]
    ) -> None:
        """
        Log detailed performance metrics.
        
        Args:
            request_id: Unique request identifier
            metrics: Performance metrics dictionary
        """
        try:
            log_entry = {
                "event_type": "performance_metrics",
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "metrics": metrics
            }
            
            await self._write_log_entry(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging performance metrics for request {request_id}: {e}")
    
    async def _write_log_entry(self, log_entry: Dict[str, Any]) -> None:
        """Write log entry to file."""
        try:
            if self.structured_logging:
                # Write as JSON
                log_message = json.dumps(log_entry, ensure_ascii=False)
            else:
                # Write as formatted text
                log_message = self._format_log_entry(log_entry)
            
            # Use the structured logger
            self.structured_logger.info(log_message)
            
        except Exception as e:
            logger.error(f"Error writing log entry: {e}")
    
    def _format_log_entry(self, log_entry: Dict[str, Any]) -> str:
        """Format log entry as readable text."""
        event_type = log_entry.get("event_type", "unknown")
        request_id = log_entry.get("request_id", "unknown")
        timestamp = log_entry.get("timestamp", "unknown")
        
        if event_type == "request":
            return (f"REQUEST {request_id} at {timestamp}: "
                   f"Documents={log_entry['request_data']['documents_url']}, "
                   f"Questions={log_entry['request_data']['questions_count']}")
        elif event_type == "response":
            return (f"RESPONSE {request_id} at {timestamp}: "
                   f"Success={log_entry['success']}, "
                   f"Time={log_entry['processing_time_seconds']}s, "
                   f"Answers={log_entry['response_data']['answers_count']}")
        elif event_type == "error":
            return (f"ERROR {request_id} at {timestamp}: "
                   f"{log_entry['error_type']}: {log_entry['error_message']}")
        else:
            return f"{event_type.upper()} {request_id} at {timestamp}: {json.dumps(log_entry)}"
    
    def _get_stack_trace(self, error: Exception) -> Optional[str]:
        """Get stack trace from exception."""
        import traceback
        try:
            return traceback.format_exc()
        except Exception:
            return None
    
    async def get_log_stats(self) -> Dict[str, Any]:
        """Get statistics about the log file."""
        try:
            if not self.log_file_path.exists():
                return {"status": "no_log_file"}
            
            stat = self.log_file_path.stat()
            
            return {
                "status": "active",
                "file_path": str(self.log_file_path),
                "file_size_mb": round(stat.st_size / (1024 * 1024), 2),
                "last_modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "rotation_size_mb": self.log_rotation_size_mb,
                "backup_count": self.log_backup_count,
                "structured_logging": self.structured_logging
            }
            
        except Exception as e:
            logger.error(f"Error getting log stats: {e}")
            return {"status": "error", "error": str(e)}
    
    async def search_logs(
        self,
        request_id: Optional[str] = None,
        event_type: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Search log entries based on criteria.
        
        Args:
            request_id: Filter by request ID
            event_type: Filter by event type (request, response, error)
            start_time: Filter by start time
            end_time: Filter by end time
            limit: Maximum number of results
            
        Returns:
            List of matching log entries
        """
        try:
            if not self.log_file_path.exists():
                return []
            
            matching_entries = []
            
            async with aiofiles.open(self.log_file_path, 'r', encoding='utf-8') as f:
                async for line in f:
                    if len(matching_entries) >= limit:
                        break
                    
                    try:
                        if self.structured_logging:
                            entry = json.loads(line.strip())
                        else:
                            # For non-structured logs, create a simple entry
                            entry = {"raw_log": line.strip()}
                        
                        # Apply filters
                        if request_id and entry.get("request_id") != request_id:
                            continue
                        
                        if event_type and entry.get("event_type") != event_type:
                            continue
                        
                        if start_time or end_time:
                            entry_time_str = entry.get("timestamp")
                            if entry_time_str:
                                entry_time = datetime.fromisoformat(entry_time_str.replace('Z', '+00:00'))
                                if start_time and entry_time < start_time:
                                    continue
                                if end_time and entry_time > end_time:
                                    continue
                        
                        matching_entries.append(entry)
                        
                    except (json.JSONDecodeError, ValueError) as e:
                        # Skip malformed entries
                        continue
            
            return matching_entries
            
        except Exception as e:
            logger.error(f"Error searching logs: {e}")
            return []


# Global request logger instance
request_logger = RequestLogger()


# Helper functions for easy logging
async def log_request(request: QueryRequest, request_id: str, **kwargs) -> None:
    """Helper function to log a request."""
    await request_logger.log_request(request, request_id, **kwargs)


async def log_response(response: QueryResponse, request_id: str, processing_time: float, **kwargs) -> None:
    """Helper function to log a response."""
    await request_logger.log_response(response, request_id, processing_time, **kwargs)


async def log_error(error: Exception, request_id: str, context: Dict[str, Any], **kwargs) -> None:
    """Helper function to log an error."""
    await request_logger.log_error(error, request_id, context, **kwargs)


def generate_request_id() -> str:
    """Helper function to generate a request ID."""
    return request_logger.generate_request_id()