"""Services package for the LLM Query-Retrieval System."""

from .document_processor import DocumentProcessor
from .embedding_service import AzureEmbeddingService
from .vector_store import VectorStore
from .cohere_reranker_service import cohere_reranker
from .llm_service import AzureLLMService

# Backward compatibility aliases
EmbeddingService = AzureEmbeddingService
RerankerService = cohere_reranker  # Now using Cohere reranker
LLMService = AzureLLMService

__all__ = [
    "DocumentProcessor",
    "AzureEmbeddingService",
    "EmbeddingService", 
    "VectorStore",
    "cohere_reranker",
    "RerankerService",
    "AzureLLMService",
    "LLMService"
]