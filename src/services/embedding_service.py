"""Embedding service for generating and managing embeddings using Azure OpenAI."""

import asyncio
import logging
from typing import List, Dict, Any
import numpy as np
from openai import AsyncAzureOpenAI

from src.config import settings
from src.models.schemas import DocumentChunk
from src.services.multiprocessing_manager import mp_manager, process_embeddings_parallel

logger = logging.getLogger(__name__)


class AzureEmbeddingService:
    """Service for generating embeddings using Azure OpenAI with multiprocessing support."""
    
    def __init__(self):
        self.client = AsyncAzureOpenAI(
            api_key=settings.AZURE_OPENAI_API_KEY,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT
        )
        self.model = settings.AZURE_EMBEDDING_MODEL
        
    async def generate_embeddings(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """
        Generate embeddings for document chunks.
        
        Args:
            chunks: List of document chunks
            
        Returns:
            List of chunks with embeddings added
        """
        try:
            # Extract text content
            texts = [chunk.content for chunk in chunks]
            
            # Generate embeddings in batches to avoid rate limits
            batch_size = 100  # OpenAI's batch limit
            all_embeddings = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_embeddings = await self._generate_batch_embeddings(batch_texts)
                all_embeddings.extend(batch_embeddings)
                
                # Add small delay to respect rate limits
                if i + batch_size < len(texts):
                    await asyncio.sleep(0.1)
            
            # Add embeddings to chunks
            for chunk, embedding in zip(chunks, all_embeddings):
                chunk.embedding = embedding
                
            return chunks
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise
    
    async def generate_query_embedding(self, query: str) -> List[float]:
        """
        Generate embedding for a single query.
        
        Args:
            query: Query text
            
        Returns:
            Query embedding vector
        """
        try:
            embeddings = await self._generate_batch_embeddings([query])
            return embeddings[0]
            
        except Exception as e:
            logger.error(f"Error generating query embedding: {str(e)}")
            raise
    
    async def _generate_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a batch of texts using Azure OpenAI."""
        try:
            response = await self.client.embeddings.create(
                model=self.model,
                input=texts,
                encoding_format="float"
            )
            
            # Extract embeddings in order
            embeddings = [item.embedding for item in response.data]
            
            logger.info(f"Generated {len(embeddings)} Azure embeddings using {response.usage.total_tokens} tokens")
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Error in Azure batch embedding generation: {str(e)}")
            raise
    
    async def _authenticate_azure(self) -> None:
        """Verify Azure OpenAI authentication."""
        try:
            # Test with a simple embedding request
            test_response = await self.client.embeddings.create(
                model=self.model,
                input=["test"],
                encoding_format="float"
            )
            logger.info("Azure OpenAI authentication successful")
        except Exception as e:
            logger.error(f"Azure OpenAI authentication failed: {e}")
            raise
    
    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        vec1_np = np.array(vec1)
        vec2_np = np.array(vec2)
        
        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return dot_product / (norm1 * norm2)
    
    async def generate_embeddings_parallel(self, chunk_batches: List[List[DocumentChunk]]) -> List[List[DocumentChunk]]:
        """
        Generate embeddings for multiple batches of chunks in parallel.
        
        Args:
            chunk_batches: List of chunk batches to process
            
        Returns:
            List of chunk batches with embeddings added
        """
        try:
            logger.info(f"Generating embeddings for {len(chunk_batches)} batches in parallel")
            
            # Use thread pool for I/O-bound embedding generation
            results = await process_embeddings_parallel(
                self._generate_embeddings_for_batch_sync,
                chunk_batches
            )
            
            logger.info(f"Successfully generated embeddings for {len(results)} batches")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel embedding generation: {str(e)}")
            raise
    
    def _generate_embeddings_for_batch_sync(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """
        Synchronous version of embedding generation for multiprocessing.
        This method runs in a separate thread.
        """
        import asyncio
        
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async embedding generation in this thread's event loop
            result = loop.run_until_complete(self.generate_embeddings(chunks))
            return result
        finally:
            loop.close()
    
    async def generate_batch_embeddings_parallel(self, text_batches: List[List[str]]) -> List[List[List[float]]]:
        """
        Generate embeddings for multiple text batches in parallel.
        
        Args:
            text_batches: List of text batches
            
        Returns:
            List of embedding batches
        """
        try:
            logger.info(f"Generating embeddings for {len(text_batches)} text batches in parallel")
            
            # Use thread pool for I/O-bound operations
            results = await process_embeddings_parallel(
                self._generate_text_batch_embeddings_sync,
                text_batches
            )
            
            logger.info(f"Successfully generated embeddings for {len(results)} text batches")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel text batch embedding generation: {str(e)}")
            raise
    
    def _generate_text_batch_embeddings_sync(self, texts: List[str]) -> List[List[float]]:
        """
        Synchronous version of text batch embedding generation for multiprocessing.
        """
        import asyncio
        
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async embedding generation in this thread's event loop
            result = loop.run_until_complete(self._generate_batch_embeddings(texts))
            return result
        finally:
            loop.close()