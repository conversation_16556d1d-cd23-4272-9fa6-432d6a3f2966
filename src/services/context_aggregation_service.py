"""Context Aggregation and Deduplication Service for combining multiple context sources."""

import logging
import time
from typing import List, Dict, Any, Optional, Set
from difflib import SequenceMatcher
import re

from src.config import settings
from src.models.schemas import ContextAggregation

logger = logging.getLogger(__name__)


class ContextAggregationService:
    """Service for combining initial context with gap-filler context and deduplication."""
    
    def __init__(self):
        self.deduplication_threshold = getattr(settings, 'DEDUPLICATION_SIMILARITY_THRESHOLD', 0.9)
        self.max_final_chunks = getattr(settings, 'MAX_FINAL_CONTEXT_CHUNKS', 20)
        self.formatting_style = getattr(settings, 'CONTEXT_FORMATTING_STYLE', 'numbered')
        
        # Performance tracking
        self.aggregations_performed = 0
        self.total_chunks_processed = 0
        self.total_duplicates_removed = 0
        self.total_processing_time = 0.0
        
    async def aggregate_contexts(
        self, 
        initial_context: List[str], 
        gap_filler_context: List[str]
    ) -> str:
        """
        Combine initial context with gap-filler context and deduplicate.
        
        Args:
            initial_context: Context from initial retrieval
            gap_filler_context: Additional context from gap-filling retry
            
        Returns:
            Final aggregated and formatted context string
        """
        start_time = time.time()
        
        try:
            logger.info(f"Aggregating contexts: {len(initial_context)} initial + {len(gap_filler_context)} gap-filler chunks")
            
            # Combine all contexts
            all_chunks = initial_context + gap_filler_context
            self.total_chunks_processed += len(all_chunks)
            
            if not all_chunks:
                return ""
            
            # Deduplicate chunks
            deduplicated_chunks = await self.deduplicate_chunks(all_chunks)
            duplicates_removed = len(all_chunks) - len(deduplicated_chunks)
            self.total_duplicates_removed += duplicates_removed
            
            # Rank chunks by relevance (if we have a query context)
            # For now, maintain original order with initial context first
            ordered_chunks = self._maintain_context_priority(
                deduplicated_chunks, 
                len(initial_context), 
                len(gap_filler_context)
            )
            
            # Limit final chunks
            if len(ordered_chunks) > self.max_final_chunks:
                logger.info(f"Limiting context from {len(ordered_chunks)} to {self.max_final_chunks} chunks")
                ordered_chunks = ordered_chunks[:self.max_final_chunks]
            
            # Format final context
            final_context = self._format_final_context(ordered_chunks)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.total_processing_time += processing_time
            self.aggregations_performed += 1
            
            logger.info(f"Context aggregation completed: {len(ordered_chunks)} final chunks, "
                       f"{duplicates_removed} duplicates removed in {processing_time:.2f}s")
            
            return final_context
            
        except Exception as e:
            logger.error(f"Error in context aggregation: {e}")
            # Fallback: return initial context formatted
            return self._format_final_context(initial_context[:self.max_final_chunks])
    
    async def deduplicate_chunks(self, chunks: List[str]) -> List[str]:
        """
        Remove duplicate or highly similar chunks.
        
        Args:
            chunks: List of context chunks to deduplicate
            
        Returns:
            List of deduplicated chunks
        """
        if not chunks:
            return []
        
        if len(chunks) == 1:
            return chunks
        
        logger.debug(f"Deduplicating {len(chunks)} chunks with threshold {self.deduplication_threshold}")
        
        deduplicated = []
        seen_chunks = set()
        
        for chunk in chunks:
            # Skip empty chunks
            if not chunk.strip():
                continue
            
            # Normalize chunk for comparison
            normalized_chunk = self._normalize_chunk_for_comparison(chunk)
            
            # Check for exact duplicates first
            if normalized_chunk in seen_chunks:
                logger.debug("Removed exact duplicate chunk")
                continue
            
            # Check for semantic similarity with existing chunks
            is_duplicate = False
            for existing_chunk in deduplicated:
                similarity = self._calculate_chunk_similarity(chunk, existing_chunk)
                if similarity >= self.deduplication_threshold:
                    logger.debug(f"Removed similar chunk (similarity: {similarity:.3f})")
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                deduplicated.append(chunk)
                seen_chunks.add(normalized_chunk)
        
        logger.debug(f"Deduplication complete: {len(chunks)} -> {len(deduplicated)} chunks")
        return deduplicated
    
    def _calculate_chunk_similarity(self, chunk1: str, chunk2: str) -> float:
        """
        Calculate similarity between two chunks.
        
        Args:
            chunk1: First chunk
            chunk2: Second chunk
            
        Returns:
            Similarity score between 0.0 and 1.0
        """
        if not chunk1.strip() or not chunk2.strip():
            return 0.0
        
        # Normalize chunks for comparison
        norm1 = self._normalize_chunk_for_comparison(chunk1)
        norm2 = self._normalize_chunk_for_comparison(chunk2)
        
        # Use sequence matcher for similarity
        similarity = SequenceMatcher(None, norm1, norm2).ratio()
        
        return similarity
    
    def _normalize_chunk_for_comparison(self, chunk: str) -> str:
        """
        Normalize chunk text for comparison.
        
        Args:
            chunk: Chunk to normalize
            
        Returns:
            Normalized chunk text
        """
        # Convert to lowercase
        normalized = chunk.lower().strip()
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Remove common punctuation that doesn't affect meaning
        normalized = re.sub(r'[^\w\s]', '', normalized)
        
        return normalized
    
    def _maintain_context_priority(
        self, 
        chunks: List[str], 
        initial_count: int, 
        gap_filler_count: int
    ) -> List[str]:
        """
        Maintain priority order with initial context first.
        
        Args:
            chunks: Deduplicated chunks
            initial_count: Number of initial context chunks
            gap_filler_count: Number of gap-filler chunks
            
        Returns:
            Ordered chunks with priority maintained
        """
        # Simple approach: maintain the order from deduplication
        # Initial context chunks should naturally come first due to input order
        return chunks
    
    def _format_final_context(self, chunks: List[str]) -> str:
        """
        Format the final context string for LLM consumption.
        
        Args:
            chunks: Final list of context chunks
            
        Returns:
            Formatted context string
        """
        if not chunks:
            return ""
        
        if self.formatting_style == "numbered":
            formatted_chunks = []
            for i, chunk in enumerate(chunks, 1):
                formatted_chunks.append(f"[Context {i}]:\n{chunk.strip()}")
            return "\n\n".join(formatted_chunks)
        
        elif self.formatting_style == "bulleted":
            formatted_chunks = []
            for chunk in chunks:
                formatted_chunks.append(f"• {chunk.strip()}")
            return "\n\n".join(formatted_chunks)
        
        elif self.formatting_style == "plain":
            return "\n\n".join(chunk.strip() for chunk in chunks)
        
        else:
            # Default to numbered
            return self._format_final_context(chunks)
    
    async def aggregate_with_relevance_ranking(
        self, 
        initial_context: List[str], 
        gap_filler_context: List[str],
        query: str
    ) -> str:
        """
        Aggregate contexts with relevance-based ranking.
        
        Args:
            initial_context: Context from initial retrieval
            gap_filler_context: Additional context from gap-filling
            query: Original query for relevance ranking
            
        Returns:
            Final aggregated context with relevance ranking
        """
        try:
            # First perform standard aggregation
            all_chunks = initial_context + gap_filler_context
            deduplicated_chunks = await self.deduplicate_chunks(all_chunks)
            
            # Rank by relevance to query
            ranked_chunks = self._rank_chunks_by_relevance(deduplicated_chunks, query)
            
            # Limit and format
            final_chunks = ranked_chunks[:self.max_final_chunks]
            return self._format_final_context(final_chunks)
            
        except Exception as e:
            logger.error(f"Error in relevance-based aggregation: {e}")
            # Fallback to standard aggregation
            return await self.aggregate_contexts(initial_context, gap_filler_context)
    
    def _rank_chunks_by_relevance(self, chunks: List[str], query: str) -> List[str]:
        """
        Rank chunks by relevance to the query.
        
        Args:
            chunks: List of chunks to rank
            query: Query for relevance calculation
            
        Returns:
            Chunks ranked by relevance (most relevant first)
        """
        if not chunks or not query:
            return chunks
        
        # Calculate relevance scores
        chunk_scores = []
        query_lower = query.lower()
        query_words = set(query_lower.split())
        
        for chunk in chunks:
            chunk_lower = chunk.lower()
            chunk_words = set(chunk_lower.split())
            
            # Simple relevance scoring
            word_overlap = len(query_words & chunk_words)
            word_overlap_ratio = word_overlap / len(query_words) if query_words else 0
            
            # Boost score if query appears as substring
            substring_bonus = 0.5 if query_lower in chunk_lower else 0
            
            # Calculate final relevance score
            relevance_score = word_overlap_ratio + substring_bonus
            
            chunk_scores.append((chunk, relevance_score))
        
        # Sort by relevance score (descending)
        chunk_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Return ranked chunks
        return [chunk for chunk, score in chunk_scores]
    
    def get_aggregation_stats(self) -> Dict[str, Any]:
        """
        Get aggregation performance statistics.
        
        Returns:
            Dictionary with aggregation statistics
        """
        avg_processing_time = self.total_processing_time / max(self.aggregations_performed, 1)
        avg_chunks_per_aggregation = self.total_chunks_processed / max(self.aggregations_performed, 1)
        deduplication_rate = self.total_duplicates_removed / max(self.total_chunks_processed, 1) * 100
        
        return {
            "total_aggregations": self.aggregations_performed,
            "total_chunks_processed": self.total_chunks_processed,
            "total_duplicates_removed": self.total_duplicates_removed,
            "deduplication_rate_percent": round(deduplication_rate, 2),
            "total_processing_time_seconds": round(self.total_processing_time, 2),
            "average_processing_time_seconds": round(avg_processing_time, 3),
            "average_chunks_per_aggregation": round(avg_chunks_per_aggregation, 1),
            "deduplication_threshold": self.deduplication_threshold,
            "max_final_chunks": self.max_final_chunks,
            "formatting_style": self.formatting_style
        }
    
    def reset_stats(self):
        """Reset aggregation statistics."""
        self.aggregations_performed = 0
        self.total_chunks_processed = 0
        self.total_duplicates_removed = 0
        self.total_processing_time = 0.0
        logger.info("Aggregation statistics reset")
    
    def configure_aggregation(
        self,
        deduplication_threshold: float = None,
        max_final_chunks: int = None,
        formatting_style: str = None
    ):
        """
        Configure aggregation settings.
        
        Args:
            deduplication_threshold: Similarity threshold for deduplication
            max_final_chunks: Maximum number of final context chunks
            formatting_style: Style for formatting final context
        """
        if deduplication_threshold is not None:
            if not 0.0 <= deduplication_threshold <= 1.0:
                raise ValueError("Deduplication threshold must be between 0.0 and 1.0")
            self.deduplication_threshold = deduplication_threshold
        
        if max_final_chunks is not None:
            if max_final_chunks < 1:
                raise ValueError("Max final chunks must be at least 1")
            self.max_final_chunks = max_final_chunks
        
        if formatting_style is not None:
            valid_styles = ["numbered", "bulleted", "plain"]
            if formatting_style not in valid_styles:
                raise ValueError(f"Formatting style must be one of: {valid_styles}")
            self.formatting_style = formatting_style
        
        logger.info(f"Aggregation configured: threshold={self.deduplication_threshold}, "
                   f"max_chunks={self.max_final_chunks}, style={self.formatting_style}")
    
    async def create_aggregation_result(
        self,
        initial_chunks: List[str],
        gap_filler_chunks: List[str],
        final_context: str,
        processing_time: float
    ) -> ContextAggregation:
        """
        Create a ContextAggregation result object.
        
        Args:
            initial_chunks: Initial context chunks
            gap_filler_chunks: Gap-filler context chunks
            final_context: Final aggregated context
            processing_time: Time taken for aggregation
            
        Returns:
            ContextAggregation object
        """
        total_initial = len(initial_chunks)
        total_gap_filler = len(gap_filler_chunks)
        total_input = total_initial + total_gap_filler
        
        # Count final chunks by counting context markers
        final_chunk_count = final_context.count("[Context ") if "[Context " in final_context else len(final_context.split("\n\n"))
        deduplication_count = max(0, total_input - final_chunk_count)
        
        return ContextAggregation(
            initial_chunks=initial_chunks,
            gap_filler_chunks=gap_filler_chunks,
            final_context=final_context,
            deduplication_count=deduplication_count,
            aggregation_time=processing_time
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check of the aggregation service.
        
        Returns:
            Health check results
        """
        health_status = {
            "service_name": "ContextAggregationService",
            "status": "healthy",
            "checks": {},
            "timestamp": time.time()
        }
        
        try:
            # Test basic aggregation functionality
            test_initial = ["This is initial context chunk 1.", "This is initial context chunk 2."]
            test_gap_filler = ["This is gap-filler context.", "This is initial context chunk 1."]  # Duplicate
            
            start_time = time.time()
            result = await self.aggregate_contexts(test_initial, test_gap_filler)
            response_time = time.time() - start_time
            
            health_status["checks"]["basic_functionality"] = {
                "status": "pass",
                "response_time_seconds": round(response_time, 3),
                "result_length": len(result)
            }
            
            # Test deduplication
            test_chunks = ["Duplicate text", "Different text", "Duplicate text"]
            deduplicated = await self.deduplicate_chunks(test_chunks)
            
            health_status["checks"]["deduplication"] = {
                "status": "pass",
                "input_chunks": len(test_chunks),
                "output_chunks": len(deduplicated),
                "duplicates_removed": len(test_chunks) - len(deduplicated)
            }
            
            # Test configuration
            health_status["checks"]["configuration"] = {
                "status": "pass",
                "deduplication_threshold": self.deduplication_threshold,
                "max_final_chunks": self.max_final_chunks,
                "formatting_style": self.formatting_style
            }
            
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
            health_status["checks"]["basic_functionality"] = {
                "status": "fail",
                "error": str(e)
            }
        
        return health_status