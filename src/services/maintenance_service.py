"""
Maintenance service for job cleanup and system maintenance tasks.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

from src.services.job_storage import job_storage
from src.services.webhook_delivery_service import webhook_delivery_service
from src.config import settings

logger = logging.getLogger(__name__)


class MaintenanceService:
    """
    Service for automated system maintenance tasks.
    
    Features:
    - Automatic cleanup of expired jobs
    - Webhook delivery retry management
    - System health monitoring
    - Performance metrics collection
    """
    
    def __init__(self):
        self.cleanup_interval = getattr(settings, 'MAINTENANCE_CLEANUP_INTERVAL', 3600)  # 1 hour
        self.is_running = False
        self.maintenance_task: asyncio.Task = None
        
        logger.info("Maintenance service initialized")
    
    async def start(self):
        """Start the maintenance service."""
        if self.is_running:
            return
        
        self.is_running = True
        self.maintenance_task = asyncio.create_task(self._maintenance_loop())
        logger.info("Maintenance service started")
    
    async def stop(self):
        """Stop the maintenance service."""
        self.is_running = False
        
        if self.maintenance_task:
            self.maintenance_task.cancel()
            try:
                await self.maintenance_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Maintenance service stopped")
    
    async def _maintenance_loop(self):
        """Main maintenance loop."""
        logger.info("Started maintenance loop")
        
        while self.is_running:
            try:
                await self.run_maintenance_tasks()
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"Error in maintenance loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
        
        logger.info("Maintenance loop stopped")
    
    async def run_maintenance_tasks(self):
        """Run all maintenance tasks."""
        try:
            logger.info("Running maintenance tasks...")
            
            # Task 1: Clean up expired jobs
            cleaned_jobs = await job_storage.cleanup_expired_jobs()
            if cleaned_jobs > 0:
                logger.info(f"Cleaned up {cleaned_jobs} expired jobs")
            
            # Task 2: Update job metrics
            await job_storage.update_metrics()
            
            # Task 3: Log system status
            await self._log_system_status()
            
            logger.info("Maintenance tasks completed")
            
        except Exception as e:
            logger.error(f"Error running maintenance tasks: {e}")
    
    async def _log_system_status(self):
        """Log current system status for monitoring."""
        try:
            # Get job metrics
            metrics = await job_storage.update_metrics()
            
            # Get webhook stats
            webhook_stats = await webhook_delivery_service.get_delivery_stats()
            
            # Log key metrics
            logger.info(f"System Status - Jobs: {metrics.total_jobs} total, "
                       f"{metrics.queued_jobs} queued, {metrics.processing_jobs} processing, "
                       f"{metrics.completed_jobs} completed, {metrics.failed_jobs} failed")
            
            logger.info(f"Webhook Status - {webhook_stats.get('delivered_count', 0)} delivered, "
                       f"{webhook_stats.get('failed_count', 0)} failed, "
                       f"{webhook_stats.get('pending_count', 0)} pending")
            
        except Exception as e:
            logger.error(f"Error logging system status: {e}")
    
    async def force_cleanup(self) -> Dict[str, Any]:
        """Force immediate cleanup of expired resources."""
        try:
            logger.info("Running forced cleanup...")
            
            # Clean up expired jobs
            cleaned_jobs = await job_storage.cleanup_expired_jobs()
            
            # Update metrics
            metrics = await job_storage.update_metrics()
            
            return {
                "cleaned_jobs": cleaned_jobs,
                "current_metrics": metrics.dict(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in forced cleanup: {e}")
            return {"error": str(e)}
    
    async def get_maintenance_stats(self) -> Dict[str, Any]:
        """Get maintenance service statistics."""
        return {
            "is_running": self.is_running,
            "cleanup_interval": self.cleanup_interval,
            "last_run": datetime.utcnow().isoformat() if self.is_running else None
        }


# Global maintenance service instance
maintenance_service = MaintenanceService()