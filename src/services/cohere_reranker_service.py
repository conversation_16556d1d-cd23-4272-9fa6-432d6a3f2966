"""
Azure Cohere Reranker Service with parallel processing optimization.
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import aiohttp
import json
from dataclasses import dataclass

from src.models.schemas import Document<PERSON>hunk, RankedChunk, SubQuery
from src.config import settings

logger = logging.getLogger(__name__)


@dataclass
class CohereRerankResult:
    """Result from Cohere reranker API."""
    relevance_score: float
    document_text: str
    index: int


class CohereRerankerService:
    """
    Azure Cohere reranker service with parallel processing optimization.
    
    Features:
    - Parallel reranking of multiple sub-queries
    - Dynamic thresholding based on score distribution
    - Connection pooling for optimal performance
    - Comprehensive error handling and fallbacks
    """
    
    def __init__(self):
        # Azure Cohere configuration from settings
        self.endpoint = getattr(settings, 'COHERE_RERANKER_ENDPOINT', 
                               "https://Cohere-rerank-v3-5-nyyox.eastus2.models.ai.azure.com/v1/rerank")
        self.api_key = getattr(settings, 'COHERE_RERANKER_API_KEY', 
                              "ofbw1UWv2C6gqsUWnvXC9fmbWDqid25u")
        self.timeout = 30
        self.max_retries = 3
        
        # Performance settings from configuration
        self.max_concurrent_requests = getattr(settings, 'COHERE_MAX_CONCURRENT_REQUESTS', 10)
        self.connection_pool_size = getattr(settings, 'COHERE_CONNECTION_POOL_SIZE', 20)
        
        # Dynamic thresholding settings
        self.min_threshold = 0.3
        self.max_threshold = 0.9
        self.default_threshold = 0.5
        
        # Initialize connection pool
        self.session: Optional[aiohttp.ClientSession] = None
        # Don't initialize session in __init__ - do it lazily when needed
    
    def _initialize_session(self):
        """Initialize aiohttp session with connection pooling."""
        connector = aiohttp.TCPConnector(
            limit=self.connection_pool_size,
            limit_per_host=self.connection_pool_size,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
        )
    
    async def rerank_parallel(
        self,
        sub_queries: List[SubQuery],
        chunks: List[DocumentChunk]
    ) -> Dict[str, List[RankedChunk]]:
        """
        Rerank chunks for multiple sub-queries in parallel.
        
        Args:
            sub_queries: List of sub-queries to process
            chunks: Document chunks to rerank
            
        Returns:
            Dictionary mapping sub-query IDs to ranked chunks
        """
        if not sub_queries or not chunks:
            return {}
        
        start_time = time.time()
        logger.info(f"Starting parallel reranking for {len(sub_queries)} sub-queries with {len(chunks)} chunks")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        
        # Create reranking tasks for each sub-query
        tasks = []
        for sub_query in sub_queries:
            task = asyncio.create_task(
                self._rerank_with_semaphore(semaphore, sub_query.query_text, chunks)
            )
            tasks.append((sub_query.id, task))
        
        # Execute all reranking tasks in parallel
        results = {}
        completed_tasks = 0
        
        for sub_query_id, task in tasks:
            try:
                ranked_chunks = await task
                results[sub_query_id] = ranked_chunks
                completed_tasks += 1
                logger.debug(f"Completed reranking for sub-query {sub_query_id} ({completed_tasks}/{len(sub_queries)})")
                
            except Exception as e:
                logger.error(f"Reranking failed for sub-query {sub_query_id}: {e}")
                results[sub_query_id] = self._create_fallback_ranking(chunks)
        
        processing_time = time.time() - start_time
        logger.info(f"Parallel reranking completed in {processing_time:.2f}s for {len(sub_queries)} sub-queries")
        
        return results
    
    async def _rerank_with_semaphore(
        self,
        semaphore: asyncio.Semaphore,
        query: str,
        chunks: List[DocumentChunk]
    ) -> List[RankedChunk]:
        """Rerank with semaphore to limit concurrent requests."""
        async with semaphore:
            return await self.rerank_single(query, chunks)
    
    async def rerank_single(
        self,
        query: str,
        chunks: List[DocumentChunk]
    ) -> List[RankedChunk]:
        """
        Rerank chunks for a single query.
        
        Args:
            query: Query text
            chunks: Document chunks to rerank
            
        Returns:
            List of ranked chunks with dynamic thresholding applied
        """
        if not chunks:
            return []
        
        try:
            # Prepare documents for Cohere API
            documents = [chunk.content for chunk in chunks]
            
            # Call Cohere reranker
            rerank_results = await self._call_cohere_api(query, documents)
            
            if not rerank_results:
                logger.warning(f"No reranking results for query: {query[:50]}...")
                return self._create_fallback_ranking(chunks)
            
            # Convert to RankedChunk objects
            ranked_chunks = []
            for result in rerank_results:
                if result.index < len(chunks):
                    chunk = chunks[result.index]
                    ranked_chunk = RankedChunk(
                        chunk=chunk,
                        score=result.relevance_score,
                        rank=len(ranked_chunks) + 1,
                        confidence=result.relevance_score,
                        relevance_explanation=f"Cohere relevance score: {result.relevance_score:.4f}"
                    )
                    ranked_chunks.append(ranked_chunk)
            
            # Apply dynamic thresholding
            filtered_chunks = self._apply_dynamic_threshold(ranked_chunks)
            
            logger.debug(f"Reranked {len(chunks)} chunks, selected {len(filtered_chunks)} after thresholding")
            return filtered_chunks
            
        except Exception as e:
            logger.error(f"Error in single reranking: {e}")
            return self._create_fallback_ranking(chunks)
    
    async def _call_cohere_api(
        self,
        query: str,
        documents: List[str]
    ) -> List[CohereRerankResult]:
        """
        Call Azure Cohere reranker API.
        
        Args:
            query: Query text
            documents: List of document texts
            
        Returns:
            List of reranking results
        """
        payload = {
            "query": query,
            "documents": documents,
            "top_n": len(documents),
            "return_documents": True
        }
        
        for attempt in range(self.max_retries):
            try:
                if not self.session:
                    self._initialize_session()
                
                async with self.session.post(self.endpoint, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_cohere_response(data)
                    else:
                        error_text = await response.text()
                        logger.warning(f"Cohere API error (attempt {attempt + 1}): {response.status} - {error_text}")
                        
                        if response.status == 429:  # Rate limit
                            await asyncio.sleep(2 ** attempt)
                            continue
                        elif response.status >= 500:  # Server error
                            await asyncio.sleep(1)
                            continue
                        else:
                            break  # Client error, don't retry
                            
            except asyncio.TimeoutError:
                logger.warning(f"Cohere API timeout (attempt {attempt + 1})")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Cohere API call error (attempt {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)
        
        logger.error("All Cohere API attempts failed")
        return []
    
    def _parse_cohere_response(self, data: Dict[str, Any]) -> List[CohereRerankResult]:
        """Parse Cohere API response."""
        results = []
        
        try:
            for item in data.get('results', []):
                result = CohereRerankResult(
                    relevance_score=item.get('relevance_score', 0.0),
                    document_text=item.get('document', {}).get('text', ''),
                    index=item.get('index', 0)
                )
                results.append(result)
                
        except Exception as e:
            logger.error(f"Error parsing Cohere response: {e}")
        
        return results
    
    def _apply_dynamic_threshold(self, ranked_chunks: List[RankedChunk]) -> List[RankedChunk]:
        """
        Apply dynamic thresholding based on score distribution.
        
        Args:
            ranked_chunks: List of ranked chunks
            
        Returns:
            Filtered list of chunks above dynamic threshold
        """
        if not ranked_chunks:
            return []
        
        scores = [chunk.score for chunk in ranked_chunks]
        threshold = self.calculate_dynamic_threshold(scores)
        
        # Filter chunks above threshold
        filtered_chunks = [
            chunk for chunk in ranked_chunks 
            if chunk.score >= threshold
        ]
        
        # Ensure we have at least some chunks (fallback)
        if not filtered_chunks and ranked_chunks:
            # Take top 3 chunks if threshold is too strict
            filtered_chunks = sorted(ranked_chunks, key=lambda x: x.score, reverse=True)[:3]
            logger.warning(f"Dynamic threshold too strict, using top 3 chunks")
        
        # Update adaptive threshold in chunk metadata
        for chunk in filtered_chunks:
            chunk.adaptive_threshold = threshold
        
        return filtered_chunks
    
    def calculate_dynamic_threshold(self, scores: List[float]) -> float:
        """
        Calculate dynamic threshold based on score distribution.
        
        Args:
            scores: List of relevance scores
            
        Returns:
            Dynamic threshold value
        """
        if not scores:
            return self.default_threshold
        
        scores_array = np.array(scores)
        
        # Calculate statistical measures
        mean_score = np.mean(scores_array)
        std_score = np.std(scores_array)
        median_score = np.median(scores_array)
        q75 = np.percentile(scores_array, 75)
        
        # Dynamic threshold based on distribution characteristics
        if std_score < 0.1:
            # Low variance - scores are similar, use median
            threshold = median_score
        elif mean_score > 0.8:
            # High scores overall - be more selective
            threshold = max(mean_score - 0.5 * std_score, q75)
        elif mean_score < 0.3:
            # Low scores overall - be less selective
            threshold = mean_score + 0.2 * std_score
        else:
            # Normal distribution - use mean with partial standard deviation
            threshold = mean_score + 0.3 * std_score
        
        # Ensure threshold is within reasonable bounds
        threshold = max(self.min_threshold, min(self.max_threshold, threshold))
        
        logger.debug(f"Dynamic threshold: {threshold:.3f} (mean: {mean_score:.3f}, std: {std_score:.3f})")
        return threshold
    
    def _create_fallback_ranking(self, chunks: List[DocumentChunk]) -> List[RankedChunk]:
        """
        Create fallback ranking when Cohere API fails.
        
        Args:
            chunks: Original document chunks
            
        Returns:
            List of ranked chunks with fallback scores
        """
        ranked_chunks = []
        
        for i, chunk in enumerate(chunks):
            # Simple fallback scoring based on content length and position
            fallback_score = max(0.1, 0.8 - (i * 0.1))  # Decreasing score
            
            ranked_chunk = RankedChunk(
                chunk=chunk,
                score=fallback_score,
                rank=i + 1,
                confidence=0.5,  # Low confidence for fallback
                relevance_explanation="Fallback ranking (Cohere API unavailable)"
            )
            ranked_chunks.append(ranked_chunk)
        
        # Apply basic threshold (keep top 50%)
        mid_point = len(ranked_chunks) // 2
        return ranked_chunks[:max(3, mid_point)]
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check health of Cohere reranker service.
        
        Returns:
            Health status information
        """
        try:
            # Test with minimal payload
            test_documents = ["Test document for health check"]
            test_query = "test"
            
            start_time = time.time()
            results = await self._call_cohere_api(test_query, test_documents)
            response_time = time.time() - start_time
            
            return {
                "status": "healthy" if results else "degraded",
                "endpoint": self.endpoint,
                "response_time_ms": round(response_time * 1000, 2),
                "connection_pool_size": self.connection_pool_size,
                "max_concurrent_requests": self.max_concurrent_requests
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "endpoint": self.endpoint
            }
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.session:
            await self.session.close()
            self.session = None


# Global instance
cohere_reranker = CohereRerankerService()