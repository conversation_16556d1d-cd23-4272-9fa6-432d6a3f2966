"""Model cache manager for pre-loading models at application startup."""

import asyncio
import logging
from typing import Dict, Any, Optional
import gc

logger = logging.getLogger(__name__)


class ModelCacheManager:
    """Manages pre-loaded models for the application."""
    
    def __init__(self):
        self.models: Dict[str, Any] = {}
        self.model_info: Dict[str, Dict[str, Any]] = {}
        self._initialized = False
        
    async def load_all_models(self) -> None:
        """Load all required models at application startup."""
        if self._initialized:
            logger.info("Models already loaded")
            return
            
        try:
            logger.info("Starting model loading process...")
            
            # Load models in order of importance
            await self._load_sentence_transformer()
            # Note: Provence and Qwen3 rerankers removed - now using Azure Cohere reranker
            
            # Mark as initialized
            self._initialized = True
            
            logger.info("All models loaded successfully")
            
            # Log model information
            for model_name, info in self.model_info.items():
                logger.info(f"  {model_name}: {info['status']}")
                
        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            self._initialized = False
            raise
    
    async def _load_sentence_transformer(self) -> None:
        """Load sentence transformer model for semantic chunking."""
        model_name = "sentence_transformer"
        
        try:
            logger.info("Loading sentence transformer model...")
            
            # Import and load the model
            from sentence_transformers import SentenceTransformer
            from src.config import settings
            
            model = SentenceTransformer(settings.SENTENCE_TRANSFORMER_MODEL)
            
            # Store model and info
            self.models[model_name] = model
            self.model_info[model_name] = {
                "status": "loaded",
                "model_path": settings.SENTENCE_TRANSFORMER_MODEL,
                "load_time": "startup"
            }
            
            logger.info("Sentence transformer model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load sentence transformer model: {e}")
            self.model_info[model_name] = {
                "status": "failed",
                "error": str(e)
            }
            raise
    
    async def _load_provence_reranker(self) -> None:
        """Load Naver Provence reranker model."""
        model_name = "provence_reranker"
        
        try:
            logger.info("Loading Provence reranker model...")
            
            # Import and load the model with trust_remote_code=True
            from transformers import AutoTokenizer, AutoModel
            
            model_path = "naver/provence-reranker-debertav3-v1"
            
            # Load tokenizer and model with trust_remote_code
            tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            model = AutoModel.from_pretrained(model_path, trust_remote_code=True)
            
            # Store both tokenizer and model
            self.models[model_name] = {
                "tokenizer": tokenizer,
                "model": model
            }
            
            self.model_info[model_name] = {
                "status": "loaded",
                "model_path": model_path,
                "load_time": "startup"
            }
            
            logger.info("Provence reranker model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load Provence reranker model: {e}")
            self.model_info[model_name] = {
                "status": "failed",
                "error": str(e)
            }
            # Don't raise here as reranker is optional
    
    async def _load_qwen3_reranker(self) -> None:
        """Load Qwen3 reranker model."""
        try:
            logger.info("Loading Qwen3 reranker model...")
            
            from transformers import AutoTokenizer, AutoModelForCausalLM
            from src.config import settings
            import torch
            
            model_path = settings.QWEN3_RERANKER_MODEL
            
            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(model_path, padding_side='left')
            model = AutoModelForCausalLM.from_pretrained(model_path).eval()
            
            # Move to appropriate device
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = model.to(device)
            
            # Store tokenizer and model separately for easier access
            self.models["qwen3_tokenizer"] = tokenizer
            self.models["qwen3_reranker"] = model
            
            self.model_info["qwen3_reranker"] = {
                "status": "loaded",
                "model_path": model_path,
                "device": str(device),
                "load_time": "startup"
            }
            
            logger.info(f"Qwen3 reranker model loaded successfully on {device}")
            
        except Exception as e:
            logger.error(f"Failed to load Qwen3 reranker model: {e}")
            self.model_info["qwen3_reranker"] = {
                "status": "failed",
                "error": str(e)
            }
            # Don't raise here as reranker is optional
    
    def get_model(self, model_name: str) -> Any:
        """Get a cached model by name."""
        if not self._initialized:
            logger.warning(f"Models not initialized when requesting {model_name}")
            return None
            
        model = self.models.get(model_name)
        if model is None:
            logger.warning(f"Model '{model_name}' not found in cache")
            
        return model
    
    def get_sentence_transformer(self):
        """Get the cached sentence transformer model."""
        return self.get_model("sentence_transformer")
    
    def get_provence_reranker(self):
        """Get the cached Provence reranker model."""
        return self.get_model("provence_reranker")
    
    def get_qwen3_reranker(self):
        """Get the cached Qwen3 reranker model."""
        return self.get_model("qwen3_reranker")
    
    def get_qwen3_tokenizer(self):
        """Get the cached Qwen3 tokenizer."""
        return self.get_model("qwen3_tokenizer")
    
    def is_initialized(self) -> bool:
        """Check if models are initialized."""
        return self._initialized
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about all cached models."""
        return {
            "initialized": self._initialized,
            "total_models": len(self.models),
            "models": self.model_info.copy()
        }
    
    async def cleanup_models(self) -> None:
        """Cleanup all cached models."""
        logger.info("Cleaning up cached models...")
        
        try:
            # Clear model references
            for model_name in list(self.models.keys()):
                del self.models[model_name]
                logger.info(f"Cleaned up model: {model_name}")
            
            self.models.clear()
            self.model_info.clear()
            self._initialized = False
            
            # Force garbage collection
            gc.collect()
            
            logger.info("Model cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during model cleanup: {e}")


# Global model cache manager instance
model_cache = ModelCacheManager()


# Helper functions for easy access
def get_sentence_transformer():
    """Get the cached sentence transformer model."""
    return model_cache.get_sentence_transformer()


def get_provence_reranker():
    """Get the cached Provence reranker model."""
    return model_cache.get_provence_reranker()


def get_qwen3_reranker():
    """Get the cached Qwen3 reranker model."""
    return model_cache.get_qwen3_reranker()


def get_qwen3_tokenizer():
    """Get the cached Qwen3 tokenizer."""
    return model_cache.get_qwen3_tokenizer()


def get_model(model_name: str):
    """Get any cached model by name."""
    return model_cache.get_model(model_name)


def is_models_initialized() -> bool:
    """Check if models are initialized."""
    return model_cache.is_initialized()