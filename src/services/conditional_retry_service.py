"""Conditional Retry Service for targeted gap filling with single-iteration limit."""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional

from src.config import settings
from src.models.schemas import GapAnalysisResult, RetryResult
from src.services.parallel_subquery_processor import ParallelSubQueryProcessor
from openai import AsyncAzureOpenAI
import json

logger = logging.getLogger(__name__)


class ConditionalRetryService:
    """Service for managing single-iteration retry loops for gap filling."""
    
    def __init__(self):
        self.max_retry_iterations = getattr(settings, 'MAX_RETRY_ITERATIONS', 1)
        self.retry_timeout = getattr(settings, 'RETRY_TIMEOUT', 60)
        self.gap_filler_max_queries = getattr(settings, 'GAP_FILLER_MAX_QUERIES', 3)
        
        # Initialize sub-query processor for gap-filler queries
        self.subquery_processor = ParallelSubQueryProcessor()
        
        # Initialize LLM client for gap-filler query generation
        self.client = AsyncAzureOpenAI(
            api_key=settings.AZURE_OPENAI_API_KEY,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT
        )
        self.model = settings.AZURE_OPENAI_MODEL
        
        # Performance tracking
        self.retry_attempts = 0
        self.successful_retries = 0
        self.failed_retries = 0
        self.total_retry_time = 0.0
        
    async def execute_retry_if_needed(
        self, 
        gap_analysis: GapAnalysisResult, 
        document_index: Any,
        original_question: Optional[str] = None
    ) -> Optional[RetryResult]:
        """
        Execute retry if gap analysis indicates insufficient context.
        
        Args:
            gap_analysis: Result from gap analysis
            document_index: Document index for searching
            original_question: Original question for context
            
        Returns:
            RetryResult object if retry executed, None otherwise
        """
        if self._should_retry(gap_analysis):
            logger.info(f"Gap analysis indicates retry needed: {gap_analysis.missing_information}")
            return await self._execute_single_retry(gap_analysis, document_index, original_question)
        else:
            logger.info("Gap analysis indicates context is sufficient - no retry needed")
            return RetryResult(
                executed=False,
                gap_filler_queries=[],
                additional_context=[],
                processing_time=0.0,
                success=True
            )
    
    def _should_retry(self, gap_analysis: GapAnalysisResult) -> bool:
        """
        Determine if retry should be executed based on gap analysis.
        
        Args:
            gap_analysis: Gap analysis result
            
        Returns:
            True if retry should be executed
        """
        # Don't retry if context is sufficient
        if gap_analysis.is_sufficient:
            return False
        
        # Don't retry if missing information is empty or too vague
        if not gap_analysis.missing_information or len(gap_analysis.missing_information.strip()) < 10:
            logger.warning("Missing information too vague for retry")
            return False
        
        # Don't retry if confidence is too low
        if gap_analysis.confidence_score < 0.5:
            logger.warning(f"Gap analysis confidence too low for retry: {gap_analysis.confidence_score}")
            return False
        
        return True
    
    async def _execute_single_retry(
        self, 
        gap_analysis: GapAnalysisResult, 
        document_index: Any,
        original_question: Optional[str] = None
    ) -> RetryResult:
        """
        Execute a single retry iteration to fill identified gaps.
        
        Args:
            gap_analysis: Gap analysis result
            document_index: Document index for searching
            original_question: Original question for context
            
        Returns:
            RetryResult object with retry details
        """
        start_time = time.time()
        self.retry_attempts += 1
        
        try:
            logger.info("Executing single retry iteration for gap filling")
            
            # Generate gap-filler queries using LLM
            gap_filler_queries = await self.generate_gap_filler_queries(
                gap_analysis.missing_information, 
                original_question
            )
            
            if not gap_filler_queries:
                logger.warning("No gap-filler queries generated")
                self.failed_retries += 1
                return RetryResult(
                    executed=True,
                    gap_filler_queries=[],
                    additional_context=[],
                    processing_time=time.time() - start_time,
                    success=False
                )
            
            logger.info(f"Generated {len(gap_filler_queries)} gap-filler queries")
            
            # Execute gap-filler pipeline with timeout
            additional_context = await asyncio.wait_for(
                self.execute_gap_filler_pipeline(gap_filler_queries, document_index),
                timeout=self.retry_timeout
            )
            
            processing_time = time.time() - start_time
            self.total_retry_time += processing_time
            
            if additional_context:
                self.successful_retries += 1
                logger.info(f"Retry successful: retrieved {len(additional_context)} additional context chunks in {processing_time:.2f}s")
                success = True
            else:
                self.failed_retries += 1
                logger.warning("Retry completed but no additional context retrieved")
                success = False
            
            return RetryResult(
                executed=True,
                gap_filler_queries=gap_filler_queries,
                additional_context=additional_context,
                processing_time=processing_time,
                success=success
            )
            
        except asyncio.TimeoutError:
            processing_time = time.time() - start_time
            self.total_retry_time += processing_time
            self.failed_retries += 1
            logger.error(f"Retry timed out after {self.retry_timeout}s")
            return RetryResult(
                executed=True,
                gap_filler_queries=[],
                additional_context=[],
                processing_time=processing_time,
                success=False
            )
        except Exception as e:
            processing_time = time.time() - start_time
            self.total_retry_time += processing_time
            self.failed_retries += 1
            logger.error(f"Error in retry execution: {e}")
            return RetryResult(
                executed=True,
                gap_filler_queries=[],
                additional_context=[],
                processing_time=processing_time,
                success=False
            )
    
    async def generate_gap_filler_queries(
        self, 
        missing_information: str, 
        original_question: Optional[str] = None
    ) -> List[str]:
        """
        Generate targeted gap-filler queries from missing information using LLM.
        
        Args:
            missing_information: Description of missing information
            original_question: Original question for context
            
        Returns:
            List of gap-filler queries
        """
        try:
            # Use LLM to generate intelligent gap-filler queries
            gap_queries = await self._call_llm_for_gap_queries(missing_information, original_question)
            
            if not gap_queries:
                logger.warning("LLM failed to generate gap-filler queries, using fallback")
                gap_queries = self._generate_fallback_queries(missing_information, original_question)
            
            # Limit number of gap-filler queries
            gap_queries = gap_queries[:self.gap_filler_max_queries]
            
            logger.info(f"Generated {len(gap_queries)} gap-filler queries from missing information")
            return gap_queries
            
        except Exception as e:
            logger.error(f"Error generating gap-filler queries: {e}")
            return []
    
    async def execute_gap_filler_pipeline(
        self, 
        gap_queries: List[str], 
        document_index: Any
    ) -> List[str]:
        """
        Execute the RAG pipeline for gap-filler queries.
        
        Args:
            gap_queries: List of gap-filler queries
            document_index: Document index for searching
            
        Returns:
            List of additional context strings
        """
        try:
            logger.info(f"Executing gap-filler pipeline for {len(gap_queries)} queries")
            
            # Use the parallel sub-query processor with Cohere reranking
            additional_contexts = await self.subquery_processor.process_subqueries_parallel_with_cohere(
                gap_queries, 
                document_index
            )
            
            # Filter out empty or very short contexts
            valid_contexts = [
                ctx for ctx in additional_contexts 
                if ctx.strip() and len(ctx.strip()) > 20
            ]
            
            logger.info(f"Gap-filler pipeline retrieved {len(valid_contexts)} valid contexts")
            return valid_contexts
            
        except Exception as e:
            logger.error(f"Error in gap-filler pipeline: {e}")
            return []
    
    def get_retry_stats(self) -> Dict[str, Any]:
        """
        Get retry performance statistics.
        
        Returns:
            Dictionary with retry statistics
        """
        success_rate = self.successful_retries / max(self.retry_attempts, 1) * 100
        avg_retry_time = self.total_retry_time / max(self.retry_attempts, 1)
        
        return {
            "total_retry_attempts": self.retry_attempts,
            "successful_retries": self.successful_retries,
            "failed_retries": self.failed_retries,
            "success_rate_percent": round(success_rate, 2),
            "total_retry_time_seconds": round(self.total_retry_time, 2),
            "average_retry_time_seconds": round(avg_retry_time, 3),
            "max_retry_iterations": self.max_retry_iterations,
            "retry_timeout_seconds": self.retry_timeout,
            "max_gap_filler_queries": self.gap_filler_max_queries
        }
    
    def reset_stats(self):
        """Reset retry statistics."""
        self.retry_attempts = 0
        self.successful_retries = 0
        self.failed_retries = 0
        self.total_retry_time = 0.0
        logger.info("Retry statistics reset")
    
    def configure_retry_settings(
        self, 
        max_iterations: int = None,
        timeout_seconds: int = None,
        max_gap_queries: int = None
    ):
        """
        Configure retry settings.
        
        Args:
            max_iterations: Maximum retry iterations (should be 1)
            timeout_seconds: Timeout for retry operations
            max_gap_queries: Maximum number of gap-filler queries
        """
        if max_iterations is not None:
            if max_iterations > 1:
                logger.warning(f"Max iterations set to {max_iterations}, but design specifies max 1 iteration")
            self.max_retry_iterations = max_iterations
        
        if timeout_seconds is not None:
            self.retry_timeout = timeout_seconds
        
        if max_gap_queries is not None:
            self.gap_filler_max_queries = max_gap_queries
        
        logger.info(f"Retry settings configured: max_iterations={self.max_retry_iterations}, "
                   f"timeout={self.retry_timeout}s, max_gap_queries={self.gap_filler_max_queries}")
    
    async def create_retry_result(
        self, 
        gap_analysis: GapAnalysisResult, 
        additional_context: Optional[List[str]]
    ) -> RetryResult:
        """
        Create a RetryResult object from retry execution.
        
        Args:
            gap_analysis: Original gap analysis
            additional_context: Additional context from retry (if any)
            
        Returns:
            RetryResult object
        """
        executed = additional_context is not None
        success = executed and len(additional_context or []) > 0
        
        # Extract gap-filler queries from the missing information
        gap_filler_queries = await self.generate_gap_filler_queries(
            gap_analysis.missing_information
        ) if executed else []
        
        return RetryResult(
            executed=executed,
            gap_filler_queries=gap_filler_queries,
            additional_context=additional_context or [],
            processing_time=0.0,  # This would be set by the caller
            success=success
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check of the retry service.
        
        Returns:
            Health check results
        """
        health_status = {
            "service_name": "ConditionalRetryService",
            "status": "healthy",
            "checks": {},
            "timestamp": time.time()
        }
        
        try:
            # Test gap-filler query generation
            test_missing_info = "Missing specific waiting period duration"
            test_queries = await self.generate_gap_filler_queries(test_missing_info)
            
            health_status["checks"]["query_generation"] = {
                "status": "pass",
                "generated_queries": len(test_queries)
            }
            
            # Test configuration
            health_status["checks"]["configuration"] = {
                "status": "pass",
                "max_retry_iterations": self.max_retry_iterations,
                "retry_timeout": self.retry_timeout,
                "max_gap_filler_queries": self.gap_filler_max_queries
            }
            
            # Test sub-query processor
            processor_stats = self.subquery_processor.get_performance_stats()
            health_status["checks"]["subquery_processor"] = {
                "status": "pass",
                "processor_ready": True
            }
            
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
            health_status["checks"]["basic_functionality"] = {
                "status": "fail",
                "error": str(e)
            }
        
        return health_status    

    async def _call_llm_for_gap_queries(
        self, 
        missing_information: str, 
        original_question: Optional[str] = None
    ) -> List[str]:
        """
        Use LLM to generate intelligent gap-filler queries.
        
        Args:
            missing_information: Description of missing information
            original_question: Original question for context
            
        Returns:
            List of gap-filler queries
        """
        try:
            prompt = self._format_gap_query_prompt(missing_information, original_question)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_gap_query_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent query generation
                max_tokens=800,
                timeout=20
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse the JSON response
            gap_queries = self._parse_gap_query_response(response_text)
            
            # Log token usage
            if response.usage:
                logger.info(f"Gap query LLM tokens used - Prompt: {response.usage.prompt_tokens}, "
                           f"Completion: {response.usage.completion_tokens}, "
                           f"Total: {response.usage.total_tokens}")
            
            return gap_queries
            
        except Exception as e:
            logger.error(f"Error calling LLM for gap queries: {str(e)}")
            return []
    
    def _format_gap_query_prompt(self, missing_information: str, original_question: Optional[str] = None) -> str:
        """Format the prompt for gap-filler query generation."""
        
        context_part = ""
        if original_question:
            context_part = f"\nOriginal Question: {original_question}"
        
        return f"""You are an expert at generating targeted search queries to fill information gaps. Your task is to create specific, focused queries that will help retrieve the missing information identified in a gap analysis.

{context_part}

Missing Information: {missing_information}

Generate maximum 3 specific, targeted queries that would help retrieve the missing information. Each query should:
1. Be focused on finding the specific missing information
2. Use clear, direct language
3. Be suitable for document search
4. Target different aspects or phrasings of the missing information

Guidelines:
- For insurance/legal contexts, focus on specific terms, amounts, periods, conditions
- Make queries atomic and focused on single pieces of information
- Use domain-appropriate terminology
- Avoid overly broad or vague queries

Return your response as a JSON array of strings containing the gap-filler queries.

Example format: ["What is the specific waiting period for X?", "How long is the waiting time for Y?", "What are the exact conditions for Z?"]

Generate gap-filler queries now:"""
    
    def _get_gap_query_system_prompt(self) -> str:
        """Get the system prompt for gap-filler query generation."""
        return """You are an expert search query generator specializing in insurance, legal, HR, and compliance domains.

Your role is to:
1. Analyze missing information descriptions
2. Generate targeted, specific search queries
3. Focus on retrieving precise information gaps
4. Use domain-appropriate terminology
5. Create queries that are likely to find the missing details

You excel at:
- Understanding what specific information is missing
- Creating focused queries that target exact details
- Using appropriate domain terminology
- Generating multiple query variations for better coverage

Always respond with valid JSON format containing an array of query strings."""
    
    def _parse_gap_query_response(self, response: str) -> List[str]:
        """Parse the LLM response to extract gap-filler queries."""
        try:
            # Clean up response
            response = response.strip()
            
            # Remove any markdown code block formatting
            if response.startswith("```json"):
                response = response[7:]
            if response.startswith("```"):
                response = response[3:]
            if response.endswith("```"):
                response = response[:-3]
            
            response = response.strip()
            
            # Parse JSON
            gap_queries = json.loads(response)
            
            # Validate that it's a list of strings
            if isinstance(gap_queries, list) and all(isinstance(q, str) for q in gap_queries):
                # Filter out empty strings and clean up
                cleaned_queries = [q.strip() for q in gap_queries if q.strip()]
                return cleaned_queries
            else:
                logger.error(f"Invalid gap query response format: {response}")
                return []
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse gap query JSON: {e}")
            logger.error(f"Response was: {response}")
            return []
        except Exception as e:
            logger.error(f"Error parsing gap query response: {e}")
            return []
    
    def _generate_fallback_queries(self, missing_information: str, original_question: Optional[str] = None) -> List[str]:
        """Generate fallback queries when LLM fails."""
        gap_queries = []
        missing_lower = missing_information.lower()
        
        # Generate specific queries based on missing information patterns
        if "waiting period" in missing_lower or "wait" in missing_lower:
            gap_queries.extend([
                "What is the waiting period?",
                "How long is the waiting time?",
                "What is the duration of the waiting period?"
            ])
        
        if "amount" in missing_lower or "cost" in missing_lower or "price" in missing_lower:
            gap_queries.extend([
                "What is the amount?",
                "What is the cost?",
                "What are the fees?"
            ])
        
        if "condition" in missing_lower or "requirement" in missing_lower:
            gap_queries.extend([
                "What are the conditions?",
                "What are the requirements?",
                "What are the eligibility criteria?"
            ])
        
        if "coverage" in missing_lower or "benefit" in missing_lower:
            gap_queries.extend([
                "What is covered?",
                "What are the benefits?",
                "What is included in coverage?"
            ])
        
        if "exclusion" in missing_lower or "not covered" in missing_lower:
            gap_queries.extend([
                "What is excluded?",
                "What is not covered?",
                "What are the exclusions?"
            ])
        
        if "limit" in missing_lower or "maximum" in missing_lower:
            gap_queries.extend([
                "What are the limits?",
                "What is the maximum amount?",
                "What are the coverage limits?"
            ])
        
        # If no specific patterns matched, create generic queries
        if not gap_queries:
            gap_queries = [
                f"What information is missing about {missing_information}?",
                f"Please provide details about {missing_information}",
                f"What are the specifics regarding {missing_information}?"
            ]
        
        # Add context from original question if available
        if original_question:
            contextual_query = f"In the context of '{original_question}', what about {missing_information.lower()}?"
            gap_queries.append(contextual_query)
        
        return gap_queries[:self.gap_filler_max_queries]