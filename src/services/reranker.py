"""Adaptive reranking service using Naver Provence reranker with parallel processing."""

import asyncio
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from transformers import AutoModel, AutoTokenizer
import torch

from src.config import settings
from src.models.schemas import Retrieval<PERSON><PERSON><PERSON>, RankedChunk, DocumentChunk
from src.services.multiprocessing_manager import mp_manager, process_reranking_parallel
from src.services.model_cache import get_provence_reranker

logger = logging.getLogger(__name__)


class AdaptiveNaverProvenceReranker:
    """Service for adaptive reranking with parallel processing using Naver Provence reranker."""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.min_chunks = getattr(settings, 'MIN_CHUNKS', 3)
        self.max_chunks = getattr(settings, 'MAX_CHUNKS', 15)
        self.confidence_threshold = getattr(settings, 'CONFIDENCE_THRESHOLD', 0.7)
        self.dynamic_adjustment = getattr(settings, 'DYNAMIC_ADJUSTMENT', True)
        self.fallback_chunks = getattr(settings, 'FALLBACK_CHUNKS', 5)
        self._load_provence_model()
        
    def _load_provence_model(self) -> None:
        """Load the Naver Provence reranker model from cache or directly."""
        try:
            # Try to get from cache first
            cached_model = get_provence_reranker()
            
            if cached_model is not None:
                self.tokenizer = cached_model["tokenizer"]
                self.model = cached_model["model"]
                logger.info("Using cached Provence reranker model")
            else:
                # Fallback to loading directly
                model_path = getattr(settings, 'PROVENCE_MODEL', 'naver/provence-reranker-debertav3-v1')
                self.tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
                self.model = AutoModel.from_pretrained(model_path, trust_remote_code=True)
                logger.info(f"Loaded Provence model directly: {model_path}")
                
        except Exception as e:
            logger.error(f"Failed to load Provence model: {e}")
            # Set to None to indicate model not available
            self.model = None
            self.tokenizer = None
    
    async def adaptive_rerank(
        self, 
        query: str, 
        chunks: List[str]
    ) -> List[RankedChunk]:
        """
        Perform adaptive reranking with confidence-based chunk selection.
        
        Args:
            query: User query
            chunks: List of chunk content strings
            
        Returns:
            List of adaptively selected ranked chunks
        """
        try:
            if not self.model or not self.tokenizer:
                logger.warning("Provence model not available, using fallback ranking")
                return self._fallback_ranking(query, chunks)
            
            # Calculate relevance scores for all chunks
            scores = await self._calculate_relevance_scores(query, chunks)
            
            # Create ranked chunks with scores
            ranked_chunks = []
            for i, (chunk_content, score) in enumerate(zip(chunks, scores)):
                chunk = DocumentChunk(
                    id=f"chunk_{i}",
                    content=chunk_content,
                    metadata={"original_index": i}
                )
                
                ranked_chunk = RankedChunk(
                    chunk=chunk,
                    score=score,
                    rank=i,  # Initial rank, will be updated after sorting
                    confidence=score,  # Use score as confidence
                    relevance_explanation=f"Provence score: {score:.3f}",
                    adaptive_threshold=0.0  # Will be set later
                )
                ranked_chunks.append(ranked_chunk)
            
            # Sort by score
            ranked_chunks.sort(key=lambda x: x.score, reverse=True)
            
            # Apply adaptive thresholding
            selected_chunks = self._apply_adaptive_threshold(ranked_chunks)
            
            # Update ranks
            for rank, chunk in enumerate(selected_chunks):
                chunk.rank = rank
            
            logger.info(f"Adaptive reranking: {len(chunks)} -> {len(selected_chunks)} chunks selected")
            return selected_chunks
            
        except Exception as e:
            logger.error(f"Error in adaptive reranking: {e}")
            return self._fallback_ranking(query, chunks)
    
    async def _calculate_relevance_scores(self, query: str, chunks: List[str]) -> List[float]:
        """Calculate relevance scores for chunks using Provence model in batch."""
        try:
            # Use the Provence model's built-in batch processing if available
            if hasattr(self.model, 'process') and len(chunks) > 1:
                # Provence model supports batch processing
                logger.info(f"Processing {len(chunks)} chunks in batch with Provence model")
                
                # Process chunks individually since batch processing format is unclear
                scores = []
                for chunk in chunks:
                    try:
                        result = self.model.process(query, chunk)
                        score = result.get('reranking_score', 0.5)
                        scores.append(score)
                    except Exception as chunk_error:
                        logger.warning(f"Error processing chunk: {chunk_error}")
                        scores.append(0.5)
                
                return scores
                

                
            else:
                # Fallback to individual processing or use direct model inference
                logger.info(f"Processing {len(chunks)} chunks individually")
                scores = []
                
                for chunk in chunks:
                    try:
                        # Use the Provence model's process method for single chunk
                        if hasattr(self.model, 'process'):
                            result = self.model.process(query, chunk)
                            score = result.get('reranking_score', 0.5)
                        else:
                            # Direct model inference (batch this part)
                            score = await self._single_chunk_score(query, chunk)
                        
                        scores.append(float(score))
                        
                    except Exception as chunk_error:
                        logger.warning(f"Error processing chunk: {chunk_error}")
                        scores.append(0.5)  # Default score for failed chunks
                
                return scores
            
        except Exception as e:
            logger.error(f"Error calculating relevance scores: {e}")
            # Return uniform scores as fallback
            return [0.5] * len(chunks)
    
    async def _single_chunk_score(self, query: str, chunk: str) -> float:
        """Calculate score for a single chunk using direct model inference."""
        try:
            # Tokenize query and chunk (remove token_type_ids to avoid the error)
            inputs = self.tokenizer(
                query, 
                chunk, 
                return_tensors="pt", 
                truncation=True, 
                max_length=512,
                padding=True
            )
            
            # Remove token_type_ids if present to avoid the error
            if 'token_type_ids' in inputs:
                del inputs['token_type_ids']
            
            # Get model output
            with torch.no_grad():
                outputs = self.model(**inputs)
                
                # Extract relevance score
                if hasattr(outputs, 'logits'):
                    logits = outputs.logits
                    # Apply softmax to get probabilities
                    probs = torch.softmax(logits, dim=-1)
                    # Take the positive class probability
                    score = probs[0][1].item() if probs.shape[-1] > 1 else probs[0][0].item()
                elif hasattr(outputs, 'last_hidden_state'):
                    # Fallback: use pooler output or last hidden state
                    score = torch.sigmoid(outputs.last_hidden_state.mean()).item()
                else:
                    score = 0.5  # Default score
            
            return float(score)
            
        except Exception as e:
            logger.warning(f"Error in single chunk scoring: {e}")
            return 0.5
    
    def _apply_adaptive_threshold(self, ranked_chunks: List[RankedChunk]) -> List[RankedChunk]:
        """Apply adaptive thresholding to select optimal number of chunks."""
        if not ranked_chunks:
            return []
        
        scores = [chunk.score for chunk in ranked_chunks]
        
        if not self.dynamic_adjustment:
            # Use fixed threshold
            threshold = self.confidence_threshold
            selected = [chunk for chunk in ranked_chunks if chunk.score >= threshold]
        else:
            # Calculate dynamic threshold
            threshold = self._calculate_dynamic_threshold(scores)
            selected = [chunk for chunk in ranked_chunks if chunk.score >= threshold]
        
        # Apply min/max constraints
        if len(selected) < self.min_chunks:
            # Take top min_chunks regardless of threshold
            selected = ranked_chunks[:self.min_chunks]
            threshold = selected[-1].score if selected else 0.0
        elif len(selected) > self.max_chunks:
            # Take only top max_chunks
            selected = selected[:self.max_chunks]
            threshold = selected[-1].score if selected else threshold
        
        # Update adaptive threshold in chunks
        for chunk in selected:
            chunk.adaptive_threshold = threshold
        
        logger.info(f"Adaptive threshold: {threshold:.3f}, selected {len(selected)} chunks")
        return selected
    
    def _calculate_dynamic_threshold(self, scores: List[float]) -> float:
        """Calculate dynamic threshold based on score distribution."""
        if not scores:
            return self.confidence_threshold
        
        scores_array = np.array(scores)
        
        # Method 1: Use mean + standard deviation
        mean_score = np.mean(scores_array)
        std_score = np.std(scores_array)
        threshold_1 = mean_score + (0.5 * std_score)
        
        # Method 2: Use percentile-based threshold
        threshold_2 = np.percentile(scores_array, 70)  # 70th percentile
        
        # Method 3: Use gap-based threshold (find largest gap in scores)
        sorted_scores = np.sort(scores_array)[::-1]  # Descending order
        gaps = np.diff(sorted_scores)
        if len(gaps) > 0:
            max_gap_idx = np.argmax(gaps)
            threshold_3 = sorted_scores[max_gap_idx + 1]
        else:
            threshold_3 = self.confidence_threshold
        
        # Combine thresholds (weighted average)
        combined_threshold = (
            0.4 * threshold_1 + 
            0.3 * threshold_2 + 
            0.3 * threshold_3
        )
        
        # Ensure threshold is within reasonable bounds
        final_threshold = max(
            min(combined_threshold, max(scores) * 0.8),  # Not higher than 80% of max score
            min(scores) * 1.2  # Not lower than 120% of min score
        )
        
        return float(final_threshold)
    
    def _fallback_ranking(self, query: str, chunks: List[str]) -> List[RankedChunk]:
        """Fallback ranking when Provence model is not available."""
        logger.warning("Using fallback ranking (simple text similarity)")
        
        ranked_chunks = []
        
        for i, chunk_content in enumerate(chunks):
            # Simple text similarity based on common words
            query_words = set(query.lower().split())
            chunk_words = set(chunk_content.lower().split())
            
            if len(query_words) > 0:
                similarity = len(query_words.intersection(chunk_words)) / len(query_words)
            else:
                similarity = 0.0
            
            chunk = DocumentChunk(
                id=f"fallback_chunk_{i}",
                content=chunk_content,
                metadata={"original_index": i, "method": "fallback"}
            )
            
            ranked_chunk = RankedChunk(
                chunk=chunk,
                score=similarity,
                rank=i,  # Initial rank
                confidence=similarity,
                relevance_explanation=f"Fallback similarity: {similarity:.3f}",
                adaptive_threshold=0.3  # Lower threshold for fallback
            )
            ranked_chunks.append(ranked_chunk)
        
        # Sort by similarity
        ranked_chunks.sort(key=lambda x: x.score, reverse=True)
        
        # Take top fallback_chunks
        selected = ranked_chunks[:self.fallback_chunks]
        
        # Update ranks
        for rank, chunk in enumerate(selected):
            chunk.rank = rank
        
        return selected
    
    async def rerank_chunks(
        self, 
        query: str, 
        results: List[RetrievalResult]
    ) -> List[RankedChunk]:
        """
        Rerank and prune retrieval results using Provence.
        
        Args:
            query: User query
            results: Initial retrieval results
            
        Returns:
            List of reranked and pruned chunks
        """
        try:
            if not results:
                return []
            
            reranked_chunks = []
            
            for result in results:
                # Process each chunk with Provence
                provence_result = await self.process(query, result.chunk.content)
                
                # Create ranked chunk with Provence results
                ranked_chunk = RankedChunk(
                    chunk=result.chunk,
                    score=provence_result.get('reranking_score', 0.0),
                    rank=result.rank,  # Will be updated after sorting
                    pruned_content=provence_result.get('pruned_context', result.chunk.content)
                )
                
                reranked_chunks.append(ranked_chunk)
            
            # Sort by Provence scores
            reranked_chunks.sort(key=lambda x: x.score, reverse=True)
            
            # Update ranks
            for rank, chunk in enumerate(reranked_chunks):
                chunk.rank = rank
            
            # Filter by score threshold if needed
            filtered_chunks = [
                chunk for chunk in reranked_chunks 
                if chunk.score > 0.5  # Minimum relevance threshold
            ]
            
            logger.info(f"Reranked {len(results)} chunks, kept {len(filtered_chunks)} after filtering")
            return filtered_chunks[:settings.FINAL_TOP_K]  # Return top K results
            
        except Exception as e:
            logger.error(f"Error in chunk reranking: {e}")
            # Return original results as fallback
            return [
                RankedChunk(
                    chunk=result.chunk,
                    score=result.score,
                    rank=result.rank,
                    pruned_content=result.chunk.content
                )
                for result in results
            ]
    
    async def rerank(
        self, 
        query: str, 
        results: List[RetrievalResult]
    ) -> List[RetrievalResult]:
        """
        Legacy method for compatibility - rerank retrieval results.
        
        Args:
            query: User query
            results: Initial retrieval results
            
        Returns:
            Reranked results
        """
        try:
            ranked_chunks = await self.rerank_chunks(query, results)
            
            # Convert back to RetrievalResult format
            reranked_results = []
            for ranked_chunk in ranked_chunks:
                result = RetrievalResult(
                    chunk=ranked_chunk.chunk,
                    score=ranked_chunk.score,
                    rank=ranked_chunk.rank
                )
                reranked_results.append(result)
            
            return reranked_results
            
        except Exception as e:
            logger.error(f"Error in legacy reranking: {e}")
            return results


    async def rerank_parallel(
        self, 
        queries: List[str], 
        chunk_lists: List[List[str]]
    ) -> List[List[RankedChunk]]:
        """
        Perform adaptive reranking for multiple queries in parallel.
        
        Args:
            queries: List of user queries
            chunk_lists: List of chunk lists (one per query)
            
        Returns:
            List of ranked chunk lists
        """
        try:
            logger.info(f"Performing parallel reranking for {len(queries)} queries")
            
            # Process all queries in parallel using asyncio.gather for better performance
            rerank_tasks = [
                self.adaptive_rerank(query, chunks)
                for query, chunks in zip(queries, chunk_lists)
            ]
            
            results = await asyncio.gather(*rerank_tasks)
            
            logger.info(f"Successfully completed parallel reranking for {len(results)} queries")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel reranking: {e}")
            # Return fallback results
            return [self._fallback_ranking(query, chunks) for query, chunks in zip(queries, chunk_lists)]
    
    async def rerank_multiple_queries_batch(
        self,
        queries: List[str],
        results_list: List[List[RetrievalResult]]
    ) -> List[List[RankedChunk]]:
        """
        Rerank multiple queries with their respective results in parallel.
        
        Args:
            queries: List of queries
            results_list: List of retrieval results for each query
            
        Returns:
            List of ranked chunks for each query
        """
        try:
            logger.info(f"Batch reranking {len(queries)} queries")
            
            # Process all queries in parallel
            rerank_tasks = [
                self.rerank_chunks(query, results)
                for query, results in zip(queries, results_list)
            ]
            
            ranked_results = await asyncio.gather(*rerank_tasks)
            
            logger.info(f"Batch reranking completed for {len(ranked_results)} queries")
            return ranked_results
            
        except Exception as e:
            logger.error(f"Error in batch reranking: {e}")
            # Return fallback
            return [
                [RankedChunk(
                    chunk=result.chunk,
                    score=result.score,
                    rank=i,
                    confidence=result.score,
                    relevance_explanation="Fallback ranking",
                    adaptive_threshold=0.5
                ) for i, result in enumerate(results)]
                for results in results_list
            ]
    
    def _adaptive_rerank_sync(self, args: tuple) -> List[RankedChunk]:
        """
        Synchronous version of adaptive reranking for multiprocessing.
        
        Args:
            args: Tuple of (query, chunks)
            
        Returns:
            List of ranked chunks
        """
        query, chunks = args
        
        import asyncio
        
        # Create new event loop for this process
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Create a new reranker instance in this process
            reranker = AdaptiveNaverProvenceReranker()
            
            # Run the async reranking in this process's event loop
            result = loop.run_until_complete(reranker.adaptive_rerank(query, chunks))
            return result
        finally:
            loop.close()
    
    def get_cached_model(self):
        """Get cached Provence model."""
        return get_provence_reranker()
    
    def calculate_adaptive_threshold(self, scores: List[float]) -> float:
        """Public method to calculate adaptive threshold."""
        return self._calculate_dynamic_threshold(scores)
    
    def select_chunks_by_confidence(
        self, 
        ranked_chunks: List[RankedChunk], 
        threshold: float
    ) -> List[RankedChunk]:
        """
        Select chunks based on confidence threshold.
        
        Args:
            ranked_chunks: List of ranked chunks
            threshold: Confidence threshold
            
        Returns:
            Filtered list of chunks
        """
        selected = [chunk for chunk in ranked_chunks if chunk.confidence >= threshold]
        
        # Apply min/max constraints
        if len(selected) < self.min_chunks:
            selected = ranked_chunks[:self.min_chunks]
        elif len(selected) > self.max_chunks:
            selected = selected[:self.max_chunks]
        
        return selected


# Update the legacy methods for backward compatibility
class NaverProvenceReranker(AdaptiveNaverProvenceReranker):
    """Legacy wrapper for backward compatibility."""
    
    async def process(self, query: str, context: str) -> Dict[str, Any]:
        """Legacy process method."""
        try:
            # Use adaptive reranking on single context
            chunks = [context]
            ranked_chunks = await self.adaptive_rerank(query, chunks)
            
            if ranked_chunks:
                return {
                    'reranking_score': ranked_chunks[0].score,
                    'pruned_context': ranked_chunks[0].chunk.content
                }
            else:
                return {
                    'reranking_score': 0.0,
                    'pruned_context': context
                }
                
        except Exception as e:
            logger.error(f"Error in legacy process method: {e}")
            return {
                'reranking_score': 0.0,
                'pruned_context': context
            }
    
    async def rerank_chunks(
        self, 
        query: str, 
        results: List[RetrievalResult]
    ) -> List[RankedChunk]:
        """Legacy rerank_chunks method with optimized batch processing."""
        try:
            if not results:
                return []
            
            logger.info(f"Reranking {len(results)} chunks for query in batch")
            
            # Extract chunks from results for batch processing
            chunks = [result.chunk.content for result in results]
            
            # Use adaptive reranking with batch processing
            ranked_chunks = await self.adaptive_rerank(query, chunks)
            
            # Map back to original chunks with metadata
            for i, ranked_chunk in enumerate(ranked_chunks):
                if i < len(results):
                    # Preserve original chunk metadata
                    original_chunk = results[i].chunk
                    ranked_chunk.chunk.id = original_chunk.id
                    ranked_chunk.chunk.metadata = original_chunk.metadata
            
            logger.info(f"Batch reranking completed: {len(ranked_chunks)} chunks selected")
            return ranked_chunks
            
        except Exception as e:
            logger.error(f"Error in legacy rerank_chunks: {e}")
            # Return fallback with original chunks
            return [
                RankedChunk(
                    chunk=result.chunk,
                    score=result.score,
                    rank=i,
                    confidence=result.score,
                    relevance_explanation="Fallback ranking",
                    adaptive_threshold=0.5
                )
                for i, result in enumerate(results)
            ]


# Backward compatibility aliases
RerankerService = NaverProvenceReranker
AdaptiveRerankerService = AdaptiveNaverProvenceReranker