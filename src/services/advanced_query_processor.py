"""Advanced Query Processor - Main orchestrator for the self-correcting multi-query RAG pipeline."""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from src.config import settings
from src.models.schemas import QueryRequest, QueryResponse, SubQuery, GapAnalysisResult, RetryResult, ContextAggregation
from src.services.query_decomposition_service import QueryDecompositionService
from src.services.parallel_subquery_processor import ParallelSubQueryProcessor
from src.services.gap_analysis_service import KnowledgeGapAnalyzer
from src.services.conditional_retry_service import ConditionalRetryService
from src.services.context_aggregation_service import ContextAggregationService
from src.services.llm_service import AzureLLMService
from src.services.document_processor import DocumentProcessor
from src.services.document_cache import DocumentCache
from src.services.logging_service import RequestLogger
from src.services.cohere_reranker_service import cohere_reranker
# Hybrid retrieval imports removed

logger = logging.getLogger(__name__)


@dataclass
class PipelineMetrics:
    """Metrics for the advanced pipeline execution."""
    total_processing_time: float = 0.0
    decomposition_time: float = 0.0
    parallel_processing_time: float = 0.0
    gap_analysis_time: float = 0.0
    retry_time: float = 0.0
    aggregation_time: float = 0.0
    synthesis_time: float = 0.0
    total_subqueries: int = 0
    retry_executed: bool = False
    cache_hits: int = 0
    questions_processed: int = 0


class AdvancedQueryProcessor:
    """
    Main orchestrator for the advanced, self-correcting, multi-query RAG pipeline.
    
    Pipeline flow:
    1. Query Decomposition → 2. Parallel Sub-Query Processing → 3. Gap Analysis → 
    4. Conditional Retry → 5. Context Aggregation → 6. Answer Synthesis
    """
    
    def __init__(self):
        # Initialize all services
        self.query_decomposer = QueryDecompositionService()
        self.subquery_processor = ParallelSubQueryProcessor()
        self.gap_analyzer = KnowledgeGapAnalyzer()
        self.retry_service = ConditionalRetryService()
        self.context_aggregator = ContextAggregationService()
        self.llm_service = AzureLLMService()
        self.document_processor = DocumentProcessor()
        self.document_cache = DocumentCache()
        self.request_logger = RequestLogger()
# Hybrid retrieval services removed
        
        # Configuration
        self.max_retry_iterations = getattr(settings, 'MAX_RETRY_ITERATIONS', 1)
        self.enable_gap_analysis = True
        self.enable_retry = True
# Hybrid retrieval configuration removed
        
        logger.info("Advanced Query Processor initialized")
    
    async def process_request(self, request: QueryRequest) -> QueryResponse:
        """
        Process a complete request through the advanced pipeline.
        
        Args:
            request: Query request with document URL and questions
            
        Returns:
            Query response with answers
        """
        start_time = time.time()
        request_id = f"req_{int(time.time() * 1000)}"
        
        try:
            # Log the incoming request
            await self.request_logger.log_request(request, request_id)
            
            logger.info(f"Processing request {request_id} with {len(request.questions)} questions")
            
            # Step 1: Process document (with caching)
            document_index = await self._process_document(request.documents)
            
            # Step 2: Process all questions through advanced pipeline
            answers = await self._process_questions_advanced(request.questions, document_index, request_id)
            
            # Create response
            response = QueryResponse(answers=answers)
            
            # Log the response
            processing_time = time.time() - start_time
            await self.request_logger.log_response(response, request_id, processing_time)
            
            logger.info(f"Request {request_id} completed in {processing_time:.2f}s")
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            await self.request_logger.log_error(e, request_id, {"processing_time": processing_time})
            logger.error(f"Error processing request {request_id}: {str(e)}")
            
            # Try fallback processing instead of returning error
            try:
                logger.info(f"Attempting fallback processing for request {request_id}")
                fallback_answers = await self._fallback_processing(request.questions, request.documents)
                return QueryResponse(answers=fallback_answers)
            except Exception as fallback_error:
                logger.error(f"Fallback processing also failed: {str(fallback_error)}")
                # Only return error message as last resort
                error_answer = "I apologize, but I encountered an error while processing your question. Please try again."
                return QueryResponse(answers=[error_answer] * len(request.questions))
    
    async def _process_document(self, document_url: str) -> Any:
        """
        Process document with caching support.
        
        Args:
            document_url: URL of the document to process
            
        Returns:
            Document index for querying
        """
        try:
            # Check cache first
            cached_doc = await self.document_cache.get_cached_document(document_url)
            if cached_doc:
                logger.info(f"Using cached document for URL: {document_url}")
                return cached_doc.faiss_index
            
            # Process document if not cached
            logger.info(f"Processing document from URL: {document_url}")
            
            # Get document chunks using the correct method
            document_chunks = await self.document_processor.process_document_from_url(document_url)
            
            # Create FAISS index from chunks
            document_index = await self._create_document_index(document_chunks)
            
            # Cache the processed document
            await self.document_cache.cache_document(
                document_url, 
                document_index, 
                document_chunks
            )
            
            return document_index
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            raise
    
    async def _create_document_index(self, chunks: List[Any]) -> Any:
        """
        Create a proper FAISS vector store from document chunks.
        
        Args:
            chunks: List of document chunks
            
        Returns:
            VectorStore instance for searching
        """
        try:
            logger.info(f"Creating FAISS vector store with {len(chunks)} chunks")
            
            # Generate embeddings for chunks if they don't have them
            from src.services.embedding_service import AzureEmbeddingService
            
            embedding_service = AzureEmbeddingService()
            
            # Check if chunks already have embeddings
            chunks_need_embeddings = [chunk for chunk in chunks if not hasattr(chunk, 'embedding') or chunk.embedding is None]
            
            if chunks_need_embeddings:
                logger.info(f"Generating embeddings for {len(chunks_need_embeddings)} chunks")
                chunks_with_embeddings = await embedding_service.generate_embeddings(chunks_need_embeddings)
                
                # Update the original chunks list with embeddings
                embedding_map = {chunk.id: chunk.embedding for chunk in chunks_with_embeddings}
                for chunk in chunks:
                    if chunk.id in embedding_map:
                        chunk.embedding = embedding_map[chunk.id]
            
            # Create and build FAISS vector store
            from src.services.vector_store import VectorStore
            vector_store = VectorStore()
            await vector_store.build_index(chunks)
            
            logger.info(f"Successfully created FAISS vector store with {len(chunks)} chunks")
            return vector_store
            
        except Exception as e:
            logger.error(f"Error creating FAISS vector store: {str(e)}")
            # Return a fallback index that works with the parallel processor
            class FallbackVectorStore:
                def __init__(self, chunks):
                    self.chunks = chunks if chunks else []
                    self.chunk_count = len(self.chunks)
                
                async def search(self, query_embedding, top_k=10):
                    """Async search method that returns mock RetrievalResult objects."""
                    from src.models.schemas import RetrievalResult
                    
                    # Return top chunks as RetrievalResult objects
                    results = []
                    for i, chunk in enumerate(self.chunks[:top_k]):
                        result = RetrievalResult(
                            chunk=chunk,
                            score=0.8,  # Mock score
                            rank=i
                        )
                        results.append(result)
                    
                    return results
            
            return FallbackVectorStore(chunks if chunks else [])
    
    async def _process_questions_advanced(
        self, 
        questions: List[str], 
        document_index: Any, 
        request_id: str
    ) -> List[str]:
        """
        Process questions through the advanced multi-query pipeline.
        
        Args:
            questions: List of user questions
            document_index: Processed document index
            request_id: Request identifier for logging
            
        Returns:
            List of answers
        """
        pipeline_start = time.time()
        metrics = PipelineMetrics()
        metrics.questions_processed = len(questions)
        
        try:
            # Step 1: Query Decomposition
            logger.info(f"Step 1: Decomposing {len(questions)} questions")
            decomp_start = time.time()
            
            all_subqueries = await self.query_decomposer.decompose_queries_parallel(questions)
            
            metrics.decomposition_time = time.time() - decomp_start
            metrics.total_subqueries = sum(len(subqs) for subqs in all_subqueries)
            
            logger.info(f"Decomposed into {metrics.total_subqueries} total sub-queries")
            
            # Step 2: Parallel Sub-Query Processing
            logger.info("Step 2: Processing sub-queries in parallel")
            parallel_start = time.time()
            
            # Flatten all sub-queries for parallel processing
            flattened_subqueries = []
            question_subquery_map = {}  # Map to track which subqueries belong to which question
            
            for q_idx, subqueries in enumerate(all_subqueries):
                question_subquery_map[q_idx] = []
                for subquery in subqueries:
                    sq_idx = len(flattened_subqueries)
                    flattened_subqueries.append(subquery)
                    question_subquery_map[q_idx].append(sq_idx)
            
            # Process all sub-queries in parallel with Cohere reranking
            initial_contexts = await self.subquery_processor.process_subqueries_parallel_with_cohere(
                flattened_subqueries, document_index
            )
            
            metrics.parallel_processing_time = time.time() - parallel_start
            
            # Step 3: Gap Analysis for each question
            logger.info("Step 3: Analyzing context gaps")
            gap_start = time.time()
            
            question_contexts = []
            gap_results = []
            
            for q_idx, question in enumerate(questions):
                # Aggregate contexts for this question
                subquery_indices = question_subquery_map[q_idx]
                question_context_chunks = [initial_contexts[i] for i in subquery_indices]
                aggregated_context = await self.context_aggregator.aggregate_contexts(
                    question_context_chunks, []
                )
                question_contexts.append(aggregated_context)
                
                # Analyze gap for this question
                if self.enable_gap_analysis:
                    gap_result = await self.gap_analyzer.analyze_context_sufficiency(
                        question, aggregated_context
                    )
                    gap_results.append(gap_result)
                else:
                    # Skip gap analysis
                    gap_results.append(GapAnalysisResult(
                        is_sufficient=True,
                        missing_information="",
                        confidence_score=1.0,
                        analysis_time=0.0,
                        context_quality_score=1.0
                    ))
            
            metrics.gap_analysis_time = time.time() - gap_start
            
            # Step 4: Conditional Retry (max 1 iteration)
            logger.info("Step 4: Conditional retry processing")
            retry_start = time.time()
            
            final_contexts = []
            retry_executed = False
            
            for q_idx, (question, context, gap_result) in enumerate(zip(questions, question_contexts, gap_results)):
                if self.enable_retry and not gap_result.is_sufficient:
                    logger.info(f"Executing retry for question {q_idx + 1}: {gap_result.missing_information}")
                    
                    # Execute first retry attempt
                    retry_result = await self.retry_service.execute_retry_if_needed(
                        gap_result, document_index
                    )
                    
                    current_context = context
                    if retry_result and retry_result.additional_context:
                        # Aggregate initial and gap-filler contexts
                        current_context = await self.context_aggregator.aggregate_contexts(
                            [context], retry_result.additional_context
                        )
                        retry_executed = True
                        
                        # Check if we need a second retry
                        if self.enable_gap_analysis:
                            second_gap_result = await self.gap_analyzer.analyze_context_sufficiency(
                                question, current_context
                            )
                            
                            if not second_gap_result.is_sufficient:
                                logger.info(f"Executing second retry for question {q_idx + 1}: {second_gap_result.missing_information}")
                                
                                # Execute second retry with different approach
                                second_retry_result = await self.retry_service.execute_retry_if_needed(
                                    second_gap_result, document_index
                                )
                                
                                if second_retry_result and second_retry_result.additional_context:
                                    # Aggregate all contexts
                                    current_context = await self.context_aggregator.aggregate_contexts(
                                        [current_context], second_retry_result.additional_context
                                    )
                                    logger.info(f"Second retry successful for question {q_idx + 1}")
                                else:
                                    logger.info(f"Second retry did not improve context for question {q_idx + 1}")
                    
                    final_contexts.append(current_context)
                else:
                    final_contexts.append(context)
            
            metrics.retry_time = time.time() - retry_start
            metrics.retry_executed = retry_executed
            
            # Step 5: Final Answer Synthesis
            logger.info("Step 5: Synthesizing final answers")
            synthesis_start = time.time()
            
            # Generate answers using the LLM service
            # Convert contexts to the format expected by the LLM service
            context_results_list = []
            for context in final_contexts:
                # Create mock RetrievalResult objects from context strings
                mock_results = []
                if context:  # Only if context is not empty
                    from src.models.schemas import RetrievalResult, DocumentChunk
                    mock_chunk = DocumentChunk(
                        id="context_chunk",
                        content=context,
                        metadata={"source": "advanced_pipeline"}
                    )
                    mock_result = RetrievalResult(
                        chunk=mock_chunk,
                        score=1.0,
                        rank=1
                    )
                    mock_results.append(mock_result)
                context_results_list.append(mock_results)
            
            answers = await self.llm_service.generate_batch_answers_parallel(
                questions, 
                context_results_list
            )
            
            metrics.synthesis_time = time.time() - synthesis_start
            metrics.total_processing_time = time.time() - pipeline_start
            
            # Log pipeline metrics
            await self._log_pipeline_metrics(request_id, metrics, gap_results)
            
            logger.info(f"Advanced pipeline completed in {metrics.total_processing_time:.2f}s")
            return answers
            
        except Exception as e:
            logger.error(f"Error in advanced pipeline: {str(e)}")
            
            # Try to provide partial answers instead of complete failure
            try:
                logger.info("Attempting to provide partial answers after pipeline error")
                partial_answers = []
                
                for question in questions:
                    try:
                        # Simple fallback answer generation
                        fallback_answer = await self._generate_fallback_answer(question, document_index)
                        partial_answers.append(fallback_answer)
                    except Exception:
                        partial_answers.append("I was unable to find relevant information to answer this question.")
                
                logger.info(f"Generated {len(partial_answers)} partial answers")
                return partial_answers
                
            except Exception as partial_error:
                logger.error(f"Partial answer generation failed: {partial_error}")
                raise e  # Re-raise original error
    
    async def _log_pipeline_metrics(
        self, 
        request_id: str, 
        metrics: PipelineMetrics, 
        gap_results: List[GapAnalysisResult]
    ):
        """Log detailed pipeline metrics."""
        
        # Calculate gap analysis statistics
        insufficient_contexts = sum(1 for gap in gap_results if not gap.is_sufficient)
        avg_context_quality = sum(gap.context_quality_score for gap in gap_results) / len(gap_results)
        
        metrics_dict = {
            "request_id": request_id,
            "total_processing_time": round(metrics.total_processing_time, 3),
            "decomposition_time": round(metrics.decomposition_time, 3),
            "parallel_processing_time": round(metrics.parallel_processing_time, 3),
            "gap_analysis_time": round(metrics.gap_analysis_time, 3),
            "retry_time": round(metrics.retry_time, 3),
            "synthesis_time": round(metrics.synthesis_time, 3),
            "total_subqueries": metrics.total_subqueries,
            "questions_processed": metrics.questions_processed,
            "avg_subqueries_per_question": round(metrics.total_subqueries / metrics.questions_processed, 2),
            "retry_executed": metrics.retry_executed,
            "insufficient_contexts": insufficient_contexts,
            "avg_context_quality": round(avg_context_quality, 3),
            "pipeline_efficiency": round(
                (metrics.parallel_processing_time + metrics.synthesis_time) / metrics.total_processing_time * 100, 1
            )
        }
        
        logger.info(f"Pipeline metrics: {metrics_dict}")
    
    async def _fallback_processing(self, questions: List[str], document_url: str) -> List[str]:
        """
        Fallback processing when the main pipeline fails.
        
        Args:
            questions: List of questions to process
            document_url: Document URL
            
        Returns:
            List of fallback answers
        """
        try:
            logger.info("Executing fallback processing with simplified pipeline")
            
            # Simple document processing
            document_chunks = await self.document_processor.process_document_from_url(document_url)
            
            # Create simple index
            class SimpleIndex:
                def __init__(self, chunks):
                    self.chunks = chunks
                
                async def search(self, query_embedding, top_k=5):
                    from src.models.schemas import RetrievalResult
                    results = []
                    for i, chunk in enumerate(self.chunks[:top_k]):
                        result = RetrievalResult(chunk=chunk, score=0.7, rank=i)
                        results.append(result)
                    return results
            
            simple_index = SimpleIndex(document_chunks)
            
            # Process each question with simple retrieval
            fallback_answers = []
            for question in questions:
                try:
                    # Simple context retrieval
                    context_chunks = []
                    for chunk in document_chunks[:5]:  # Take first 5 chunks as context
                        if hasattr(chunk, 'content'):
                            context_chunks.append(chunk.content)
                    
                    context = "\n\n".join(context_chunks) if context_chunks else "No context available"
                    
                    # Generate answer with simple LLM call
                    from src.services.llm_service import AzureLLMService
                    llm_service = AzureLLMService()
                    
                    # Create mock retrieval results
                    from src.models.schemas import RetrievalResult, DocumentChunk
                    mock_results = []
                    if context_chunks:
                        for i, content in enumerate(context_chunks[:3]):
                            mock_chunk = DocumentChunk(
                                id=f"fallback_chunk_{i}",
                                content=content,
                                metadata={"source": "fallback"}
                            )
                            mock_result = RetrievalResult(chunk=mock_chunk, score=0.7, rank=i)
                            mock_results.append(mock_result)
                    
                    answer = await llm_service._generate_answer_sync(question, mock_results)
                    fallback_answers.append(answer)
                    
                except Exception as e:
                    logger.error(f"Error in fallback processing for question: {e}")
                    fallback_answers.append("I was unable to find relevant information to answer this question.")
            
            logger.info(f"Fallback processing completed for {len(questions)} questions")
            return fallback_answers
            
        except Exception as e:
            logger.error(f"Fallback processing failed: {e}")
            raise
    
    async def _generate_fallback_answer(self, question: str, document_index: Any) -> str:
        """
        Generate a fallback answer for a single question.
        
        Args:
            question: User question
            document_index: Document index
            
        Returns:
            Fallback answer string
        """
        try:
            # Get some context from document index
            context_text = ""
            if hasattr(document_index, 'chunks') and document_index.chunks:
                # Take first few chunks as context
                context_chunks = []
                for chunk in document_index.chunks[:3]:
                    if hasattr(chunk, 'content'):
                        context_chunks.append(chunk.content)
                    elif isinstance(chunk, str):
                        context_chunks.append(chunk)
                
                context_text = "\n\n".join(context_chunks)
            
            if not context_text:
                return "I was unable to find relevant information to answer this question."
            
            # Generate simple answer using LLM
            from src.services.llm_service import AzureLLMService
            from src.models.schemas import RetrievalResult, DocumentChunk
            
            llm_service = AzureLLMService()
            
            # Create mock retrieval result
            mock_chunk = DocumentChunk(
                id="fallback_context",
                content=context_text,
                metadata={"source": "fallback"}
            )
            mock_result = RetrievalResult(chunk=mock_chunk, score=0.5, rank=0)
            
            answer = await llm_service._generate_answer_sync(question, [mock_result])
            return answer
            
        except Exception as e:
            logger.error(f"Error generating fallback answer: {e}")
            return "I was unable to find relevant information to answer this question."

    async def process_single_question_advanced(
        self, 
        question: str, 
        document_index: Any
    ) -> str:
        """
        Process a single question through the advanced pipeline.
        
        Args:
            question: User question
            document_index: Processed document index
            
        Returns:
            Answer string
        """
        try:
            logger.info(f"Processing single question: {question[:50]}...")
            
            # Step 1: Decompose question
            subqueries = await self.query_decomposer.decompose_query(question)
            
            # Step 2: Process sub-queries in parallel with Cohere reranking
            initial_contexts = await self.subquery_processor.process_subqueries_parallel_with_cohere(
                subqueries, document_index
            )
            
            # Step 3: Aggregate initial context
            aggregated_context = await self.context_aggregator.aggregate_contexts(
                initial_contexts, []
            )
            
            # Step 4: Gap analysis
            gap_result = await self.gap_analyzer.analyze_context_sufficiency(
                question, aggregated_context
            )
            
            # Step 5: Conditional retry
            final_context = aggregated_context
            if not gap_result.is_sufficient:
                retry_result = await self.retry_service.execute_retry_if_needed(
                    gap_result, document_index
                )
                
                if retry_result and retry_result.additional_context:
                    final_context = await self.context_aggregator.aggregate_contexts(
                        [aggregated_context], retry_result.additional_context
                    )
            
            # Step 6: Generate answer
            answer = await self.llm_service.generate_answer_from_ranked_chunks(
                question, [], None  # Will need to adapt this based on actual implementation
            )
            
            return answer
            
        except Exception as e:
            logger.error(f"Error processing single question: {str(e)}")
            return "I apologize, but I encountered an error while processing your question."
    
    def configure_pipeline(
        self, 
        enable_gap_analysis: bool = True, 
        enable_retry: bool = True,
        max_retry_iterations: int = 1
    ):
        """
        Configure pipeline behavior.
        
        Args:
            enable_gap_analysis: Whether to enable gap analysis
            enable_retry: Whether to enable retry mechanism
            max_retry_iterations: Maximum retry iterations (should be 1 for latency)
        """
        self.enable_gap_analysis = enable_gap_analysis
        self.enable_retry = enable_retry
        self.max_retry_iterations = min(max_retry_iterations, 1)  # Enforce max 1 iteration
        
        logger.info(f"Pipeline configured - Gap Analysis: {enable_gap_analysis}, "
                   f"Retry: {enable_retry}, Max Retries: {self.max_retry_iterations}")
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """
        Get current pipeline status and configuration.
        
        Returns:
            Dictionary with pipeline status
        """
        return {
            "pipeline_enabled": True,
            "gap_analysis_enabled": self.enable_gap_analysis,
            "retry_enabled": self.enable_retry,
            "max_retry_iterations": self.max_retry_iterations,
            "services_status": {
                "query_decomposer": self.query_decomposer is not None,
                "subquery_processor": self.subquery_processor is not None,
                "gap_analyzer": self.gap_analyzer is not None,
                "retry_service": self.retry_service is not None,
                "context_aggregator": self.context_aggregator is not None,
                "llm_service": self.llm_service is not None,
                "document_processor": self.document_processor is not None,
                "document_cache": self.document_cache is not None
            },
            "performance_metrics": self._get_performance_summary()
        }
    
    def _get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary from all services."""
        try:
            return {
                "decomposition_cache_stats": self.query_decomposer.get_cache_stats(),
                "document_cache_stats": self.document_cache.get_cache_stats(),
                "decomposition_performance": self.query_decomposer.get_performance_metrics()
            }
        except Exception as e:
            logger.warning(f"Could not get performance summary: {e}")
            return {"error": "Performance metrics unavailable"}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check of all pipeline components.
        
        Returns:
            Health check results
        """
        health_status = {
            "overall_status": "healthy",
            "timestamp": time.time(),
            "components": {}
        }
        
        # Check each service including Cohere reranker
        services_to_check = [
            ("query_decomposer", self.query_decomposer),
            ("subquery_processor", self.subquery_processor),
            ("gap_analyzer", self.gap_analyzer),
            ("retry_service", self.retry_service),
            ("context_aggregator", self.context_aggregator),
            ("llm_service", self.llm_service),
            ("document_processor", self.document_processor),
            ("document_cache", self.document_cache),
            ("cohere_reranker", cohere_reranker)
        ]
        
        for service_name, service in services_to_check:
            try:
                # Basic availability check
                if service is None:
                    health_status["components"][service_name] = {
                        "status": "unhealthy",
                        "error": "Service not initialized"
                    }
                    health_status["overall_status"] = "degraded"
                else:
                    health_status["components"][service_name] = {
                        "status": "healthy",
                        "initialized": True
                    }
                    
                    # Add service-specific health info if available
                    if hasattr(service, 'get_cache_stats'):
                        health_status["components"][service_name]["cache_stats"] = service.get_cache_stats()
                    
                    # Special handling for Cohere reranker health check
                    if service_name == "cohere_reranker" and hasattr(service, 'health_check'):
                        try:
                            cohere_health = await service.health_check()
                            health_status["components"][service_name].update(cohere_health)
                            if cohere_health.get("status") != "healthy":
                                health_status["overall_status"] = "degraded"
                        except Exception as e:
                            health_status["components"][service_name]["cohere_error"] = str(e)
                    
            except Exception as e:
                health_status["components"][service_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                health_status["overall_status"] = "degraded"
        
        return health_status
    
    async def benchmark_pipeline(
        self, 
        test_questions: List[str], 
        document_url: str,
        iterations: int = 1
    ) -> Dict[str, Any]:
        """
        Benchmark the advanced pipeline performance.
        
        Args:
            test_questions: List of test questions
            document_url: Test document URL
            iterations: Number of benchmark iterations
            
        Returns:
            Benchmark results
        """
        logger.info(f"Starting pipeline benchmark with {len(test_questions)} questions, {iterations} iterations")
        
        benchmark_results = {
            "test_questions": len(test_questions),
            "iterations": iterations,
            "results": []
        }
        
        # Process document once
        document_index = await self._process_document(document_url)
        
        for iteration in range(iterations):
            iteration_start = time.time()
            
            try:
                # Process questions through advanced pipeline
                answers = await self._process_questions_advanced(
                    test_questions, document_index, f"benchmark_{iteration}"
                )
                
                iteration_time = time.time() - iteration_start
                
                benchmark_results["results"].append({
                    "iteration": iteration + 1,
                    "success": True,
                    "processing_time": round(iteration_time, 3),
                    "questions_processed": len(test_questions),
                    "answers_generated": len(answers),
                    "avg_time_per_question": round(iteration_time / len(test_questions), 3)
                })
                
            except Exception as e:
                iteration_time = time.time() - iteration_start
                benchmark_results["results"].append({
                    "iteration": iteration + 1,
                    "success": False,
                    "processing_time": round(iteration_time, 3),
                    "error": str(e)
                })
        
        # Calculate summary statistics
        successful_runs = [r for r in benchmark_results["results"] if r["success"]]
        if successful_runs:
            processing_times = [r["processing_time"] for r in successful_runs]
            benchmark_results["summary"] = {
                "success_rate": round(len(successful_runs) / iterations * 100, 1),
                "avg_processing_time": round(sum(processing_times) / len(processing_times), 3),
                "min_processing_time": round(min(processing_times), 3),
                "max_processing_time": round(max(processing_times), 3),
                "total_questions_processed": sum(r.get("questions_processed", 0) for r in successful_runs)
            }
        else:
            benchmark_results["summary"] = {
                "success_rate": 0.0,
                "error": "All benchmark iterations failed"
            }
        
        logger.info(f"Benchmark completed: {benchmark_results['summary']}")
        return benchmark_results