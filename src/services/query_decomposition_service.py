"""Query decomposition service for breaking complex questions into atomic sub-queries using GPT-4.1 with HyDE enhancement."""

import json
import logging
import time
import re
from typing import List, Dict, Any, Optional
from openai import AsyncAzureOpenAI
from cachetools import LRUCache

from src.config import settings
from src.models.schemas import SubQuery
from src.services.multiprocessing_manager import mp_manager, process_llm_parallel
from src.services.decomposition_monitor import decomposition_monitor
# HyDE functionality removed
# Chain-of-thought functionality removed

logger = logging.getLogger(__name__)


class QueryDecompositionService:
    """Service for decomposing complex user questions into atomic sub-queries using GPT-4.1."""
    
    def __init__(self):
        self.client = AsyncAzureOpenAI(
            api_key=settings.AZURE_OPENAI_API_KEY,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT
        )
        self.model = settings.AZURE_OPENAI_MODEL
        
        # Cache for repeated question decompositions
        self.decomposition_cache = LRUCache(maxsize=500)
        
        # Configuration - optimized for speed while preserving accuracy
        self.max_subqueries = 3  # Limit to 3 sub-queries max for faster processing
        self.timeout = getattr(settings, 'DECOMPOSITION_TIMEOUT', 30)
        
    async def decompose_query(self, user_question: str) -> List[str]:
        """
        Decompose a complex user question into atomic sub-queries.
        
        Args:
            user_question: The original user question to decompose
            
        Returns:
            List of atomic sub-queries
        """
        start_time = time.time()
        decomposition_monitor.record_request(1)
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(user_question)
            if cache_key in self.decomposition_cache:
                logger.info(f"Using cached decomposition for question: {user_question[:50]}...")
                decomposition_monitor.record_cache_hit()
                subqueries = self.decomposition_cache[cache_key]
                decomposition_monitor.record_success(time.time() - start_time, len(subqueries))
                return subqueries
            
            decomposition_monitor.record_cache_miss()
            
            # Call LLM for decomposition
            subqueries = await self._call_decomposition_llm(user_question)
            
            # Cache the result
            self.decomposition_cache[cache_key] = subqueries
            
            processing_time = time.time() - start_time
            decomposition_monitor.record_success(processing_time, len(subqueries))
            
            logger.info(f"Decomposed question into {len(subqueries)} sub-queries in {processing_time:.2f}s")
            return subqueries
            
        except Exception as e:
            logger.error(f"Error decomposing query: {str(e)}")
            decomposition_monitor.record_failure()
            decomposition_monitor.record_fallback_usage()
            # Fallback: return original question as single sub-query
            return [user_question] 
   
    async def decompose_queries_parallel(self, user_questions: List[str]) -> List[List[str]]:
        """
        Decompose multiple questions in parallel using worker pools.
        
        Args:
            user_questions: List of user questions to decompose
            
        Returns:
            List of lists, where each inner list contains sub-queries for the corresponding question
        """
        try:
            logger.info(f"Decomposing {len(user_questions)} questions in parallel")
            
            # Use thread pool for I/O-bound LLM API calls
            results = await process_llm_parallel(
                self._decompose_query_sync,
                user_questions
            )
            
            logger.info(f"Successfully decomposed {len(results)} questions in parallel")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel query decomposition: {str(e)}")
            # Fallback: return original questions as single sub-queries
            return [[question] for question in user_questions]
    
    async def _call_decomposition_llm(self, question: str) -> List[str]:
        """
        Call GPT-4.1 to decompose a question into sub-queries.
        
        Args:
            question: The question to decompose
            
        Returns:
            List of sub-queries
        """
        try:
            prompt = self._format_decomposition_prompt(question)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_decomposition_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent decomposition
                max_tokens=1000,
                timeout=self.timeout
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse the JSON response
            subqueries = self._parse_decomposition_response(response_text)
            
            # Validate and limit number of sub-queries
            if len(subqueries) > self.max_subqueries:
                logger.warning(f"Truncating {len(subqueries)} sub-queries to {self.max_subqueries}")
                subqueries = subqueries[:self.max_subqueries]
            
            # Log token usage
            if response.usage:
                logger.info(f"Decomposition LLM tokens used - Prompt: {response.usage.prompt_tokens}, "
                           f"Completion: {response.usage.completion_tokens}, "
                           f"Total: {response.usage.total_tokens}")
            
            return subqueries if subqueries else [question]
            
        except Exception as e:
            logger.error(f"Error calling decomposition LLM: {str(e)}")
            return [question]  # Fallback to original question    

    def _format_decomposition_prompt(self, question: str) -> str:
        """
        Format the prompt for query decomposition.
        
        Args:
            question: The question to decompose
            
        Returns:
            Formatted prompt
        """
        return f"""You are a legal and insurance policy analyst. Your task is to decompose a complex user question into a maximum of 3 comprehensive sub-questions that together cover ALL aspects of the original question.

CRITICAL GUIDELINES:
1. Create MAXIMUM 3 sub-questions that comprehensively cover the entire original question
2. Each sub-question should be broad enough to capture multiple related aspects
3. Ensure complete coverage - nothing from the original question should be missed
4. For insurance/legal questions, group related concepts: definitions+conditions, limits+exclusions, procedures+requirements
5. If the question is already focused, return it as a single sub-question
6. Prioritize comprehensive coverage over atomic decomposition

User Question: {question}

Decompose this into a JSON list of 1-3 comprehensive strings that together cover everything in the original question. Return only the JSON array, no additional text.

Example format: ["What are the definitions, conditions, and eligibility criteria for X?", "What are the coverage limits, exclusions, and waiting periods for Y?", "What are the procedures, requirements, and documentation needed for Z?"]"""
    
    def _get_decomposition_system_prompt(self) -> str:
        """Get the system prompt for query decomposition."""
        return """You are an expert at breaking down complex questions into simple, atomic sub-questions. 

Your role is to:
1. Analyze complex questions and identify their component parts
2. Create focused sub-questions that each target a single piece of information
3. Maintain the original question's intent and scope
4. Ensure sub-questions are clear and unambiguous
5. Return results as a clean JSON array of strings

Focus on creating sub-questions that will help retrieve comprehensive information to answer the original question completely."""
    
    def _parse_decomposition_response(self, response: str) -> List[str]:
        """
        Parse the LLM response to extract sub-queries.
        
        Args:
            response: Raw response from LLM
            
        Returns:
            List of sub-queries
        """
        try:
            # Try to parse as JSON
            response = response.strip()
            
            # Remove any markdown code block formatting
            if response.startswith("```json"):
                response = response[7:]
            if response.startswith("```"):
                response = response[3:]
            if response.endswith("```"):
                response = response[:-3]
            
            response = response.strip()
            
            # Parse JSON
            subqueries = json.loads(response)
            
            # Validate that it's a list of strings
            if isinstance(subqueries, list) and all(isinstance(q, str) for q in subqueries):
                # Filter out empty strings and clean up
                cleaned_queries = [q.strip() for q in subqueries if q.strip()]
                return cleaned_queries
            else:
                logger.error(f"Invalid decomposition response format: {response}")
                return []
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse decomposition JSON: {e}")
            logger.error(f"Response was: {response}")
            
            # Try to extract questions manually as fallback
            return self._extract_questions_fallback(response)
        except Exception as e:
            logger.error(f"Error parsing decomposition response: {e}")
            return []
    
    def _extract_questions_fallback(self, response: str) -> List[str]:
        """
        Fallback method to extract questions from malformed response.
        
        Args:
            response: Raw response text
            
        Returns:
            List of extracted questions
        """
        try:
            # Look for question-like patterns
            import re
            
            # Find lines that look like questions
            lines = response.split('\n')
            questions = []
            
            for line in lines:
                line = line.strip()
                # Remove list markers like "1.", "-", "*"
                line = re.sub(r'^[\d\-\*\.\)]+\s*', '', line)
                line = line.strip('"\'')
                
                # Check if it looks like a question
                if line and (line.endswith('?') or 'what' in line.lower() or 'how' in line.lower() or 'when' in line.lower()):
                    questions.append(line)
            
            return questions[:self.max_subqueries] if questions else []
            
        except Exception as e:
            logger.error(f"Error in fallback question extraction: {e}")
            return []
    
    def _generate_cache_key(self, question: str) -> str:
        """
        Generate a cache key for a question.
        
        Args:
            question: The question to generate key for
            
        Returns:
            Cache key
        """
        import hashlib
        return hashlib.md5(question.lower().strip().encode()).hexdigest()
    
    def _decompose_query_sync(self, question: str) -> List[str]:
        """
        Synchronous version of query decomposition for multiprocessing.
        
        Args:
            question: Question to decompose
            
        Returns:
            List of sub-queries
        """
        import asyncio
        
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async decomposition in this thread's event loop
            result = loop.run_until_complete(self.decompose_query(question))
            return result
        finally:
            loop.close()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            "cache_size": len(self.decomposition_cache),
            "cache_maxsize": self.decomposition_cache.maxsize,
            "cache_hits": getattr(self.decomposition_cache, 'hits', 0),
            "cache_misses": getattr(self.decomposition_cache, 'misses', 0)
        }
    
    def clear_cache(self) -> None:
        """Clear the decomposition cache."""
        self.decomposition_cache.clear()
        logger.info("Decomposition cache cleared")
    
    def validate_subquery_quality(self, original_question: str, subqueries: List[str]) -> float:
        """
        Validate the quality of decomposed sub-queries.
        
        Args:
            original_question: The original question
            subqueries: List of generated sub-queries
            
        Returns:
            Quality score between 0.0 and 1.0
        """
        if not subqueries:
            return 0.0
        
        quality_score = 0.0
        total_checks = 0
        
        # Check 1: All sub-queries should be questions
        question_count = sum(1 for q in subqueries if q.strip().endswith('?') or 
                           any(word in q.lower() for word in ['what', 'how', 'when', 'where', 'why', 'which', 'who']))
        if subqueries:
            quality_score += (question_count / len(subqueries)) * 0.3
        total_checks += 0.3
        
        # Check 2: Sub-queries should be atomic (not too complex)
        atomic_count = sum(1 for q in subqueries if len(q.split()) <= 15)  # Max 15 words per sub-query
        if subqueries:
            quality_score += (atomic_count / len(subqueries)) * 0.2
        total_checks += 0.2
        
        # Check 3: Sub-queries should relate to original question
        original_keywords = set(re.findall(r'\b\w+\b', original_question.lower()))
        related_count = 0
        for subquery in subqueries:
            subquery_keywords = set(re.findall(r'\b\w+\b', subquery.lower()))
            overlap = len(original_keywords.intersection(subquery_keywords))
            if overlap >= 2:  # At least 2 common keywords
                related_count += 1
        
        if subqueries:
            quality_score += (related_count / len(subqueries)) * 0.3
        total_checks += 0.3
        
        # Check 4: Reasonable number of sub-queries (not too many, not too few)
        count_score = 0.0
        if 1 <= len(subqueries) <= 5:
            count_score = 1.0
        elif 6 <= len(subqueries) <= 8:
            count_score = 0.8
        elif len(subqueries) > 8:
            count_score = 0.5
        
        quality_score += count_score * 0.2
        total_checks += 0.2
        
        return min(quality_score / total_checks if total_checks > 0 else 0.0, 1.0)
    
    def optimize_subqueries(self, subqueries: List[str]) -> List[str]:
        """
        Optimize sub-queries by removing duplicates and improving clarity.
        
        Args:
            subqueries: List of sub-queries to optimize
            
        Returns:
            Optimized list of sub-queries
        """
        if not subqueries:
            return subqueries
        
        # Remove duplicates while preserving order
        seen = set()
        optimized = []
        for query in subqueries:
            query_normalized = query.lower().strip()
            if query_normalized not in seen:
                seen.add(query_normalized)
                optimized.append(query.strip())
        
        # Remove very similar queries (high overlap in keywords)
        final_optimized = []
        for i, query in enumerate(optimized):
            is_similar = False
            query_words = set(re.findall(r'\b\w+\b', query.lower()))
            
            for existing_query in final_optimized:
                existing_words = set(re.findall(r'\b\w+\b', existing_query.lower()))
                overlap = len(query_words.intersection(existing_words))
                similarity = overlap / max(len(query_words), len(existing_words), 1)
                
                if similarity > 0.8:  # 80% similarity threshold
                    is_similar = True
                    break
            
            if not is_similar:
                final_optimized.append(query)
        
        return final_optimized
    
    async def decompose_with_fallback(self, user_question: str, max_retries: int = 2) -> List[str]:
        """
        Decompose query with fallback mechanisms for improved reliability.
        
        Args:
            user_question: The question to decompose
            max_retries: Maximum number of retry attempts
            
        Returns:
            List of sub-queries
        """
        for attempt in range(max_retries + 1):
            try:
                if attempt == 0:
                    # First attempt: normal decomposition
                    subqueries = await self.decompose_query(user_question)
                else:
                    # Retry attempts: use simpler prompt
                    subqueries = await self._decompose_with_simple_prompt(user_question)
                
                # Validate quality
                quality_score = self.validate_subquery_quality(user_question, subqueries)
                
                if quality_score >= 0.6:  # Acceptable quality threshold
                    return subqueries
                else:
                    logger.warning(f"Low quality decomposition (score: {quality_score:.2f}), attempt {attempt + 1}")
                    
            except Exception as e:
                logger.error(f"Decomposition attempt {attempt + 1} failed: {e}")
        
        # Final fallback: return original question
        decomposition_monitor.record_fallback_usage()
        logger.warning("All decomposition attempts failed, using original question")
        return [user_question]
    
    async def _decompose_with_simple_prompt(self, question: str) -> List[str]:
        """
        Decompose using a simpler, more reliable prompt.
        
        Args:
            question: Question to decompose
            
        Returns:
            List of sub-queries
        """
        simple_prompt = f"""Break this question into 2-5 simple questions. Return as JSON array.

Question: {question}

Format: ["question 1?", "question 2?"]"""
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You break complex questions into simple ones. Return only JSON."},
                    {"role": "user", "content": simple_prompt}
                ],
                temperature=0.0,
                max_tokens=500,
                timeout=15
            )
            
            response_text = response.choices[0].message.content.strip()
            return self._parse_decomposition_response(response_text)
            
        except Exception as e:
            logger.error(f"Simple prompt decomposition failed: {e}")
            return [question]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics."""
        base_metrics = decomposition_monitor.get_metrics()
        cache_stats = self.get_cache_stats()
        
        return {
            **base_metrics,
            "cache_statistics": cache_stats,
            "service_config": {
                "max_subqueries": self.max_subqueries,
                "timeout": self.timeout,
                "cache_maxsize": self.decomposition_cache.maxsize
            }
        }
    
    def filter_subqueries(self, subqueries: List[str], original_question: str) -> List[str]:
        """
        Filter sub-queries for quality and atomicity.
        
        Args:
            subqueries: List of sub-queries to validate
            original_question: Original question for context
            
        Returns:
            Filtered list of high-quality sub-queries
        """
        validated_queries = []
        filtered_count = 0
        
        for subquery in subqueries:
            if self._is_atomic_query(subquery) and self._is_relevant_query(subquery, original_question):
                validated_queries.append(subquery)
            else:
                logger.debug(f"Filtered out low-quality sub-query: {subquery}")
                filtered_count += 1
        
        # Record filtering metrics
        if filtered_count > 0:
            decomposition_monitor.record_validation_filter(filtered_count)
        
        # If all queries were filtered out, return original question
        if not validated_queries:
            logger.warning("All sub-queries filtered out, returning original question")
            decomposition_monitor.record_fallback_usage()
            return [original_question]
        
        return validated_queries
    
    def _is_atomic_query(self, query: str) -> bool:
        """
        Check if a query is atomic (focuses on single piece of information).
        
        Args:
            query: Query to check
            
        Returns:
            True if query is atomic
        """
        # Handle empty or very short queries
        if not query or len(query.strip()) < 3:
            return False
            
        # Basic checks for atomicity
        query_lower = query.lower()
        
        # Check for multiple question words (indicates compound question)
        question_words = ['what', 'when', 'where', 'who', 'why', 'how', 'which']
        question_count = sum(1 for word in question_words if word in query_lower)
        
        # Check for conjunctions that might indicate compound questions
        conjunctions = [' and ', ' or ', ' but ', ' also ', ' additionally']
        has_conjunctions = any(conj in query_lower for conj in conjunctions)
        
        # Check length (very long queries are likely not atomic)
        is_reasonable_length = len(query.split()) <= 20
        
        # Query is atomic if it has single focus and reasonable length
        return question_count <= 2 and not has_conjunctions and is_reasonable_length
    
    def _is_relevant_query(self, subquery: str, original_question: str) -> bool:
        """
        Check if sub-query is relevant to the original question.
        
        Args:
            subquery: Sub-query to check
            original_question: Original question
            
        Returns:
            True if sub-query is relevant
        """
        # Extract key terms from original question
        import re
        
        # Remove common stop words and extract meaningful terms
        stop_words = {'the', 'is', 'are', 'was', 'were', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        
        original_terms = set(re.findall(r'\b\w+\b', original_question.lower()))
        subquery_terms = set(re.findall(r'\b\w+\b', subquery.lower()))
        
        # Remove stop words
        original_terms -= stop_words
        subquery_terms -= stop_words
        
        # Check for overlap
        overlap = len(original_terms & subquery_terms)
        total_original = len(original_terms)
        
        # Require at least 20% overlap or presence of key domain terms
        domain_terms = {'policy', 'insurance', 'coverage', 'premium', 'claim', 'benefit', 'waiting', 'period', 'condition', 'exclusion'}
        has_domain_terms = bool(subquery_terms & domain_terms)
        
        relevance_score = overlap / max(total_original, 1)
        return relevance_score >= 0.2 or has_domain_terms
    
    async def get_decomposition_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for decomposition operations.
        
        Returns:
            Dictionary with performance metrics
        """
        cache_stats = self.get_cache_stats()
        
        return {
            "cache_stats": cache_stats,
            "max_subqueries": self.max_subqueries,
            "timeout_seconds": self.timeout,
            "model_used": self.model,
            "cache_hit_rate": cache_stats.get("cache_hits", 0) / max(cache_stats.get("cache_hits", 0) + cache_stats.get("cache_misses", 0), 1)
        }
    
    def optimize_batch_size(self, num_questions: int) -> int:
        """
        Optimize batch size for parallel processing based on number of questions.
        
        Args:
            num_questions: Total number of questions to process
            
        Returns:
            Optimal batch size
        """
        # Dynamic batch sizing based on load
        if num_questions <= 5:
            return num_questions  # Process all at once for small batches
        elif num_questions <= 20:
            return 5  # Medium batches
        else:
            return 10  # Larger batches for high volume
    
    async def decompose_queries_with_validation(self, user_questions: List[str]) -> List[List[str]]:
        """
        Decompose multiple questions with quality validation.
        
        Args:
            user_questions: List of user questions to decompose
            
        Returns:
            List of validated sub-query lists
        """
        try:
            logger.info(f"Decomposing {len(user_questions)} questions with validation")
            
            # First decompose all questions
            all_subqueries = await self.decompose_queries_parallel(user_questions)
            
            # Then validate each set of sub-queries
            validated_results = []
            for original_question, subqueries in zip(user_questions, all_subqueries):
                validated_subqueries = self.validate_subquery_quality(subqueries, original_question)
                validated_results.append(validated_subqueries)
            
            logger.info(f"Validation complete for {len(validated_results)} question sets")
            return validated_results
            
        except Exception as e:
            logger.error(f"Error in validated decomposition: {str(e)}")
            # Fallback: return original questions as single sub-queries
            return [[question] for question in user_questions]
    
    def get_monitoring_data(self) -> Dict[str, Any]:
        """
        Get comprehensive monitoring data for the decomposition service.
        
        Returns:
            Dictionary with monitoring data
        """
        return {
            "performance_summary": decomposition_monitor.get_performance_summary(),
            "health_status": decomposition_monitor.get_health_status(),
            "cache_stats": self.get_cache_stats(),
            "configuration": {
                "max_subqueries": self.max_subqueries,
                "timeout_seconds": self.timeout,
                "model": self.model,
                "cache_maxsize": self.decomposition_cache.maxsize
            }
        }