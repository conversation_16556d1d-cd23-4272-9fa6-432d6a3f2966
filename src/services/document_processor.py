"""Document processing service for handling PDFs, DOCX, and email documents."""

import asyncio
import logging
import tempfile
import base64
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
import aiohttp
import aiofiles
import requests
import json

from src.config import settings
from src.models.schemas import DocumentChunk
from src.services.multiprocessing_manager import mp_manager, process_documents_parallel, process_ocr_parallel

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Service for processing various document types with multiprocessing support."""
    
    def __init__(self):
        self.azureai_endpoint = settings.AZUREAI_ENDPOINT
        self.azureai_api_key = settings.AZUREAI_API_KEY
        
    async def process_document_from_url(self, document_url: str) -> List[DocumentChunk]:
        """
        Process a document from a URL and return chunks.
        
        Args:
            document_url: URL to the document
            
        Returns:
            List of document chunks
        """
        try:
            # Download document
            document_path = await self._download_document(document_url)
            is_local_file = urlparse(document_url).scheme == 'file'
            
            # Extract text based on file type
            text_content = await self._extract_text(document_path)
            
            # Clean up temporary file (but not local files)
            if not is_local_file:
                document_path.unlink(missing_ok=True)
            
            # Chunk the text
            chunks = await self._chunk_text(text_content, document_url)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Error processing document from {document_url}: {str(e)}")
            raise
    
    async def process_documents_parallel(self, document_urls: List[str]) -> List[List[DocumentChunk]]:
        """
        Process multiple documents in parallel using multiprocessing.
        
        Args:
            document_urls: List of document URLs to process
            
        Returns:
            List of document chunk lists (one per document)
        """
        try:
            logger.info(f"Processing {len(document_urls)} documents in parallel")
            
            # Use multiprocessing for parallel document processing
            results = await process_documents_parallel(
                self._process_single_document_sync, 
                document_urls
            )
            
            logger.info(f"Successfully processed {len(results)} documents in parallel")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel document processing: {str(e)}")
            raise
    
    async def _download_document(self, url: str) -> Path:
        """Download document from URL to temporary file or handle local file URLs."""
        parsed_url = urlparse(url)
        
        # Handle local file URLs
        if parsed_url.scheme == 'file':
            local_path = Path(parsed_url.path)
            if local_path.exists():
                return local_path
            else:
                raise FileNotFoundError(f"Local file not found: {local_path}")
        
        # Handle HTTP/HTTPS URLs
        file_extension = Path(parsed_url.path).suffix or '.pdf'
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response.raise_for_status()
                
                # Create temporary file
                temp_file = tempfile.NamedTemporaryFile(
                    delete=False, 
                    suffix=file_extension
                )
                temp_path = Path(temp_file.name)
                
                # Write content to file
                async with aiofiles.open(temp_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                
                return temp_path
    
    async def _extract_text(self, document_path: Path) -> str:
        """Extract text from document based on file type."""
        file_extension = document_path.suffix.lower()
        
        if file_extension == '.pdf':
            return await self._extract_pdf_text(document_path)
        elif file_extension in ['.docx', '.doc']:
            return await self._extract_docx_text(document_path)
        elif file_extension in ['.txt', '.md']:
            return await self._extract_text_file(document_path)
        else:
            # Try OCR for other formats
            return await self._extract_with_ocr(document_path)
    
    async def _extract_pdf_text(self, pdf_path: Path) -> str:
        """Extract text from PDF using OCR if needed."""
        try:
            # First try standard PDF text extraction
            import PyPDF2
            
            text_content = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"
            
            # If text extraction yields little content, use OCR
            if len(text_content.strip()) < 100:
                text_content = await self._extract_with_ocr(pdf_path)
                
            return text_content
            
        except Exception as e:
            logger.warning(f"Standard PDF extraction failed: {e}. Trying OCR...")
            return await self._extract_with_ocr(pdf_path)
    
    async def _extract_docx_text(self, docx_path: Path) -> str:
        """Extract text from DOCX file."""
        try:
            from docx import Document
            
            doc = Document(docx_path)
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
                
            return text_content
            
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            raise
    
    async def _extract_text_file(self, text_path: Path) -> str:
        """Extract text from plain text file."""
        async with aiofiles.open(text_path, 'r', encoding='utf-8') as f:
            return await f.read()
    
    async def _extract_with_ocr(self, document_path: Path) -> str:
        """Extract text using Mistral OCR or fallback OCR."""
        if self.azureai_endpoint and self.azureai_api_key:
            return await self._mistral_ocr(document_path)
        else:
            return await self._fallback_ocr(document_path)
    
    async def _mistral_ocr(self, document_path: Path) -> str:
        """Use Mistral OCR for document processing."""
        try:
            logger.info(f"Processing document with Mistral OCR: {document_path}")
            
            # Determine MIME type
            mime_ext = document_path.suffix.lower()
            mime_type = {
                ".pdf": "application/pdf",
                ".png": "image/png", 
                ".jpg": "image/jpeg",
                ".jpeg": "image/jpeg"
            }.get(mime_ext, "application/octet-stream")
            
            # Read and encode file
            with open(document_path, "rb") as f:
                raw_bytes = f.read()
            
            b64_content = base64.b64encode(raw_bytes).decode("utf-8")
            data_uri = f"data:{mime_type};base64,{b64_content}"
            
            # Build payload
            payload = {
                "model": "mistral-ocr-2503",
                "document": {
                    "type": "document_url",
                    "document_url": data_uri
                },
                "include_image_base64": True
            }
            
            # Call OCR endpoint
            url = f"{self.azureai_endpoint}/v1/ocr"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.azureai_api_key}"
            }
            
            # Use requests for synchronous call, then run in executor
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.post(url, headers=headers, json=payload)
            )
            
            response.raise_for_status()
            ocr_result = response.json()
            
            # Extract markdown content from all pages
            all_markdown = []
            for page in ocr_result.get("pages", []):
                md_content = page.get("markdown", "")
                if md_content.strip():
                    all_markdown.append(md_content.strip())
            
            # Combine all pages
            combined_text = "\n\n".join(all_markdown)
            
            logger.info(f"Mistral OCR successful. Extracted {len(combined_text)} characters")
            return combined_text
            
        except Exception as e:
            logger.error(f"Mistral OCR failed: {e}")
            return await self._fallback_ocr(document_path)
    
    async def _fallback_ocr(self, document_path: Path) -> str:
        """Fallback OCR using pytesseract."""
        try:
            import pytesseract
            from PIL import Image
            import pdf2image
            
            if document_path.suffix.lower() == '.pdf':
                # Convert PDF to images
                images = pdf2image.convert_from_path(document_path)
                text_content = ""
                
                for image in images:
                    text_content += pytesseract.image_to_string(image) + "\n"
                    
                return text_content
            else:
                # Process image directly
                image = Image.open(document_path)
                return pytesseract.image_to_string(image)
                
        except Exception as e:
            logger.error(f"Fallback OCR failed: {e}")
            raise
    
    async def _chunk_text(self, text: str, source_url: str) -> List[DocumentChunk]:
        """Chunk text using semantic chunking for better context preservation."""
        from src.services.chunking_service import SemanticChunker
        
        try:
            # Use semantic chunking
            chunker = SemanticChunker()
            chunks = await chunker.semantic_chunk(text, source_url)
            
            logger.info(f"Successfully created {len(chunks)} semantic chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Semantic chunking failed: {e}. Using fallback chunking.")
            
            # Fallback to simple chunking
            return self._fallback_chunk_text(text, source_url)
    
    def _fallback_chunk_text(self, text: str, source_url: str) -> List[DocumentChunk]:
        """Fallback chunking method."""
        chunk_size = settings.CHUNK_SIZE
        overlap = settings.CHUNK_OVERLAP
        
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(text):
                # Find the last sentence ending within the chunk
                last_sentence_end = max(
                    text.rfind('.', start, end),
                    text.rfind('!', start, end),
                    text.rfind('?', start, end)
                )
                if last_sentence_end > start:
                    end = last_sentence_end + 1
                else:
                    # Fall back to word boundary
                    last_space = text.rfind(' ', start, end)
                    if last_space > start:
                        end = last_space
            
            chunk_content = text[start:end].strip()
            
            if chunk_content:
                chunk = DocumentChunk(
                    id=f"{hash(source_url)}_{chunk_index}",
                    content=chunk_content,
                    metadata={
                        "source": source_url,
                        "chunk_index": chunk_index,
                        "chunking_method": "fallback",
                        "start_pos": start,
                        "end_pos": end
                    }
                )
                chunks.append(chunk)
                chunk_index += 1
            
            start = end - overlap if end < len(text) else len(text)
        
        # Update total_chunks in metadata
        for chunk in chunks:
            chunk.metadata["total_chunks"] = len(chunks)
        
        return chunks
    
    def _process_single_document_sync(self, document_url: str) -> List[DocumentChunk]:
        """
        Synchronous version of document processing for multiprocessing.
        This method runs in a separate process.
        """
        import asyncio
        
        # Create new event loop for this process
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async processing in this process's event loop
            result = loop.run_until_complete(self.process_document_from_url(document_url))
            return result
        finally:
            loop.close()
    
    async def extract_text_parallel(self, document_paths: List[Path], formats: List[str]) -> List[str]:
        """
        Extract text from multiple documents in parallel.
        
        Args:
            document_paths: List of document file paths
            formats: List of document formats corresponding to paths
            
        Returns:
            List of extracted text content
        """
        try:
            logger.info(f"Extracting text from {len(document_paths)} documents in parallel")
            
            # Prepare arguments for parallel processing
            extract_args = list(zip(document_paths, formats))
            
            # Use multiprocessing for parallel text extraction
            results = await process_ocr_parallel(
                self._extract_text_sync, 
                extract_args
            )
            
            logger.info(f"Successfully extracted text from {len(results)} documents")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel text extraction: {str(e)}")
            raise
    
    def _extract_text_sync(self, args: tuple) -> str:
        """
        Synchronous version of text extraction for multiprocessing.
        
        Args:
            args: Tuple of (document_path, format)
            
        Returns:
            Extracted text content
        """
        document_path, format_hint = args
        
        # Import required modules in the worker process
        import PyPDF2
        from docx import Document
        import pytesseract
        from PIL import Image
        import pdf2image
        import requests
        import base64
        
        try:
            file_extension = Path(document_path).suffix.lower()
            
            if file_extension == '.pdf':
                return self._extract_pdf_text_sync(document_path)
            elif file_extension in ['.docx', '.doc']:
                return self._extract_docx_text_sync(document_path)
            elif file_extension in ['.txt', '.md']:
                return self._extract_text_file_sync(document_path)
            else:
                # Try OCR for other formats
                return self._extract_with_ocr_sync(document_path)
                
        except Exception as e:
            logger.error(f"Error in sync text extraction for {document_path}: {e}")
            return ""
    
    def _extract_pdf_text_sync(self, pdf_path: Path) -> str:
        """Synchronous PDF text extraction."""
        import PyPDF2
        
        try:
            text_content = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"
            
            # If text extraction yields little content, use OCR
            if len(text_content.strip()) < 100:
                text_content = self._extract_with_ocr_sync(pdf_path)
                
            return text_content
            
        except Exception as e:
            logger.warning(f"Standard PDF extraction failed: {e}. Trying OCR...")
            return self._extract_with_ocr_sync(pdf_path)
    
    def _extract_docx_text_sync(self, docx_path: Path) -> str:
        """Synchronous DOCX text extraction."""
        from docx import Document
        
        try:
            doc = Document(docx_path)
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
                
            return text_content
            
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            return ""
    
    def _extract_text_file_sync(self, text_path: Path) -> str:
        """Synchronous text file extraction."""
        try:
            with open(text_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading text file: {e}")
            return ""
    
    def _extract_with_ocr_sync(self, document_path: Path) -> str:
        """Synchronous OCR extraction."""
        if self.azureai_endpoint and self.azureai_api_key:
            return self._mistral_ocr_sync(document_path)
        else:
            return self._fallback_ocr_sync(document_path)
    
    def _mistral_ocr_sync(self, document_path: Path) -> str:
        """Synchronous Mistral OCR processing."""
        import requests
        import base64
        
        try:
            logger.info(f"Processing document with Mistral OCR: {document_path}")
            
            # Determine MIME type
            mime_ext = document_path.suffix.lower()
            mime_type = {
                ".pdf": "application/pdf",
                ".png": "image/png", 
                ".jpg": "image/jpeg",
                ".jpeg": "image/jpeg"
            }.get(mime_ext, "application/octet-stream")
            
            # Read and encode file
            with open(document_path, "rb") as f:
                raw_bytes = f.read()
            
            b64_content = base64.b64encode(raw_bytes).decode("utf-8")
            data_uri = f"data:{mime_type};base64,{b64_content}"
            
            # Build payload
            payload = {
                "model": "mistral-ocr-2503",
                "document": {
                    "type": "document_url",
                    "document_url": data_uri
                },
                "include_image_base64": True
            }
            
            # Call OCR endpoint
            url = f"{self.azureai_endpoint}/v1/ocr"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.azureai_api_key}"
            }
            
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            ocr_result = response.json()
            
            # Extract markdown content from all pages
            all_markdown = []
            for page in ocr_result.get("pages", []):
                md_content = page.get("markdown", "")
                if md_content.strip():
                    all_markdown.append(md_content.strip())
            
            # Combine all pages
            combined_text = "\n\n".join(all_markdown)
            
            logger.info(f"Mistral OCR successful. Extracted {len(combined_text)} characters")
            return combined_text
            
        except Exception as e:
            logger.error(f"Mistral OCR failed: {e}")
            return self._fallback_ocr_sync(document_path)
    
    def _fallback_ocr_sync(self, document_path: Path) -> str:
        """Synchronous fallback OCR using pytesseract."""
        import pytesseract
        from PIL import Image
        import pdf2image
        
        try:
            if document_path.suffix.lower() == '.pdf':
                # Convert PDF to images
                images = pdf2image.convert_from_path(document_path)
                text_content = ""
                
                for image in images:
                    text_content += pytesseract.image_to_string(image) + "\n"
                    
                return text_content
            else:
                # Process image directly
                image = Image.open(document_path)
                return pytesseract.image_to_string(image)
                
        except Exception as e:
            logger.error(f"Fallback OCR failed: {e}")
            return ""