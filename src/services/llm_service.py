"""LLM service for generating contextual answers using Azure OpenAI GPT-4.1."""

import logging
from typing import List, Dict, Any, Optional, Tuple
from openai import AsyncAzureOpenAI

from src.config import settings
from src.models.schemas import RetrievalR<PERSON>ult, RankedChunk
from src.services.multiprocessing_manager import mp_manager, process_llm_parallel

logger = logging.getLogger(__name__)


class AzureLLMService:
    """Service for generating answers using Azure OpenAI GPT-4.1 with multiprocessing support."""
    
    def __init__(self):
        self.client = AsyncAzureOpenAI(
            api_key=settings.AZURE_OPENAI_API_KEY,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT
        )
        self.model = settings.AZURE_OPENAI_MODEL
# Chain-of-thought functionality removed
        
        # Circuit breaker state
        self.failure_count = 0
        self.last_failure_time = 0
        self.circuit_breaker_threshold = 5  # Open circuit after 5 c
        
    async def generate_answer(
        self, 
        query: str, 
        context_results: List[RetrievalResult],
        domain_context: Optional[str] = None
    ) -> str:
        """
        Generate an answer using LLM with retrieved context and robust retry logic.
        
        Args:
            query: User query
            context_results: Retrieved and reranked context chunks
            domain_context: Optional domain-specific context
            
        Returns:
            Generated answer
        """
        # Build context from retrieval results
        context = self._build_context(context_results)
        
        # Create prompt
        prompt = self._create_prompt(query, context, domain_context)
        
        # Try with robust retry logic
        return await self._generate_with_retry(query, prompt, max_retries=3)
    
    def _build_context(self, results: List[RetrievalResult]) -> str:
        """Build context string from retrieval results."""
        if not results:
            return "No relevant context found."
        
        context_parts = []
        for i, result in enumerate(results, 1):
            context_parts.append(f"[Context {i}]:\n{result.chunk.content}")
        
        return "\n\n".join(context_parts)
    
    def _create_prompt(self, query: str, context: str, domain_context: Optional[str] = None) -> str:
        """Create the prompt for the LLM."""
        prompt_parts = []
        
        if domain_context:
            prompt_parts.append(f"Domain Context:\n{domain_context}\n")
        
        prompt_parts.extend([
            f"Context Information:\n{context}\n",
            f"Question: {query}\n",
            "Instructions:",
            "- Provide a direct, professional answer based on the provided context",
            "- Be concise and specific - avoid lengthy explanations or recommendations",
            "- Include exact details like numbers, time periods, percentages, and conditions when available",
            "- If the context contains the answer, state it clearly and directly",
            "- If the context doesn't contain the specific information, provide a brief statement that the information is not available in the provided context",
            "- Do not include 'Missing Information' sections, recommendations, or suggestions to refer to other documents",
            "- Format your response as a natural, professional statement",
            "\nAnswer:"
        ])
        
        return "\n".join(prompt_parts)
    def _is_circuit_open(self) -> bool:
        """Check if circuit breaker is open."""
        import time
        
        if self.failure_count >= self.circuit_breaker_threshold:
            if time.time() - self.last_failure_time < self.circuit_breaker_timeout:
                return True
            else:
                # Reset circuit breaker after timeout
                self.failure_count = 0
                return False
        return False
    
    def _record_success(self):
        """Record successful request."""
        self.failure_count = 0
    
    def _record_failure(self):
        """Record failed request."""
        import time
        self.failure_count += 1
        self.last_failure_time = time.time()

    async def _generate_with_retry(self, query: str, prompt: str, max_retries: int = 3, domain: str = "general") -> str:
        """
        Generate answer with robust retry logic, exponential backoff, and circuit breaker.
        
        Args:
            query: User query
            prompt: Formatted prompt
            max_retries: Maximum number of retry attempts
            
        Returns:
            Generated answer
        """
        import asyncio
        import random
        
        # Check circuit breaker
        if self._is_circuit_open():
            logger.warning("Circuit breaker is open, using fallback answer")
            return await self._generate_fallback_answer(query, prompt)
        
        for attempt in range(max_retries + 1):
            try:
                # Generate response with Chain-of-Thought system prompt
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": self._get_system_prompt()
                        },
                        {
                            "role": "user", 
                            "content": prompt
                        }
                    ],
                    temperature=settings.AZURE_TEMPERATURE,
                    max_tokens=settings.AZURE_MAX_TOKENS,
                    timeout=30  # 30 second timeout
                )
                
                answer = response.choices[0].message.content.strip()
                
                # Record success
                self._record_success()
                
                # Log token usage
                if response.usage:
                    logger.info(f"LLM tokens used - Prompt: {response.usage.prompt_tokens}, "
                               f"Completion: {response.usage.completion_tokens}, "
                               f"Total: {response.usage.total_tokens}")
                
                return answer
                
            except Exception as e:
                error_msg = str(e).lower()
                
                # Record failure
                self._record_failure()
                
                # Check if this is a retryable error
                retryable_errors = [
                    'connection error', 'timeout', 'rate limit', 'server error',
                    'service unavailable', 'internal server error', 'bad gateway'
                ]
                
                is_retryable = any(err in error_msg for err in retryable_errors)
                
                if attempt < max_retries and is_retryable and not self._is_circuit_open():
                    # Exponential backoff with jitter
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"LLM request failed (attempt {attempt + 1}/{max_retries + 1}): {str(e)}. Retrying in {wait_time:.2f}s")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # Final attempt failed, non-retryable error, or circuit is open
                    logger.error(f"LLM request failed after {attempt + 1} attempts: {str(e)}")
                    
                    # Try to provide a meaningful fallback answer
                    return await self._generate_fallback_answer(query, prompt)
        
        # Should never reach here, but just in case
        return await self._generate_fallback_answer(query, prompt)
    
    async def _generate_fallback_answer(self, query: str, prompt: str) -> str:
        """
        Generate a fallback answer when LLM calls fail.
        
        Args:
            query: User query
            prompt: Original prompt
            
        Returns:
            Fallback answer
        """
        try:
            # Extract context from prompt for a simple rule-based response
            if "Context Information:" in prompt:
                context_start = prompt.find("Context Information:") + len("Context Information:")
                context_end = prompt.find("Question:")
                if context_end > context_start:
                    context = prompt[context_start:context_end].strip()
                    
                    # Simple keyword matching for common insurance terms
                    query_lower = query.lower()
                    context_lower = context.lower()
                    
                    if "grace period" in query_lower and "grace period" in context_lower:
                        # Extract grace period information
                        sentences = context.split('.')
                        for sentence in sentences:
                            if "grace period" in sentence.lower():
                                return sentence.strip() + "."
                    
                    elif "waiting period" in query_lower and ("waiting" in context_lower or "wait" in context_lower):
                        # Extract waiting period information
                        sentences = context.split('.')
                        for sentence in sentences:
                            if "waiting" in sentence.lower() or "wait" in sentence.lower():
                                return sentence.strip() + "."
                    
                    elif "coverage" in query_lower and "cover" in context_lower:
                        # Extract coverage information
                        sentences = context.split('.')
                        for sentence in sentences:
                            if "cover" in sentence.lower():
                                return sentence.strip() + "."
                    
                    # If no specific match, return first meaningful sentence
                    sentences = [s.strip() for s in context.split('.') if len(s.strip()) > 20]
                    if sentences:
                        return sentences[0] + "."
            
            # Final fallback
            return "I was unable to process this question due to a technical issue. Please try again."
            
        except Exception as e:
            logger.error(f"Error in fallback answer generation: {e}")
            return "I was unable to process this question due to a technical issue. Please try again."

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the LLM."""
        return """You are a professional insurance policy analyst. Your task is to provide direct, concise answers to questions about insurance policies based on the provided context.

Guidelines:
1. Provide direct, factual answers without unnecessary elaboration
2. Include specific details like time periods, percentages, amounts, and conditions when available
3. Use professional, clear language suitable for policy documentation
4. If the context contains the answer, state it directly and completely
5. If the context lacks specific information, briefly state that the information is not available in the provided context
6. Do not include recommendations, suggestions, or references to other documents
7. Do not use formatting like bold text, bullet points, or section headers
8. Provide answers in natural, flowing sentences

Your responses should be professional, accurate, and concise - similar to how a policy expert would answer questions about coverage details."""
    
    async def generate_batch_answers(
        self, 
        queries: List[str], 
        context_results_list: List[List[RetrievalResult]],
        domain_context: Optional[str] = None
    ) -> List[str]:
        """
        Generate answers for multiple queries efficiently with individual error handling.
        
        Args:
            queries: List of user queries
            context_results_list: List of context results for each query
            domain_context: Optional domain-specific context
            
        Returns:
            List of generated answers
        """
        try:
            # Process queries concurrently with individual error handling
            import asyncio
            
            async def safe_generate_answer(query: str, context_results: List[RetrievalResult], index: int) -> str:
                try:
                    return await self.generate_answer(query, context_results, domain_context)
                except Exception as e:
                    logger.error(f"Error processing query {index}: {str(e)}")
                    # Try to generate a fallback answer
                    try:
                        context = self._build_context(context_results)
                        prompt = self._create_prompt(query, context, domain_context)
                        return await self._generate_fallback_answer(query, prompt)
                    except Exception as fallback_error:
                        logger.error(f"Fallback also failed for query {index}: {str(fallback_error)}")
                        return "I was unable to process this question due to a technical issue. Please try again."
            
            tasks = [
                safe_generate_answer(query, context_results, i)
                for i, (query, context_results) in enumerate(zip(queries, context_results_list))
            ]
            
            answers = await asyncio.gather(*tasks)
            
            logger.info(f"Successfully generated {len(answers)} answers in batch")
            return answers
            
        except Exception as e:
            logger.error(f"Error in batch answer generation: {str(e)}")
            # Try to provide individual fallback answers
            fallback_answers = []
            for i, (query, context_results) in enumerate(zip(queries, context_results_list)):
                try:
                    context = self._build_context(context_results)
                    prompt = self._create_prompt(query, context, domain_context)
                    fallback_answer = await self._generate_fallback_answer(query, prompt)
                    fallback_answers.append(fallback_answer)
                except Exception:
                    fallback_answers.append("I was unable to process this question due to a technical issue. Please try again.")
            
            return fallback_answers
    
    async def _authenticate_azure(self) -> None:
        """Verify Azure OpenAI authentication."""
        try:
            # Test with a simple completion request
            test_response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            logger.info("Azure OpenAI LLM authentication successful")
        except Exception as e:
            logger.error(f"Azure OpenAI LLM authentication failed: {e}")
            raise
    
    def _build_context_from_ranked_chunks(self, ranked_chunks: List[RankedChunk]) -> str:
        """Build context string from ranked chunks with pruned content."""
        if not ranked_chunks:
            return "No relevant context found."
        
        context_parts = []
        for i, ranked_chunk in enumerate(ranked_chunks, 1):
            # Use pruned content if available, otherwise use original content
            content = ranked_chunk.pruned_content or ranked_chunk.chunk.content
            context_parts.append(f"[Context {i}]:\n{content}")
        
        return "\n\n".join(context_parts)
    
    async def generate_answer_from_ranked_chunks(
        self, 
        query: str, 
        ranked_chunks: List[RankedChunk],
        domain_context: Optional[str] = None
    ) -> str:
        """
        Generate an answer using LLM with ranked and pruned chunks.
        
        Args:
            query: User query
            ranked_chunks: Ranked and pruned context chunks
            domain_context: Optional domain-specific context
            
        Returns:
            Generated answer
        """
        # Build context from ranked chunks
        context = self._build_context_from_ranked_chunks(ranked_chunks)
        
        # Create prompt
        prompt = self._create_prompt(query, context, domain_context)
        
        # Use the same retry logic
        return await self._generate_with_retry(query, prompt, max_retries=3)


    async def generate_batch_answers_parallel(
        self, 
        queries: List[str], 
        context_results_list: List[List[RetrievalResult]],
        domain_context: Optional[str] = None
    ) -> List[str]:
        """
        Generate answers for multiple queries using multiprocessing for I/O optimization.
        
        Args:
            queries: List of user queries
            context_results_list: List of context results for each query
            domain_context: Optional domain-specific context
            
        Returns:
            List of generated answers
        """
        try:
            logger.info(f"Generating answers for {len(queries)} queries in parallel")
            
            # Prepare arguments for parallel processing
            query_args = [
                (query, context_results, domain_context)
                for query, context_results in zip(queries, context_results_list)
            ]
            
            # Use thread pool for I/O-bound LLM API calls
            results = await process_llm_parallel(
                self._generate_answer_sync,
                query_args
            )
            
            logger.info(f"Successfully generated {len(results)} answers in parallel")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel answer generation: {str(e)}")
            return ["Error processing questions. Please try again."] * len(queries)
    
    def _generate_answer_sync(self, *args) -> str:
        """
        Synchronous version of answer generation for multiprocessing.
        
        Args:
            *args: Either a single tuple (query, context_results, domain_context) 
                   or three separate arguments
            
        Returns:
            Generated answer
        """
        # Handle both tuple and unpacked arguments
        if len(args) == 1 and isinstance(args[0], (tuple, list)):
            # Single tuple argument
            query, context_results, domain_context = args[0]
        elif len(args) == 3:
            # Three separate arguments
            query, context_results, domain_context = args
        else:
            logger.error(f"Invalid arguments to _generate_answer_sync: {args}")
            return "Error: Invalid arguments for answer generation"
        
        import asyncio
        
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async answer generation in this thread's event loop
            result = loop.run_until_complete(
                self.generate_answer(query, context_results, domain_context)
            )
            return result
        finally:
            loop.close()
    
    async def generate_answers_with_workers(
        self, 
        query_context_pairs: List[Tuple[str, List[RetrievalResult]]],
        domain_context: Optional[str] = None
    ) -> List[str]:
        """
        Generate answers for query-context pairs using optimized worker pools.
        
        Args:
            query_context_pairs: List of (query, context_results) tuples
            domain_context: Optional domain-specific context
            
        Returns:
            List of generated answers
        """
        try:
            logger.info(f"Processing {len(query_context_pairs)} query-context pairs with workers")
            
            # Prepare arguments for parallel processing
            worker_args = [
                (query, context_results, domain_context)
                for query, context_results in query_context_pairs
            ]
            
            # Use thread pool for I/O-bound operations
            results = await process_llm_parallel(
                self._generate_answer_sync,
                worker_args
            )
            
            logger.info(f"Successfully processed {len(results)} queries with workers")
            return results
            
        except Exception as e:
            logger.error(f"Error in worker-based answer generation: {str(e)}")
            return ["Error processing questions. Please try again."] * len(query_context_pairs)
    
    async def generate_batch_answers_from_ranked_chunks_parallel(
        self,
        queries: List[str],
        ranked_chunks_list: List[List[RankedChunk]],
        domain_context: Optional[str] = None
    ) -> List[str]:
        """
        Generate answers for multiple queries using ranked chunks in parallel.
        
        Args:
            queries: List of user queries
            ranked_chunks_list: List of ranked chunks for each query
            domain_context: Optional domain-specific context
            
        Returns:
            List of generated answers
        """
        try:
            logger.info(f"Generating answers from ranked chunks for {len(queries)} queries in parallel")
            
            # Prepare arguments for parallel processing
            query_args = [
                (query, ranked_chunks, domain_context)
                for query, ranked_chunks in zip(queries, ranked_chunks_list)
            ]
            
            # Use thread pool for I/O-bound LLM API calls
            results = await process_llm_parallel(
                self._generate_answer_from_ranked_chunks_sync,
                query_args
            )
            
            logger.info(f"Successfully generated {len(results)} answers from ranked chunks in parallel")
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel ranked chunk answer generation: {str(e)}")
            return ["Error processing questions. Please try again."] * len(queries)
    
    def _generate_answer_from_ranked_chunks_sync(self, args: tuple) -> str:
        """
        Synchronous version of ranked chunk answer generation for multiprocessing.
        
        Args:
            args: Tuple of (query, ranked_chunks, domain_context)
            
        Returns:
            Generated answer
        """
        query, ranked_chunks, domain_context = args
        
        import asyncio
        
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async answer generation in this thread's event loop
            result = loop.run_until_complete(
                self.generate_answer_from_ranked_chunks(query, ranked_chunks, domain_context)
            )
            return result
        finally:
            loop.close()


# Backward compatibility alias
LLMService = AzureLLMService