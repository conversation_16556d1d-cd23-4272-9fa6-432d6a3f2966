"""Performance monitoring for query decomposition operations."""

import time
import logging
from typing import Dict, Any, List
from dataclasses import dataclass, field
from threading import Lock

logger = logging.getLogger(__name__)


@dataclass
class DecompositionMetrics:
    """Metrics for decomposition operations."""
    total_requests: int = 0
    successful_decompositions: int = 0
    failed_decompositions: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0
    total_subqueries_generated: int = 0
    average_subqueries_per_question: float = 0.0
    quality_scores: List[float] = field(default_factory=list)
    fallback_usage: int = 0


class DecompositionMonitor:
    """Monitor for tracking decomposition performance and quality."""
    
    def __init__(self):
        self.metrics = DecompositionMetrics()
        self._lock = Lock()
        self.start_time = time.time()
    
    def record_request(self, count: int = 1):
        """Record decomposition requests."""
        with self._lock:
            self.metrics.total_requests += count
    
    def record_success(self, processing_time: float, subquery_count: int, quality_score: float = 1.0):
        """Record successful decomposition."""
        with self._lock:
            self.metrics.successful_decompositions += 1
            self.metrics.total_processing_time += processing_time
            self.metrics.total_subqueries_generated += subquery_count
            self.metrics.quality_scores.append(quality_score)
            
            # Update averages
            if self.metrics.successful_decompositions > 0:
                self.metrics.average_processing_time = (
                    self.metrics.total_processing_time / self.metrics.successful_decompositions
                )
                self.metrics.average_subqueries_per_question = (
                    self.metrics.total_subqueries_generated / self.metrics.successful_decompositions
                )
    
    def record_failure(self, processing_time: float):
        """Record failed decomposition."""
        with self._lock:
            self.metrics.failed_decompositions += 1
            self.metrics.total_processing_time += processing_time
    
    def record_cache_hit(self):
        """Record cache hit."""
        with self._lock:
            self.metrics.cache_hits += 1
    
    def record_cache_miss(self):
        """Record cache miss."""
        with self._lock:
            self.metrics.cache_misses += 1
    
    def record_fallback_usage(self):
        """Record fallback mechanism usage."""
        with self._lock:
            self.metrics.fallback_usage += 1
    
    def record_validation_filter(self, filtered_count: int):
        """Record validation filtering operations."""
        # This can be extended to track filtering metrics if needed
        logger.info(f"Filtered {filtered_count} low-quality sub-queries")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        with self._lock:
            uptime = time.time() - self.start_time
            success_rate = (
                self.metrics.successful_decompositions / max(self.metrics.total_requests, 1) * 100
            )
            cache_hit_rate = (
                self.metrics.cache_hits / max(self.metrics.cache_hits + self.metrics.cache_misses, 1) * 100
            )
            average_quality = (
                sum(self.metrics.quality_scores) / max(len(self.metrics.quality_scores), 1)
            )
            
            return {
                "uptime_seconds": uptime,
                "total_requests": self.metrics.total_requests,
                "successful_decompositions": self.metrics.successful_decompositions,
                "failed_decompositions": self.metrics.failed_decompositions,
                "success_rate_percent": success_rate,
                "cache_hits": self.metrics.cache_hits,
                "cache_misses": self.metrics.cache_misses,
                "cache_hit_rate_percent": cache_hit_rate,
                "total_processing_time": self.metrics.total_processing_time,
                "average_processing_time": self.metrics.average_processing_time,
                "total_subqueries_generated": self.metrics.total_subqueries_generated,
                "average_subqueries_per_question": self.metrics.average_subqueries_per_question,
                "average_quality_score": average_quality,
                "fallback_usage": self.metrics.fallback_usage,
                "requests_per_second": self.metrics.total_requests / max(uptime, 1)
            }
    
    def reset_metrics(self):
        """Reset all metrics."""
        with self._lock:
            self.metrics = DecompositionMetrics()
            self.start_time = time.time()
            logger.info("Decomposition metrics reset")


# Global monitor instance
decomposition_monitor = DecompositionMonitor()