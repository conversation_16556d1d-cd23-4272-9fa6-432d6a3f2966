{"info": {"name": "HackRX API Test Collection", "description": "Test collection for HackRX hackathon endpoint", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "HackRX Run Query", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer 7dJCrCMV0qWZAqTGLm6fUwwQgQ5RhZzP2SXbHLSmArM5tD0vEI1UdmWOAZjzZHtbNs7U3VczA7PlmI0cYcRrGQ", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"question\": \"What are the eligibility criteria for participating in the hackathon?\",\n  \"session_id\": \"test_session_001\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**************:8000/api/v1/hackrx/run", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000", "path": ["api", "v1", "hackrx", "run"]}}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://**************:8000/health", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000", "path": ["health"]}}, "response": []}, {"name": "API Info", "request": {"method": "GET", "header": [], "url": {"raw": "http://**************:8000/", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000", "path": [""]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://**************:8000", "type": "string"}, {"key": "api_token", "value": "7dJCrCMV0qWZAqTGLm6fUwwQgQ5RhZzP2SXbHLSmArM5tD0vEI1UdmWOAZjzZHtbNs7U3VczA7PlmI0cYcRrGQ", "type": "string"}]}