# LLM-Powered Intelligent Query-Retrieval System

A professional-grade document processing and query system designed for insurance, legal, HR, and compliance domains. This system processes documents from URLs, performs semantic search using FAISS, implements reranking for improved relevance, and provides contextual answers using GPT-4.

## 🏗️ System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Document      │    │   LLM Parser     │    │  Embedding      │
│   Processing    │───▶│   (Mistral OCR)  │───▶│  Generation     │
│   (PDF/DOCX)    │    │                  │    │  (OpenAI)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   JSON Output   │    │   Logic          │    │   FAISS Vector  │
│   (Structured   │◀───│   Evaluation     │◀───│   Search        │
│   Response)     │    │   (GPT-4)        │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                         │
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Provence       │    │   Clause        │
                       │   Reranker       │◀───│   Matching      │
                       │                  │    │                 │
                       └──────────────────┘    └─────────────────┘
```

## 🚀 Features

- **Multi-format Document Processing**: Supports PDF, DOCX, and email documents
- **Advanced OCR**: Uses Mistral AI for intelligent text extraction
- **Semantic Search**: OpenAI text-embedding-3-large for high-quality embeddings
- **Vector Storage**: FAISS for efficient similarity search
- **Smart Reranking**: Cross-encoder models for improved relevance
- **Domain Expertise**: Specialized for insurance, legal, HR, and compliance
- **Professional API**: FastAPI with authentication and comprehensive error handling
- **Explainable Responses**: Clear reasoning and clause traceability

## 📋 Requirements

- Python 3.11+
- OpenAI API key
- Mistral AI API key (optional, for advanced OCR)
- 4GB+ RAM (for embedding models)
- Docker (optional, for containerized deployment)

## 🛠️ Installation

### Local Development

1. **Clone the repository**
```bash
git clone <repository-url>
cd llm-query-retrieval-system
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your API keys
```

5. **Install system dependencies** (for OCR)
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr poppler-utils

# macOS
brew install tesseract poppler

# Windows
# Download and install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki
```

### Docker Deployment

1. **Build and run with Docker Compose**
```bash
# Set environment variables
export OPENAI_API_KEY="your-openai-key"
export MISTRAL_API_KEY="your-mistral-key"  # Optional

# Start the service
docker-compose up -d
```

## 🔧 Configuration

Key configuration options in `.env`:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here
BEARER_TOKEN=0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378

# Optional
MISTRAL_API_KEY=your_mistral_api_key_here
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
RERANK_TOP_K=20
FINAL_TOP_K=5
```

## 🚀 Usage

### Starting the Server

```bash
# Local development
cd src
python main.py

# Or with uvicorn
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
```

### API Endpoints

#### Main Query Endpoint
```http
POST /api/v1/hackrx/run
Authorization: Bearer 0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378
Content-Type: application/json

{
    "documents": "https://example.com/document.pdf",
    "questions": [
        "What is the grace period for premium payment?",
        "What are the waiting periods for pre-existing diseases?"
    ]
}
```

#### Health Check
```http
GET /api/v1/health
```

### Example Usage

```python
import httpx
import asyncio

async def query_document():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/hackrx/run",
            json={
                "documents": "https://example.com/policy.pdf",
                "questions": [
                    "What is the coverage limit?",
                    "What are the exclusions?"
                ]
            },
            headers={
                "Authorization": "Bearer 0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378"
            }
        )
        return response.json()

# Run the query
result = asyncio.run(query_document())
print(result["answers"])
```

## 🧪 Testing

### Run the test suite
```bash
# Start the server first
python src/main.py

# In another terminal, run tests
python test_api.py
```

### Manual testing with curl
```bash
curl -X POST "http://localhost:8000/api/v1/hackrx/run" \
  -H "Authorization: Bearer 0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378" \
  -H "Content-Type: application/json" \
  -d '{
    "documents": "https://example.com/document.pdf",
    "questions": ["What is covered under this policy?"]
  }'
```

## 📊 Performance Optimization

### Token Efficiency
- Optimized chunking strategy (1000 chars with 200 overlap)
- Batch processing for embeddings
- Efficient context selection (top 5 reranked results)

### Latency Optimization
- Concurrent processing of multiple questions
- FAISS for fast similarity search
- Async/await throughout the pipeline

### Cost Management
- Configurable chunk sizes and retrieval limits
- Token usage logging and monitoring
- Efficient prompt engineering

## 🔍 Domain Expertise

The system is optimized for:

### Insurance
- Policy coverage analysis
- Waiting periods and exclusions
- Claim procedures and conditions
- Premium and benefit details

### Legal
- Contract clause interpretation
- Terms and conditions analysis
- Compliance requirements
- Legal obligations

### HR
- Employee benefit policies
- Procedure documentation
- Eligibility criteria
- Policy compliance

### Compliance
- Regulatory requirements
- Audit criteria
- Reporting obligations
- Standards compliance

## 📈 Monitoring and Logging

- Comprehensive request/response logging
- Performance metrics (processing time, token usage)
- Health check endpoints
- Error tracking and reporting

## 🔒 Security

- Bearer token authentication
- Input validation and sanitization
- Rate limiting (configurable)
- Secure error handling (no sensitive data exposure)

## 🐛 Troubleshooting

### Common Issues

1. **OCR Failures**
   - Ensure Tesseract is installed
   - Check document accessibility
   - Verify Mistral API key (if using)

2. **Embedding Errors**
   - Verify OpenAI API key
   - Check rate limits
   - Ensure sufficient credits

3. **Memory Issues**
   - Reduce chunk size
   - Lower batch sizes
   - Increase system RAM

4. **Slow Performance**
   - Check network connectivity
   - Optimize chunk sizes
   - Use GPU acceleration (if available)

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python src/main.py
```

## 📝 API Documentation

Once the server is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for GPT-4 and embedding models
- Mistral AI for OCR capabilities
- FAISS team for efficient vector search
- FastAPI for the excellent web framework