"""
Optimized Section-Aware Chunking for PDF Documents
Performance improvements while maintaining hierarchical structure preservation
"""
import os
import re
import json
import numpy as np
import faiss
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Tuple, Optional
from unstructured.partition.pdf import partition_pdf
from unstructured.documents.elements import Title, NarrativeText, ListItem, Table
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from collections import defaultdict
import tiktoken
from dataclasses import dataclass
import threading

# Download required NLTK data once
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    print("📥 Downloading NLTK punkt tokenizer...")
    nltk.download('punkt', quiet=True)

@dataclass
class TimingResult:
    operation: str
    duration: float
    details: Dict[str, Any] = None

class PerformanceTimer:
    def __init__(self):
        self.timings = []
        self.start_time = None
    
    def start(self, operation: str):
        self.start_time = time.time()
        self.current_operation = operation
        print(f"⏱️ Starting: {operation}")
    
    def end(self, details: Dict[str, Any] = None):
        if self.start_time is None:
            return
        
        duration = time.time() - self.start_time
        result = TimingResult(self.current_operation, duration, details)
        self.timings.append(result)
        
        print(f"✅ Completed: {self.current_operation} in {duration:.2f}s")
        if details:
            for key, value in details.items():
                print(f"   📊 {key}: {value}")
        
        self.start_time = None
        return result
    
    def get_summary(self):
        total_time = sum(t.duration for t in self.timings)
        return {
            'total_time': total_time,
            'operations': [{
                'operation': t.operation,
                'duration': t.duration,
                'percentage': (t.duration / total_time * 100) if total_time > 0 else 0,
                'details': t.details
            } for t in self.timings]
        }

class OptimizedSectionAwareChunker:
    """
    Optimized section-aware chunker with performance improvements
    """
    
    def __init__(self, pdf_path: str, max_tokens: int = 300, section_max_tokens: int = 500, max_workers: int = 4):
        self.pdf_path = pdf_path
        self.max_tokens = max_tokens
        self.section_max_tokens = section_max_tokens
        self.max_workers = max_workers
        self.elements = None
        self.document_structure = None
        self.chunks = None
        self.embeddings = None
        self.faiss_index = None
        self.timer = PerformanceTimer()
        
        # Initialize tokenizer for accurate token counting (cached)
        try:
            self.tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo")
        except:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        # Cache for token counting
        self._token_cache = {}
        self._cache_lock = threading.Lock()
    
    def _count_tokens_cached(self, text: str) -> int:
        """Cached token counting for performance"""
        text_hash = hash(text)
        
        with self._cache_lock:
            if text_hash in self._token_cache:
                return self._token_cache[text_hash]
        
        try:
            token_count = len(self.tokenizer.encode(text))
        except:
            # Fallback to word-based estimation
            token_count = len(text.split()) * 1.3
        
        with self._cache_lock:
            self._token_cache[text_hash] = token_count
        
        return int(token_count)
    
    def extract_with_layout_awareness(self) -> List[Any]:
        """Optimized PDF extraction with minimal processing"""
        self.timer.start("PDF Layout Extraction")
        
        # Optimized partition parameters for hi_res processing
        self.elements = partition_pdf(
            filename=self.pdf_path,
            strategy="fast",  # Using hi_res for better extraction
            infer_table_structure=False,  # Disabled for speed
            extract_images_in_pdf=False,
            include_page_breaks=True,
            chunking_strategy=None,
            max_characters=10000  # Limit element size
        )
        
        self.timer.end({
            'elements_extracted': len(self.elements),
            'strategy': 'fast'
        })
        
        return self.elements
    
    def build_document_hierarchy(self) -> Dict[str, Any]:
        """Optimized hierarchy building with batch processing"""
        self.timer.start("Document Hierarchy Building")
        
        if not self.elements:
            self.extract_with_layout_awareness()
        
        # Pre-process page numbers in batch
        page_numbers = []
        element_texts = []
        element_types = []
        
        for elem in self.elements:
            text = str(elem).strip()
            element_texts.append(text)
            element_types.append(getattr(elem, 'category', 'unknown'))
            
            if hasattr(elem, 'metadata') and hasattr(elem.metadata, 'page_number'):
                page_num = elem.metadata.page_number
                if page_num is not None:
                    page_numbers.append(page_num)
        
        self.document_structure = {
            "document_id": os.path.basename(self.pdf_path).replace('.pdf', ''),
            "sections": [],
            "metadata": {
                "total_elements": len(self.elements),
                "total_pages": max(page_numbers) if page_numbers else 1
            }
        }
        
        # Vectorized header detection
        sections = self._build_sections_batch(element_texts, element_types)
        self.document_structure["sections"] = sections
        
        self.timer.end({
            'total_sections': len(sections),
            'total_pages': self.document_structure["metadata"]["total_pages"]
        })
        
        return self.document_structure
    
    def _build_sections_batch(self, element_texts: List[str], element_types: List[str]) -> List[Dict]:
        """Batch process sections for better performance"""
        sections = []
        current_section = None
        section_counter = 1
        
        # Compiled regex patterns for better performance
        number_pattern = re.compile(r'^(\d+\.?\d*)\s+(.+)', re.IGNORECASE)
        
        for i, (text, elem_type) in enumerate(zip(element_texts, element_types)):
            if not text:
                continue
            
            # Fast header detection
            is_header = (
                elem_type in ['Title', 'Header'] or
                len(text) < 100 or
                text.isupper() or
                number_pattern.match(text)
            )
            
            if is_header:
                section = {
                    "section_id": section_counter,
                    "section_number": self._extract_section_number(text),
                    "heading_text": text,
                    "level": self._determine_level(text),
                    "content": [],
                    "subsections": [],
                    "page_start": None,
                    "page_end": None
                }
                sections.append(section)
                current_section = section
                section_counter += 1
            elif current_section:
                current_section["content"].append(text)
        
        return sections
    
    def _extract_section_number(self, text: str) -> Optional[str]:
        """Fast section number extraction"""
        match = re.match(r'^(\d+\.?\d*)', text.strip())
        return match.group(1) if match else None
    
    def _determine_level(self, text: str) -> int:
        """Fast level determination"""
        if re.match(r'^\d+\.\d+\.\d+', text):
            return 3
        elif re.match(r'^\d+\.\d+', text):
            return 2
        elif re.match(r'^\d+', text):
            return 1
        else:
            return 1
    
    def create_section_aware_chunks(self) -> List[Dict[str, Any]]:
        """Optimized chunk creation with parallel processing"""
        self.timer.start("Section-Aware Chunk Creation")
        
        if not self.document_structure:
            self.build_document_hierarchy()
        
        chunks = []
        chunk_id = 1
        
        # Process sections in parallel for large documents
        if len(self.document_structure["sections"]) > 20:
            chunks = self._create_chunks_parallel()
        else:
            chunks = self._create_chunks_sequential()
        
        self.chunks = chunks
        
        self.timer.end({
            'total_chunks': len(chunks),
            'avg_tokens_per_chunk': sum(c['metadata']['token_count'] for c in chunks) / len(chunks) if chunks else 0
        })
        
        return chunks
    
    def _create_chunks_sequential(self) -> List[Dict[str, Any]]:
        """Sequential chunk creation for smaller documents"""
        chunks = []
        chunk_id = 1
        
        for section in self.document_structure["sections"]:
            section_chunks = self._process_single_section(section, chunk_id)
            chunks.extend(section_chunks)
            chunk_id += len(section_chunks)
        
        return chunks
    
    def _create_chunks_parallel(self) -> List[Dict[str, Any]]:
        """Parallel chunk creation for larger documents"""
        chunks = []
        chunk_id = 1
        
        # Split sections into batches for parallel processing
        batch_size = max(1, len(self.document_structure["sections"]) // self.max_workers)
        section_batches = [
            self.document_structure["sections"][i:i + batch_size]
            for i in range(0, len(self.document_structure["sections"]), batch_size)
        ]
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {
                executor.submit(self._process_section_batch, batch, chunk_id + i * 1000): batch
                for i, batch in enumerate(section_batches)
            }
            
            for future in as_completed(future_to_batch):
                batch_chunks = future.result()
                chunks.extend(batch_chunks)
        
        # Re-assign sequential chunk IDs
        for i, chunk in enumerate(chunks, 1):
            chunk['chunk_id'] = i
        
        return chunks
    
    def _process_section_batch(self, sections: List[Dict], base_chunk_id: int) -> List[Dict[str, Any]]:
        """Process a batch of sections"""
        chunks = []
        chunk_id = base_chunk_id
        
        for section in sections:
            section_chunks = self._process_single_section(section, chunk_id)
            chunks.extend(section_chunks)
            chunk_id += len(section_chunks)
        
        return chunks
    
    def _process_single_section(self, section: Dict, chunk_id: int) -> List[Dict[str, Any]]:
        """Process a single section into chunks"""
        chunks = []
        
        # Combine heading and content
        full_text = section["heading_text"]
        if section["content"]:
            full_text += "\n\n" + "\n".join(section["content"])
        
        # Fast token counting
        total_tokens = self._count_tokens_cached(full_text)
        
        if total_tokens <= self.max_tokens:
            # Single chunk
            chunk = self._create_chunk(
                chunk_id, full_text, section, 
                section.get("section_number", str(section["section_id"])),
                section["heading_text"]
            )
            chunks.append(chunk)
        else:
            # Split into multiple chunks
            text_chunks = self._smart_split_text(full_text, section["heading_text"])
            for i, text_chunk in enumerate(text_chunks):
                chunk = self._create_chunk(
                    chunk_id + i, text_chunk, section,
                    section.get("section_number", str(section["section_id"])),
                    section["heading_text"]
                )
                chunks.append(chunk)
        
        return chunks
    
    def _smart_split_text(self, text: str, heading: str) -> List[str]:
        """Smart text splitting with sentence preservation"""
        sentences = sent_tokenize(text)
        chunks = []
        current_chunk = heading + "\n\n"
        current_tokens = self._count_tokens_cached(current_chunk)
        
        for sentence in sentences:
            sentence_tokens = self._count_tokens_cached(sentence)
            
            if current_tokens + sentence_tokens <= self.max_tokens:
                current_chunk += sentence + " "
                current_tokens += sentence_tokens
            else:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = heading + "\n\n" + sentence + " "
                current_tokens = self._count_tokens_cached(current_chunk)
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _create_chunk(self, chunk_id: int, text: str, section: Dict, section_path: str, heading: str) -> Dict[str, Any]:
        """Create a single chunk with metadata"""
        token_count = self._count_tokens_cached(text)
        word_count = len(text.split())
        
        return {
            "chunk_id": chunk_id,
            "original_text": text,
            "processed_text": text,  # Can add preprocessing here if needed
            "metadata": {
                "document_id": self.document_structure["document_id"],
                "section_id": section["section_id"],
                "section_path": section_path,
                "section_level": section["level"],
                "heading_text": heading,
                "token_count": token_count,
                "character_count": len(text),
                "word_count": word_count,
                "has_section_context": True,
                "page_start": section.get("page_start"),
                "page_end": section.get("page_end")
            }
        }
    
    def generate_embeddings(self, embedding_function) -> np.ndarray:
        """Optimized embedding generation with batch processing"""
        self.timer.start("Embedding Generation")
        
        if not self.chunks:
            self.create_section_aware_chunks()
        
        # Extract texts for embedding
        texts = [chunk["processed_text"] for chunk in self.chunks]
        
        # Process in optimal batches
        batch_size = 50  # Reduced batch size for better memory management
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            print(f"🔄 Processing embeddings batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
            
            batch_embeddings = embedding_function(batch_texts)
            embeddings.extend(batch_embeddings)
        
        self.embeddings = np.array(embeddings)
        
        self.timer.end({
            'total_embeddings': len(embeddings),
            'embedding_dimension': len(embeddings[0]) if embeddings else 0,
            'batch_size': batch_size
        })
        
        return self.embeddings
    
    def build_faiss_index(self, index_path: str) -> faiss.Index:
        """Optimized FAISS index building"""
        self.timer.start("FAISS Index Building")
        
        if self.embeddings is None:
            raise ValueError("Embeddings must be generated before building FAISS index")
        
        # Ensure embeddings are in correct format (float32)
        if not isinstance(self.embeddings, np.ndarray):
            self.embeddings = np.array(self.embeddings, dtype=np.float32)
        else:
            self.embeddings = self.embeddings.astype(np.float32)
        
        # Ensure embeddings are contiguous in memory
        if not self.embeddings.flags['C_CONTIGUOUS']:
            self.embeddings = np.ascontiguousarray(self.embeddings)
        
        # Use more efficient index type for smaller datasets
        if len(self.embeddings) < 1000:
            # Use flat index for small datasets (faster search)
            index = faiss.IndexFlatIP(self.embeddings.shape[1])
        else:
            # Use IVF index for larger datasets
            nlist = min(100, len(self.embeddings) // 10)
            quantizer = faiss.IndexFlatIP(self.embeddings.shape[1])
            index = faiss.IndexIVFFlat(quantizer, self.embeddings.shape[1], nlist)
            index.train(self.embeddings)
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(self.embeddings)
        index.add(self.embeddings)
        
        # Save index
        faiss.write_index(index, index_path)
        self.faiss_index = index
        
        self.timer.end({
            'index_type': type(index).__name__,
            'total_vectors': index.ntotal,
            'index_size_mb': os.path.getsize(index_path) / (1024 * 1024) if os.path.exists(index_path) else 0
        })
        
        return index
    
    def save_chunks_metadata(self, metadata_path: str):
        """Save chunk metadata efficiently"""
        self.timer.start("Metadata Saving")
        
        if not self.chunks:
            raise ValueError("Chunks must be created before saving metadata")
        
        metadata = {
            "document_info": {
                "document_id": self.document_structure["document_id"],
                "total_pages": self.document_structure["metadata"]["total_pages"],
                "total_elements": self.document_structure["metadata"]["total_elements"]
            },
            "processing_config": {
                "max_tokens": self.max_tokens,
                "section_max_tokens": self.section_max_tokens,
                "max_workers": self.max_workers
            },
            "chunks": self.chunks,
            "performance_summary": self.timer.get_summary()
        }
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        self.timer.end({
            'metadata_file_size_mb': os.path.getsize(metadata_path) / (1024 * 1024),
            'total_chunks_saved': len(self.chunks)
        })
    
    def get_chunk_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics with performance metrics"""
        if not self.chunks:
            return {}
        
        stats = {
            'total_chunks': len(self.chunks),
            'avg_token_count': np.mean([c['metadata']['token_count'] for c in self.chunks]),
            'avg_chunk_length': np.mean([c['metadata']['character_count'] for c in self.chunks]),
            'avg_word_count': np.mean([c['metadata']['word_count'] for c in self.chunks]),
            'total_characters': sum(c['metadata']['character_count'] for c in self.chunks),
            'total_words': sum(c['metadata']['word_count'] for c in self.chunks),
            'unique_sections': len(set(c['metadata']['section_id'] for c in self.chunks)),
            'chunks_with_context': sum(1 for c in self.chunks if c['metadata']['has_section_context']),
            'level_distribution': {},
            'section_distribution': {},
            'performance_metrics': self.timer.get_summary()
        }
        
        # Level distribution
        for chunk in self.chunks:
            level = chunk['metadata']['section_level']
            stats['level_distribution'][level] = stats['level_distribution'].get(level, 0) + 1
        
        # Section distribution
        for chunk in self.chunks:
            section_path = chunk['metadata']['section_path']
            stats['section_distribution'][section_path] = stats['section_distribution'].get(section_path, 0) + 1
        
        # Document structure stats
        if self.document_structure:
            stats['document_structure_stats'] = {
                'total_sections': len(self.document_structure['sections']),
                'total_pages': self.document_structure['metadata']['total_pages']
            }
        
        return stats
    
    def search_similar_chunks(self, query_embedding: np.ndarray, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar chunks using FAISS index"""
        if self.faiss_index is None:
            raise ValueError("FAISS index must be built before searching")
        
        # Ensure query embedding is correct format
        if isinstance(query_embedding, list):
            query_embedding = np.array([query_embedding], dtype=np.float32)
        else:
            query_embedding = np.array([query_embedding], dtype=np.float32)
        
        # Ensure contiguous memory layout
        if not query_embedding.flags['C_CONTIGUOUS']:
            query_embedding = np.ascontiguousarray(query_embedding)
        
        # Normalize query embedding
        faiss.normalize_L2(query_embedding)
        
        # Search
        scores, indices = self.faiss_index.search(query_embedding, top_k)
        
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx >= 0:  # Valid index
                chunk = self.chunks[idx]
                result = {
                    'rank': i + 1,
                    'similarity_score': float(score),
                    'chunk': chunk,
                    'section_info': {
                        'path': chunk['metadata']['section_path'],
                        'heading': chunk['metadata']['heading_text'],
                        'level': chunk['metadata']['section_level']
                    }
                }
                results.append(result)
        
        return results
