"""
Section-aware document chunking with Mistral OCR optimization and performance monitoring.
"""

import time
import hashlib
import logging
import pickle
import os
import re
import base64
import requests
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# Add the chunking directory to Python path for imports
import sys
sys.path.append('/home/<USER>/Hackathon/Bajaj-Hackathon')

# Import with error handling for environment loading
try:
    from src.config import settings
except Exception as e:
    # Fallback: load environment variables directly
    import os
    from dotenv import load_dotenv
    load_dotenv('/home/<USER>/Hackathon/Bajaj-Hackathon/.env')
    
    class MockSettings:
        AZUREAI_ENDPOINT = os.getenv('AZUREAI_ENDPOINT')
        AZUREAI_API_KEY = os.getenv('AZUREAI_API_KEY')
    
    settings = MockSettings()

from chunking.optimized_embeddings import optimized_embedding

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceTimer:
    """Context manager for timing operations with detailed breakdown."""
    
    def __init__(self, operation_name: str, parent_timer=None):
        self.operation_name = operation_name
        self.parent_timer = parent_timer
        self.start_time = None
        self.end_time = None
        self.duration = 0
        self.sub_operations = {}
        
    def __enter__(self):
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        
    def add_sub_operation(self, name: str, duration: float):
        self.sub_operations[name] = duration
        
    def print_breakdown(self, indent=0):
        prefix = "  " * indent
        print(f"{prefix}{self.operation_name}: {self.duration:.2f}s")
        for name, duration in self.sub_operations.items():
            print(f"{prefix}  └─ {name}: {duration:.2f}s")


class MistralOptimizedSectionAwareChunker:
    """
    Optimized section-aware document chunker using Mistral OCR for text extraction.
    """
    
    def __init__(self, azureai_endpoint: str = None, azureai_api_key: str = None):
        self.azureai_endpoint = azureai_endpoint or settings.AZUREAI_ENDPOINT
        self.azureai_api_key = azureai_api_key or settings.AZUREAI_API_KEY
        
        # Check if Mistral OCR credentials are available
        if not self.azureai_endpoint or not self.azureai_api_key:
            raise ValueError("Mistral OCR requires AZUREAI_ENDPOINT and AZUREAI_API_KEY to be set")
        
        # Cache for token counts to avoid recalculation
        self._token_count_cache = {}
        
        # Section patterns for identification
        self.section_patterns = [
            r'^(\d+\.?\s+.*)$',  # Numbered sections (1. Section Name)
            r'^([A-Z][A-Z\s&-]+)$',  # All caps sections
            r'^([IVX]+\.?\s+.*)$',  # Roman numerals
            r'^([a-z]\)?\s+.*)$',  # Lettered subsections
            r'^\*\*(.*)\*\*$',  # Bold sections (markdown)
            r'^#{1,6}\s+(.*)$',  # Markdown headers
        ]
        
        # Compile regex patterns for performance
        self.compiled_patterns = [re.compile(pattern, re.MULTILINE) for pattern in self.section_patterns]
        
        print(f"✅ Mistral OCR Chunker initialized with endpoint: {self.azureai_endpoint[:50]}...")
    
    def _mistral_ocr_extract(self, document_path: Path) -> str:
        """
        Extract text from PDF using Mistral OCR.
        
        Args:
            document_path: Path to the PDF document
            
        Returns:
            Extracted text content
        """
        try:
            logger.info(f"Processing document with Mistral OCR: {document_path}")
            
            # Determine MIME type
            mime_ext = document_path.suffix.lower()
            mime_type = {
                ".pdf": "application/pdf",
                ".png": "image/png", 
                ".jpg": "image/jpeg",
                ".jpeg": "image/jpeg"
            }.get(mime_ext, "application/octet-stream")
            
            # Read and encode file
            with open(document_path, "rb") as f:
                raw_bytes = f.read()
            
            b64_content = base64.b64encode(raw_bytes).decode("utf-8")
            data_uri = f"data:{mime_type};base64,{b64_content}"
            
            # Build payload
            payload = {
                "model": "mistral-ocr-2503",
                "document": {
                    "type": "document_url",
                    "document_url": data_uri
                },
                "include_image_base64": True
            }
            
            # Call OCR endpoint
            url = f"{self.azureai_endpoint}/v1/ocr"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.azureai_api_key}"
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=120)
            response.raise_for_status()
            ocr_result = response.json()
            
            # Extract markdown content from all pages
            all_markdown = []
            for page in ocr_result.get("pages", []):
                md_content = page.get("markdown", "")
                if md_content.strip():
                    all_markdown.append(md_content.strip())
            
            # Combine all pages
            combined_text = "\n\n".join(all_markdown)
            
            logger.info(f"Mistral OCR successful. Extracted {len(combined_text)} characters")
            return combined_text
            
        except Exception as e:
            logger.error(f"Mistral OCR failed: {e}")
            raise
    
    def _cached_token_count(self, text: str) -> int:
        """Get token count with caching for performance."""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        if text_hash not in self._token_count_cache:
            # Simple approximation: 1 token ≈ 4 characters
            self._token_count_cache[text_hash] = len(text) // 4
            
        return self._token_count_cache[text_hash]
    
    def identify_sections(self, text: str, timer: PerformanceTimer = None) -> List[Dict[str, Any]]:
        """
        Identify document sections with optimized pattern matching.
        
        Args:
            text: Document text to analyze
            timer: Performance timer for tracking
            
        Returns:
            List of section dictionaries
        """
        with PerformanceTimer("Section Identification") as section_timer:
            if timer:
                timer.add_sub_operation("section_identification", section_timer.duration)
                
            lines = text.split('\n')
            sections = []
            current_section = {
                'title': 'Introduction',
                'content': '',
                'start_line': 0,
                'level': 0
            }
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    current_section['content'] += '\n'
                    continue
                
                # Check if line matches any section pattern
                is_section_header = False
                section_level = 0
                
                # Use compiled patterns for better performance
                for j, pattern in enumerate(self.compiled_patterns):
                    if pattern.match(line):
                        is_section_header = True
                        section_level = j + 1
                        break
                
                if is_section_header and len(current_section['content'].strip()) > 50:
                    # Save current section
                    sections.append(current_section.copy())
                    
                    # Start new section
                    current_section = {
                        'title': line,
                        'content': '',
                        'start_line': i,
                        'level': section_level
                    }
                else:
                    current_section['content'] += line + '\n'
            
            # Add the last section
            if current_section['content'].strip():
                sections.append(current_section)
            
            print(f"✅ Identified {len(sections)} sections")
            return sections
    
    def create_adaptive_chunks(self, sections: List[Dict[str, Any]], target_chunk_size: int = 1000) -> List[Dict[str, Any]]:
        """
        Create chunks with adaptive sizing based on content and token count.
        
        Args:
            sections: List of document sections
            target_chunk_size: Target size in characters
            
        Returns:
            List of chunk dictionaries
        """
        with PerformanceTimer("Adaptive Chunking") as chunk_timer:
            chunks = []
            
            for section in sections:
                content = section['content'].strip()
                if not content:
                    continue
                
                content_length = len(content)
                estimated_tokens = self._cached_token_count(content)
                
                # Adaptive chunk sizing based on content type
                if section['level'] <= 2:  # Main sections
                    max_chunk_size = min(target_chunk_size * 1.5, 1500)
                else:  # Subsections
                    max_chunk_size = target_chunk_size
                
                if content_length <= max_chunk_size:
                    # Section fits in one chunk
                    chunks.append({
                        'content': content,
                        'metadata': {
                            'section_title': section['title'],
                            'section_level': section['level'],
                            'chunk_index': 0,
                            'estimated_tokens': estimated_tokens,
                            'section_start_line': section['start_line']
                        }
                    })
                else:
                    # Split section into multiple chunks with overlap
                    overlap_size = max_chunk_size // 10  # 10% overlap
                    start = 0
                    chunk_index = 0
                    
                    while start < content_length:
                        end = min(start + max_chunk_size, content_length)
                        
                        # Adjust end to sentence boundary if possible
                        if end < content_length:
                            last_sentence = content.rfind('.', start, end)
                            if last_sentence > start + max_chunk_size // 2:
                                end = last_sentence + 1
                        
                        chunk_content = content[start:end].strip()
                        if chunk_content:
                            chunks.append({
                                'content': chunk_content,
                                'metadata': {
                                    'section_title': section['title'],
                                    'section_level': section['level'],
                                    'chunk_index': chunk_index,
                                    'estimated_tokens': self._cached_token_count(chunk_content),
                                    'section_start_line': section['start_line']
                                }
                            })
                            
                        chunk_index += 1
                        start = end - overlap_size
                        
                        if start >= content_length:
                            break
            
            print(f"✅ Created {len(chunks)} adaptive chunks")
            return chunks
    
    def process_document_optimized(self, pdf_path: str):
        """Process document using unstructured for comparison"""
        timing_stats = {}
        
        with PerformanceTimer("unstructured_extraction") as timer:
            from unstructured.partition.pdf import partition_pdf
            
            elements = partition_pdf(
                filename=pdf_path,
                strategy="fast",
                infer_table_structure=False,
                extract_images_in_pdf=False,
                include_page_breaks=True,
                chunking_strategy=None,
                max_characters=10000
            )
            
            timing_stats['unstructured_extraction'] = timer.duration
        
        with PerformanceTimer("section_identification") as timer:
            sections = self._build_sections_from_elements(elements)
            timing_stats['section_identification'] = timer.duration
        
        with PerformanceTimer("chunk_creation") as timer:
            chunks = self._create_chunks_from_sections(sections)
            timing_stats['chunk_creation'] = timer.duration
        
        timing_stats['total_processing'] = sum(timing_stats.values())
        
        return chunks, timing_stats

    def _build_sections_from_elements(self, elements):
        """Build sections from unstructured elements"""
        sections = []
        current_section = None
        section_counter = 1
        
        for element in elements:
            text = str(element).strip()
            if not text:
                continue
            
            element_type = getattr(element, 'category', 'unknown')
            
            # Simple header detection
            is_header = (
                element_type in ['Title', 'Header'] or
                len(text) < 100 or
                text.isupper() or
                re.match(r'^\d+\.?\d*\s+', text)
            )
            
            if is_header:
                section = {
                    'id': section_counter,
                    'heading': text,
                    'content': [],
                    'level': self._determine_level(text)
                }
                sections.append(section)
                current_section = section
                section_counter += 1
            elif current_section:
                current_section['content'].append(text)
        
        return sections
    
    def _build_sections_from_mistral_content(self, content):
        """Build sections from Mistral OCR content"""
        sections = []
        lines = content.split('\n')
        current_section = None
        section_counter = 1
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Enhanced header detection for OCR content
            is_header = (
                len(line) < 100 or
                line.isupper() or
                re.match(r'^\d+\.?\d*\s+', line) or
                re.match(r'^[A-Z][A-Z\s]+$', line) or
                line.endswith(':') and len(line) < 50
            )
            
            if is_header:
                section = {
                    'id': section_counter,
                    'heading': line,
                    'content': [],
                    'level': self._determine_level(line)
                }
                sections.append(section)
                current_section = section
                section_counter += 1
            elif current_section:
                current_section['content'].append(line)
            else:
                # Create a default section if no header found yet
                section = {
                    'id': section_counter,
                    'heading': 'Document Content',
                    'content': [line],
                    'level': 1
                }
                sections.append(section)
                current_section = section
                section_counter += 1
        
        return sections
    
    def _determine_level(self, text):
        """Determine section level based on text patterns"""
        if re.match(r'^\d+\.\d+\.\d+', text):
            return 3
        elif re.match(r'^\d+\.\d+', text):
            return 2
        elif re.match(r'^\d+', text):
            return 1
        else:
            return 1
    
    def _create_chunks_from_sections(self, sections):
        """Create chunks from sections with optimal sizing"""
        chunks = []
        chunk_id = 1
        
        for section in sections:
            # Combine heading and content
            full_text = section['heading']
            if section['content']:
                full_text += '\n\n' + '\n'.join(section['content'])
            
            # Check if text fits in one chunk
            estimated_tokens = len(full_text.split()) * 1.3  # Rough token estimation
            
            if estimated_tokens <= 300:  # Max tokens per chunk
                chunk = {
                    'id': chunk_id,
                    'content': full_text,
                    'section_id': section['id'],
                    'section_heading': section['heading'],
                    'section_level': section['level'],
                    'estimated_tokens': int(estimated_tokens)
                }
                chunks.append(chunk)
                chunk_id += 1
            else:
                # Split large sections into smaller chunks
                sentences = re.split(r'[.!?]\s+', full_text)
                current_chunk = section['heading'] + '\n\n'
                current_tokens = len(current_chunk.split()) * 1.3
                
                for sentence in sentences:
                    sentence_tokens = len(sentence.split()) * 1.3
                    
                    if current_tokens + sentence_tokens <= 300:
                        current_chunk += sentence + '. '
                        current_tokens += sentence_tokens
                    else:
                        if current_chunk.strip():
                            chunk = {
                                'id': chunk_id,
                                'content': current_chunk.strip(),
                                'section_id': section['id'],
                                'section_heading': section['heading'],
                                'section_level': section['level'],
                                'estimated_tokens': int(current_tokens)
                            }
                            chunks.append(chunk)
                            chunk_id += 1
                        
                        current_chunk = section['heading'] + '\n\n' + sentence + '. '
                        current_tokens = len(current_chunk.split()) * 1.3
                
                # Add final chunk if any content remains
                if current_chunk.strip():
                    chunk = {
                        'id': chunk_id,
                        'content': current_chunk.strip(),
                        'section_id': section['id'],
                        'section_heading': section['heading'],
                        'section_level': section['level'],
                        'estimated_tokens': int(current_tokens)
                    }
                    chunks.append(chunk)
                    chunk_id += 1
        
        return chunks
            
            # Section identification
            with PerformanceTimer("Section Processing") as section_timer:
                sections = self.identify_sections(text, section_timer)
                timing_stats['section_identification'] = section_timer.duration
            
            # Chunk creation
            with PerformanceTimer("Chunk Creation") as chunk_timer:
                chunks = self.create_adaptive_chunks(sections, target_chunk_size)
                timing_stats['chunk_creation'] = chunk_timer.duration
            
            # Generate embeddings
            with PerformanceTimer("Embedding Generation") as embedding_timer:
                chunk_texts = [chunk['content'] for chunk in chunks]
                embeddings = optimized_embedding(chunk_texts)
                
                # Add embeddings to chunks
                for i, chunk in enumerate(chunks):
                    chunk['embedding'] = embeddings[i]
                
                timing_stats['embedding_generation'] = embedding_timer.duration
            
            timing_stats['total_processing'] = total_timer.duration
            
            print(f"✅ Processing complete: {len(chunks)} chunks with embeddings")
            
        # Print detailed timing breakdown
        total_timer.add_sub_operation("mistral_ocr_extraction", timing_stats['mistral_ocr_extraction'])
        total_timer.add_sub_operation("section_identification", timing_stats['section_identification'])
        total_timer.add_sub_operation("chunk_creation", timing_stats['chunk_creation'])
        total_timer.add_sub_operation("embedding_generation", timing_stats['embedding_generation'])
        total_timer.print_breakdown()
        
        return chunks, timing_stats


def analyze_chunks(chunks: List[Dict[str, Any]]) -> None:
    """Analyze and print chunk statistics."""
    if not chunks:
        print("❌ No chunks to analyze")
        return
    
    total_chunks = len(chunks)
    total_chars = sum(len(chunk['content']) for chunk in chunks)
    avg_chunk_size = total_chars / total_chunks if total_chunks > 0 else 0
    
    # Token statistics
    total_tokens = sum(chunk['metadata']['estimated_tokens'] for chunk in chunks)
    avg_tokens = total_tokens / total_chunks if total_chunks > 0 else 0
    
    # Section level distribution
    section_levels = {}
    for chunk in chunks:
        level = chunk['metadata']['section_level']
        section_levels[level] = section_levels.get(level, 0) + 1
    
    print(f"\n📊 Chunk Analysis:")
    print(f"  Total chunks: {total_chunks}")
    print(f"  Total characters: {total_chars:,}")
    print(f"  Average chunk size: {avg_chunk_size:.1f} characters")
    print(f"  Total estimated tokens: {total_tokens:,}")
    print(f"  Average tokens per chunk: {avg_tokens:.1f}")
    print(f"  Section level distribution: {section_levels}")
    
    # Size distribution
    sizes = [len(chunk['content']) for chunk in chunks]
    sizes.sort()
    print(f"  Chunk size range: {min(sizes)} - {max(sizes)} characters")
    print(f"  Median chunk size: {sizes[len(sizes)//2]} characters")


if __name__ == "__main__":
    # Example usage
    pdf_path = "/home/<USER>/Hackathon/Bajaj-Hackathon/chunking/policy.pdf"
    
    try:
        print("🚀 Starting Mistral OCR document processing...")
        
        # Initialize the chunker
        chunker = MistralOptimizedSectionAwareChunker()
        
        # Process the document
        chunks, timing_stats = chunker.process_document_optimized(pdf_path)
        
        # Analyze results
        analyze_chunks(chunks)
        
        print(f"\n⏱️  Performance Summary:")
        print(f"  Mistral OCR extraction: {timing_stats['mistral_ocr_extraction']:.2f}s")
        print(f"  Section identification: {timing_stats['section_identification']:.2f}s")
        print(f"  Chunk creation: {timing_stats['chunk_creation']:.2f}s")
        print(f"  Embedding generation: {timing_stats['embedding_generation']:.2f}s")
        print(f"  Total processing time: {timing_stats['total_processing']:.2f}s")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


# Add missing methods to MistralOptimizedSectionAwareChunker class
def _build_sections_from_elements(self, elements):
    """Build sections from unstructured elements"""
    sections = []
    current_section = None
    section_counter = 1
    
    for element in elements:
        text = str(element).strip()
        if not text:
            continue
        
        element_type = getattr(element, 'category', 'unknown')
        
        # Simple header detection
        is_header = (
            element_type in ['Title', 'Header'] or
            len(text) < 100 or
            text.isupper() or
            re.match(r'^\d+\.?\d*\s+', text)
        )
        
        if is_header:
            section = {
                'id': section_counter,
                'heading': text,
                'content': [],
                'level': self._determine_level(text)
            }
            sections.append(section)
            current_section = section
            section_counter += 1
        elif current_section:
            current_section['content'].append(text)
    
    return sections

def _build_sections_from_mistral_content(self, content):
    """Build sections from Mistral OCR content"""
    sections = []
    lines = content.split('\n')
    current_section = None
    section_counter = 1
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # Enhanced header detection for OCR content
        is_header = (
            len(line) < 100 or
            line.isupper() or
            re.match(r'^\d+\.?\d*\s+', line) or
            re.match(r'^[A-Z][A-Z\s]+$', line) or
            line.endswith(':') and len(line) < 50
        )
        
        if is_header:
            section = {
                'id': section_counter,
                'heading': line,
                'content': [],
                'level': self._determine_level(line)
            }
            sections.append(section)
            current_section = section
            section_counter += 1
        elif current_section:
            current_section['content'].append(line)
        else:
            # Create a default section if no header found yet
            section = {
                'id': section_counter,
                'heading': 'Document Content',
                'content': [line],
                'level': 1
            }
            sections.append(section)
            current_section = section
            section_counter += 1
    
    return sections

def _determine_level(self, text):
    """Determine section level based on text patterns"""
    if re.match(r'^\d+\.\d+\.\d+', text):
        return 3
    elif re.match(r'^\d+\.\d+', text):
        return 2
    elif re.match(r'^\d+', text):
        return 1
    else:
        return 1

def _create_chunks_from_sections(self, sections):
    """Create chunks from sections with optimal sizing"""
    chunks = []
    chunk_id = 1
    
    for section in sections:
        # Combine heading and content
        full_text = section['heading']
        if section['content']:
            full_text += '\n\n' + '\n'.join(section['content'])
        
        # Check if text fits in one chunk
        estimated_tokens = len(full_text.split()) * 1.3  # Rough token estimation
        
        if estimated_tokens <= 300:  # Max tokens per chunk
            chunk = {
                'id': chunk_id,
                'content': full_text,
                'section_id': section['id'],
                'section_heading': section['heading'],
                'section_level': section['level'],
                'estimated_tokens': int(estimated_tokens)
            }
            chunks.append(chunk)
            chunk_id += 1
        else:
            # Split large sections into smaller chunks
            sentences = re.split(r'[.!?]\s+', full_text)
            current_chunk = section['heading'] + '\n\n'
            current_tokens = len(current_chunk.split()) * 1.3
            
            for sentence in sentences:
                sentence_tokens = len(sentence.split()) * 1.3
                
                if current_tokens + sentence_tokens <= 300:
                    current_chunk += sentence + '. '
                    current_tokens += sentence_tokens
                else:
                    if current_chunk.strip():
                        chunk = {
                            'id': chunk_id,
                            'content': current_chunk.strip(),
                            'section_id': section['id'],
                            'section_heading': section['heading'],
                            'section_level': section['level'],
                            'estimated_tokens': int(current_tokens)
                        }
                        chunks.append(chunk)
                        chunk_id += 1
                    
                    current_chunk = section['heading'] + '\n\n' + sentence + '. '
                    current_tokens = len(current_chunk.split()) * 1.3
            
            # Add final chunk if any content remains
            if current_chunk.strip():
                chunk = {
                    'id': chunk_id,
                    'content': current_chunk.strip(),
                    'section_id': section['id'],
                    'section_heading': section['heading'],
                    'section_level': section['level'],
                    'estimated_tokens': int(current_tokens)
                }
                chunks.append(chunk)
                chunk_id += 1
    
    return chunks
