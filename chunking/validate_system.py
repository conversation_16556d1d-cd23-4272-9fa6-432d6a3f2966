#!/usr/bin/env python3
"""
System validation script for section-aware chunking
Verifies all components work correctly
"""

import os
import json
from section_aware_chunker import SectionAwareChunker
from search_interface import PolicySearchEngine
from embeddings import get_query_embedding

def validate_files():
    """Validate required files exist"""
    required_files = [
        "section_aware_faiss_index.idx",
        "section_aware_chunks_metadata.json", 
        "section_aware_statistics.json"
    ]
    
    print("🔍 Validating required files...")
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - Missing!")
            return False
    return True

def validate_metadata_structure():
    """Validate metadata structure compliance"""
    print("\n📋 Validating metadata structure...")
    
    try:
        with open("section_aware_chunks_metadata.json", 'r') as f:
            data = json.load(f)
        
        # Check for required top-level structure
        if 'chunks' not in data:
            print("   ❌ Missing 'chunks' key in metadata")
            return False
        
        chunks = data['chunks']
        if not chunks:
            print("   ❌ No chunks found")
            return False
        
        # Validate first chunk structure
        chunk = chunks[0]
        required_fields = ['id', 'text', 'metadata']
        required_metadata = ['document_id', 'section_path', 'heading_text']
        
        for field in required_fields:
            if field not in chunk:
                print(f"   ❌ Missing required field: {field}")
                return False
        
        for field in required_metadata:
            if field not in chunk['metadata']:
                print(f"   ❌ Missing required metadata: {field}")
                return False
        
        print(f"   ✅ Metadata structure valid ({len(chunks)} chunks)")
        return True
        
    except Exception as e:
        print(f"   ❌ Error validating metadata: {str(e)}")
        return False

def validate_search_functionality():
    """Validate search functionality"""
    print("\n🔍 Validating search functionality...")
    
    try:
        # Initialize search engine
        search_engine = PolicySearchEngine()
        
        # Test basic search
        query_embedding = get_query_embedding("test query")
        results = search_engine.search("claims processing", top_k=3)
        
        if results:
            print(f"   ✅ Search returns {len(results)} results")
            
            # Validate result structure
            result = results[0]
            required_fields = ['chunk_id', 'title', 'text', 'similarity_score']
            
            for field in required_fields:
                if field not in result:
                    print(f"   ❌ Missing result field: {field}")
                    return False
            
            print("   ✅ Search result structure valid")
            return True
        else:
            print("   ❌ Search returns no results")
            return False
            
    except Exception as e:
        print(f"   ❌ Error validating search: {str(e)}")
        return False

def validate_statistics():
    """Validate processing statistics"""
    print("\n📊 Validating statistics...")
    
    try:
        with open("section_aware_statistics.json", 'r') as f:
            stats = json.load(f)
        
        required_stats = [
            'total_chunks', 'avg_token_count', 'unique_sections',
            'chunks_with_context', 'processing_info'
        ]
        
        for stat in required_stats:
            if stat not in stats:
                print(f"   ❌ Missing statistic: {stat}")
                return False
        
        # Validate compliance
        total_chunks = stats['total_chunks']
        chunks_with_context = stats['chunks_with_context']
        avg_tokens = stats['avg_token_count']
        
        print(f"   📄 Total chunks: {total_chunks}")
        print(f"   🎯 Average tokens: {avg_tokens:.1f}")
        print(f"   📋 Chunks with context: {chunks_with_context}/{total_chunks}")
        
        if chunks_with_context == total_chunks:
            print("   ✅ 100% chunks have section context")
        else:
            print(f"   ⚠️ Only {chunks_with_context/total_chunks*100:.1f}% have context")
        
        if avg_tokens <= 300:
            print("   ✅ Average tokens within limit")
        else:
            print(f"   ⚠️ Average tokens exceed 300 limit")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error validating statistics: {str(e)}")
        return False

def main():
    """Main validation function"""
    print("🚀 Section-Aware Chunking System Validation")
    print("=" * 50)
    
    validations = [
        validate_files,
        validate_metadata_structure,
        validate_search_functionality,
        validate_statistics
    ]
    
    passed = 0
    total = len(validations)
    
    for validation in validations:
        if validation():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 VALIDATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validations passed! System is ready for use.")
        return True
    else:
        print("❌ Some validations failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)