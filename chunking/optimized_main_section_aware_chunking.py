#!/usr/bin/env python3
"""
Optimized Main script for section-aware chunking with performance monitoring
Implements faster processing while maintaining quality
"""

import os
import sys
import json
from datetime import datetime
from optimized_section_aware_chunker import OptimizedSectionAwareChunker, PerformanceTimer
from optimized_embeddings import optimized_embedding, get_query_embedding, get_cache_stats
import multiprocessing

def print_performance_header():
    """Print performance optimization header"""
    print("🚀 OPTIMIZED Section-Aware Chunking Pipeline")
    print("⚡ Performance-enhanced while maintaining quality")
    print("📊 Real-time timing and statistics enabled")
    print("=" * 70)

def display_optimization_settings():
    """Display current optimization settings"""
    cpu_count = multiprocessing.cpu_count()
    cache_stats = get_cache_stats()
    
    print(f"\n⚙️ OPTIMIZATION SETTINGS:")
    print(f"   🖥️ CPU cores available: {cpu_count}")
    print(f"   🧵 Max workers: {min(4, cpu_count)}")
    print(f"   📦 Embedding cache: {'Enabled' if cache_stats['cache_enabled'] else 'Disabled'}")
    if cache_stats['cache_files'] > 0:
        print(f"   💾 Cache files: {cache_stats['cache_files']} ({cache_stats['cache_size_mb']:.1f} MB)")
    print(f"   🔄 Strategy: Fast extraction + Batch processing")

def main():
    """Optimized main execution function"""
    print_performance_header()
    
    # Configuration with performance optimizations
    PDF_PATH = "policy.pdf"
    INDEX_PATH = "section_aware_faiss_index.idx"
    METADATA_PATH = "section_aware_chunks_metadata.json"
    STATS_PATH = "section_aware_statistics.json"
    
    # Optimized parameters
    MAX_TOKENS_PER_CHUNK = 300
    SECTION_MAX_TOKENS = 500
    MAX_WORKERS = min(4, multiprocessing.cpu_count())  # Optimal worker count
    
    # Verify PDF exists
    if not os.path.exists(PDF_PATH):
        print(f"❌ Error: {PDF_PATH} not found!")
        sys.exit(1)
    
    display_optimization_settings()
    
    # Initialize main timer
    main_timer = PerformanceTimer()
    main_timer.start("Complete Section-Aware Chunking Pipeline")
    
    try:
        # Initialize optimized chunker
        print(f"\n🔧 Initializing Optimized Section-Aware Chunker...")
        print(f"   📏 Max tokens per chunk: {MAX_TOKENS_PER_CHUNK}")
        print(f"   📄 Section max tokens: {SECTION_MAX_TOKENS}")
        print(f"   🧵 Max workers: {MAX_WORKERS}")
        
        chunker = OptimizedSectionAwareChunker(
            pdf_path=PDF_PATH,
            max_tokens=MAX_TOKENS_PER_CHUNK,
            section_max_tokens=SECTION_MAX_TOKENS,
            max_workers=MAX_WORKERS
        )
        
        # Step 1: Extract with optimized layout-aware parsing
        print(f"\n📄 Step 1: Optimized layout-aware document parsing...")
        elements = chunker.extract_with_layout_awareness()
        
        # Step 2: Build hierarchical document structure
        print(f"\n🏗️ Step 2: Building document hierarchy with batch processing...")
        document_structure = chunker.build_document_hierarchy()
        
        # Display document structure overview
        display_document_structure_optimized(document_structure)
        
        # Step 3: Apply optimized section-aware splitter
        print(f"\n🎯 Step 3: Applying optimized section-aware splitter...")
        chunks = chunker.create_section_aware_chunks()
        
        # Step 4: Generate embeddings with optimizations
        print(f"\n📐 Step 4: Generating embeddings with batch processing and caching...")
        embeddings = chunker.generate_embeddings(optimized_embedding)
        
        # Step 5: Build optimized FAISS index
        print(f"\n💾 Step 5: Building optimized FAISS vector index...")
        faiss_index = chunker.build_faiss_index(INDEX_PATH)
        
        # Step 6: Save metadata with performance metrics
        print(f"\n💾 Step 6: Saving chunk metadata with performance data...")
        chunker.save_chunks_metadata(METADATA_PATH)
        
        # Step 7: Generate comprehensive statistics
        print(f"\n📊 Step 7: Generating performance statistics...")
        stats = chunker.get_chunk_statistics()
        
        # Add optimization metadata
        stats['optimization_info'] = {
            'timestamp': datetime.now().isoformat(),
            'pdf_file': PDF_PATH,
            'index_file': INDEX_PATH,
            'metadata_file': METADATA_PATH,
            'chunking_method': 'optimized_section_aware_layout_preserving',
            'embedding_model': 'text-embedding-3-large',
            'max_tokens_per_chunk': MAX_TOKENS_PER_CHUNK,
            'section_max_tokens': SECTION_MAX_TOKENS,
            'max_workers': MAX_WORKERS,
            'optimizations_enabled': [
                'fast_pdf_extraction',
                'batch_processing',
                'parallel_chunking',
                'embedding_caching',
                'token_counting_cache',
                'optimized_faiss_index',
                'memory_efficient_batching'
            ],
            'cache_stats': get_cache_stats()
        }
        
        # Save statistics with performance data
        with open(STATS_PATH, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        # End main timer
        main_timer.end({
            'total_chunks': len(chunks),
            'total_embeddings': len(embeddings) if embeddings is not None else 0
        })
        
        # Display results with performance analysis
        print("\n" + "=" * 70)
        print("✅ OPTIMIZED SECTION-AWARE CHUNKING COMPLETED!")
        print("=" * 70)
        
        display_performance_summary(stats, chunker.timer)
        display_processing_summary_optimized(stats)
        
        print(f"\n📁 FILES CREATED:")
        print(f"   🗃️ Vector Index: {INDEX_PATH}")
        print(f"   📋 Metadata: {METADATA_PATH}")
        print(f"   📊 Statistics: {STATS_PATH}")
        
        # Test search functionality with timing
        print(f"\n🔍 Testing optimized section-aware search...")
        test_optimized_search(chunker)
        
        print(f"\n🎉 Optimized section-aware vector database is ready!")
        print(f"   ⚡ Processing completed with performance monitoring")
        print(f"   ✅ All optimizations applied successfully")
        
        # Display performance recommendations
        display_performance_recommendations(stats)
        
    except Exception as e:
        print(f"\n❌ Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def display_document_structure_optimized(document_structure: dict):
    """Display optimized document structure overview"""
    print(f"\n📋 DOCUMENT STRUCTURE OVERVIEW:")
    print(f"   📄 Document ID: {document_structure['document_id']}")
    print(f"   📚 Total pages: {document_structure['metadata']['total_pages']}")
    print(f"   🗂️ Top-level sections: {len(document_structure['sections'])}")
    print(f"   📊 Processing strategy: Batch optimized")

def display_performance_summary(stats: dict, timer: PerformanceTimer):
    """Display detailed performance summary"""
    performance = stats.get('performance_metrics', {})
    total_time = performance.get('total_time', 0)
    
    print(f"\n⚡ PERFORMANCE SUMMARY:")
    print(f"   🕒 Total processing time: {total_time:.2f}s")
    
    if 'operations' in performance:
        print(f"   📊 Operation breakdown:")
        for op in performance['operations']:
            print(f"      {op['operation']}: {op['duration']:.2f}s ({op['percentage']:.1f}%)")
    
    # Cache performance
    cache_stats = get_cache_stats()
    if cache_stats['cache_files'] > 0:
        print(f"   💾 Cache performance: {cache_stats['cache_files']} cached embeddings")

def display_processing_summary_optimized(stats: dict):
    """Display optimized processing summary"""
    print(f"\n📊 PROCESSING RESULTS:")
    print(f"   📄 Total chunks created: {stats['total_chunks']}")
    print(f"   🎯 Average tokens per chunk: {stats['avg_token_count']:.1f}")
    print(f"   📏 Average chunk length: {stats['avg_chunk_length']:.0f} characters")
    print(f"   🗂️ Unique sections: {stats['unique_sections']}")
    print(f"   📚 Total characters: {stats['total_characters']:,}")
    print(f"   ✅ Chunks with section context: {stats['chunks_with_context']}")

def test_optimized_search(chunker: OptimizedSectionAwareChunker):
    """Test optimized search functionality with timing"""
    test_queries = [
        "What are the compliance requirements?",
        "How are claims processed?",
        "What are the eligibility criteria?"
    ]
    
    total_search_time = 0
    
    for i, query in enumerate(test_queries, 1):
        try:
            start_time = datetime.now()
            query_embedding = get_query_embedding(query)
            results = chunker.search_similar_chunks(query_embedding, top_k=3)
            search_time = (datetime.now() - start_time).total_seconds()
            total_search_time += search_time
            
            print(f"   Query {i}: '{query}' ({search_time:.3f}s)")
            print(f"   Found {len(results)} relevant chunks")
            
        except Exception as e:
            print(f"   ⚠️ Search test failed for query '{query}': {str(e)}")
    
    avg_search_time = total_search_time / len(test_queries) if test_queries else 0
    print(f"   ⚡ Average search time: {avg_search_time:.3f}s per query")

def display_performance_recommendations(stats: dict):
    """Display performance optimization recommendations"""
    total_chunks = stats.get('total_chunks', 0)
    avg_tokens = stats.get('avg_token_count', 0)
    performance = stats.get('performance_metrics', {})
    total_time = performance.get('total_time', 0)
    
    print(f"\n💡 PERFORMANCE RECOMMENDATIONS:")
    
    if total_time > 60:
        print(f"   📈 For faster processing:")
        print(f"      • Consider reducing chunk size for fewer embeddings")
        print(f"      • Enable more aggressive caching")
    
    if avg_tokens < 100:
        print(f"   🎯 Chunk optimization:")
        print(f"      • Current chunks are small ({avg_tokens:.0f} tokens)")
        print(f"      • Consider increasing max_tokens for better context")
    
    if total_chunks > 500:
        print(f"   📊 Large dataset detected ({total_chunks} chunks):")
        print(f"      • Consider using hierarchical indexing")
        print(f"      • Enable parallel processing for even faster results")
    
    cache_stats = get_cache_stats()
    if not cache_stats['cache_enabled']:
        print(f"   💾 Enable embedding cache for faster subsequent runs")

if __name__ == "__main__":
    main()
    
    # Display final performance metrics
    if len(sys.argv) > 1 and "--detailed-performance" in sys.argv:
        print(f"\n📈 DETAILED PERFORMANCE ANALYSIS:")
        print(f"   Run with --detailed-performance for comprehensive metrics")
