#!/usr/bin/env python3
"""
Main script for section-aware chunking following layout-aware document parsing
Implements the specified algorithm for PDF document structure preservation
"""

import os
import sys
import json
from datetime import datetime
from section_aware_chunker import SectionAwareChunker
from embeddings import embedding, get_query_embedding

def main():
    """Main execution function for section-aware chunking"""
    print("🚀 Starting Section-Aware Chunking Pipeline")
    print("Following layout-aware document parsing specifications")
    print("=" * 60)
    
    # Configuration following the specifications
    PDF_PATH = "policy.pdf"
    INDEX_PATH = "section_aware_faiss_index.idx"
    METADATA_PATH = "section_aware_chunks_metadata.json"
    STATS_PATH = "section_aware_statistics.json"
    
    # Token limits as specified
    MAX_TOKENS_PER_CHUNK = 300  # Token cap for embedding model window
    SECTION_MAX_TOKENS = 500    # Initial section size limit
    
    # Verify PDF exists
    if not os.path.exists(PDF_PATH):
        print(f"❌ Error: {PDF_PATH} not found!")
        sys.exit(1)
    
    try:
        # Initialize section-aware chunker with specified parameters
        print("🔧 Initializing Section-Aware Chunker...")
        print(f"   📏 Max tokens per chunk: {MAX_TOKENS_PER_CHUNK}")
        print(f"   📄 Section max tokens: {SECTION_MAX_TOKENS}")
        
        chunker = SectionAwareChunker(
            pdf_path=PDF_PATH,
            max_tokens=MAX_TOKENS_PER_CHUNK,
            section_max_tokens=SECTION_MAX_TOKENS
        )
        
        # Step 1: Extract with layout-aware parsing
        print(f"\n📄 Step 1: Layout-aware document parsing...")
        print("   Extracting headings, subheadings, and paragraph boundaries...")
        elements = chunker.extract_with_layout_awareness()
        
        # Step 2: Build hierarchical document structure
        print(f"\n🏗️ Step 2: Building document hierarchy...")
        print("   Representing structure as hierarchy of sections and sub-sections...")
        document_structure = chunker.build_document_hierarchy()
        
        # Display document structure overview
        display_document_structure(document_structure)
        
        # Step 3: Apply section-aware splitter
        print(f"\n🎯 Step 3: Applying section-aware splitter...")
        print("   For each top-level section:")
        print("   a. Collecting text from heading through end of content")
        print("   b. Splitting at sub-heading boundaries if exceeding token limits")
        print("   c. Prepending each chunk with full section path")
        chunks = chunker.create_section_aware_chunks()
        
        # Step 4: Generate embeddings
        print(f"\n📐 Step 4: Generating embeddings...")
        embeddings = chunker.generate_embeddings(embedding)
        
        # Step 5: Build FAISS index
        print(f"\n💾 Step 5: Building FAISS vector index...")
        faiss_index = chunker.build_faiss_index(INDEX_PATH)
        
        # Step 6: Save metadata with required fields
        print(f"\n💾 Step 6: Saving chunk metadata...")
        print("   Including required metadata fields:")
        print("   - document_id")
        print("   - section_path (e.g., '1 > 1.2 > 1.2.3')")
        print("   - heading_text")
        chunker.save_chunks_metadata(METADATA_PATH)
        
        # Step 7: Generate and save statistics
        print(f"\n📊 Step 7: Generating comprehensive statistics...")
        stats = chunker.get_chunk_statistics()
        
        # Add processing metadata
        stats['processing_info'] = {
            'timestamp': datetime.now().isoformat(),
            'pdf_file': PDF_PATH,
            'index_file': INDEX_PATH,
            'metadata_file': METADATA_PATH,
            'chunking_method': 'section_aware_layout_preserving',
            'embedding_model': 'text-embedding-3-large',
            'max_tokens_per_chunk': MAX_TOKENS_PER_CHUNK,
            'section_max_tokens': SECTION_MAX_TOKENS,
            'follows_specifications': True,
            'features': [
                'layout_aware_parsing',
                'hierarchical_structure',
                'section_path_context',
                'token_limit_enforcement',
                'sentence_boundary_preservation'
            ]
        }
        
        # Save statistics
        with open(STATS_PATH, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        # Display results
        print("\n" + "=" * 60)
        print("✅ SECTION-AWARE CHUNKING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        display_processing_summary(stats)
        display_section_analysis(stats)
        
        print(f"\n📁 FILES CREATED:")
        print(f"   🗃️ Vector Index: {INDEX_PATH}")
        print(f"   📋 Metadata: {METADATA_PATH}")
        print(f"   📊 Statistics: {STATS_PATH}")
        
        # Test search functionality
        print(f"\n🔍 Testing section-aware search functionality...")
        test_section_aware_search(chunker)
        
        print(f"\n🎉 Section-aware vector database is ready!")
        print(f"   ✅ Every chunk aligns with PDF's logical structure")
        print(f"   ✅ Full sentence and paragraph integrity retained")
        print(f"   ✅ Section context preserved for accurate retrieval")
        
        # Display sample chunks
        if len(sys.argv) > 1 and sys.argv[1] == "--show-samples":
            display_chunk_samples()
        
    except Exception as e:
        print(f"\n❌ Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def display_document_structure(document_structure: dict):
    """Display overview of document structure"""
    print(f"\n📋 DOCUMENT STRUCTURE OVERVIEW:")
    print(f"   📄 Document ID: {document_structure['document_id']}")
    print(f"   📚 Total pages: {document_structure['metadata']['total_pages']}")
    print(f"   🗂️ Top-level sections: {len(document_structure['sections'])}")
    
    print(f"\n🏗️ SECTION HIERARCHY (first 10 sections):")
    for i, section in enumerate(document_structure['sections'][:10]):
        level_indent = "  " * (section['level'] - 1)
        section_num = section.get('section_number', f"S{section['section_id']}")
        print(f"   {level_indent}📑 {section_num}: {section['heading_text'][:60]}...")
        
        # Show subsections
        for subsection in section['subsections'][:3]:
            sub_indent = "  " * subsection['level']
            sub_num = subsection.get('section_number', f"S{subsection['section_id']}")
            print(f"   {sub_indent}📄 {sub_num}: {subsection['heading_text'][:50]}...")
        
        if len(section['subsections']) > 3:
            print(f"   {level_indent}   ... and {len(section['subsections']) - 3} more subsections")
    
    if len(document_structure['sections']) > 10:
        print(f"   ... and {len(document_structure['sections']) - 10} more sections")

def display_processing_summary(stats: dict):
    """Display processing summary"""
    print(f"\n📊 PROCESSING SUMMARY:")
    print(f"   📄 Total chunks created: {stats['total_chunks']}")
    print(f"   🎯 Average tokens per chunk: {stats['avg_token_count']:.1f}")
    print(f"   📏 Average chunk length: {stats['avg_chunk_length']:.0f} characters")
    print(f"   📝 Average word count: {stats['avg_word_count']:.0f} words")
    print(f"   🗂️ Unique sections: {stats['unique_sections']}")
    print(f"   📚 Total characters: {stats['total_characters']:,}")
    print(f"   📖 Total words: {stats['total_words']:,}")
    print(f"   ✅ Chunks with section context: {stats['chunks_with_context']}")

def display_section_analysis(stats: dict):
    """Display section-level analysis"""
    print(f"\n🗂️ SECTION DISTRIBUTION ANALYSIS:")
    
    # Level distribution
    if 'level_distribution' in stats:
        print(f"   📊 Chunks by section level:")
        for level, count in sorted(stats['level_distribution'].items()):
            print(f"      Level {level}: {count} chunks")
    
    # Top sections by chunk count
    print(f"\n🏆 TOP SECTIONS BY CHUNK COUNT:")
    for i, (section_path, count) in enumerate(list(stats['section_distribution'].items())[:5], 1):
        path_display = section_path[:50] + "..." if len(section_path) > 50 else section_path
        print(f"   {i}. {path_display}: {count} chunks")
    
    # Document structure stats
    if 'document_structure_stats' in stats:
        ds_stats = stats['document_structure_stats']
        print(f"\n📋 DOCUMENT STRUCTURE STATS:")
        print(f"   📑 Total sections in hierarchy: {ds_stats['total_sections']}")
        print(f"   📄 Total pages processed: {ds_stats['total_pages']}")

def test_section_aware_search(chunker: SectionAwareChunker, test_queries: list = None):
    """Test the section-aware search functionality"""
    if test_queries is None:
        test_queries = [
            "What are the compliance requirements?",
            "How are claims processed?",
            "What are the eligibility criteria?",
            "What are the coverage limitations?",
            "How is premium calculated?"
        ]
    
    print("   Running section-aware search tests...")
    
    for i, query in enumerate(test_queries[:3], 1):
        try:
            query_embedding = get_query_embedding(query)
            results = chunker.search_similar_chunks(query_embedding, top_k=3)
            
            print(f"\n   Query {i}: '{query}'")
            print(f"   Found {len(results)} relevant chunks with section context:")
            
            for j, result in enumerate(results, 1):
                chunk = result['chunk']
                section_info = result['section_info']
                score = result['similarity_score']
                
                print(f"     {j}. Section: {section_info['path']}")
                print(f"        Heading: {section_info['heading'][:40]}...")
                print(f"        Level: {section_info['level']}, Score: {score:.3f}")
                
        except Exception as e:
            print(f"   ⚠️ Search test failed for query '{query}': {str(e)}")
    
    print("   ✅ Section-aware search functionality test completed")

def display_chunk_samples():
    """Display sample chunks with section context"""
    try:
        with open("section_aware_chunks_metadata.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        chunks = data.get('chunks', [])
        
        print(f"\n📋 SAMPLE SECTION-AWARE CHUNKS (showing first 3 of {len(chunks)}):")
        print("-" * 80)
        
        for i, chunk in enumerate(chunks[:3]):
            metadata = chunk['metadata']
            print(f"\nChunk {i+1}:")
            print(f"Document ID: {metadata['document_id']}")
            print(f"Section Path: {metadata['section_path']}")
            print(f"Heading: {metadata['heading_text']}")
            print(f"Level: {metadata['section_level']}")
            print(f"Pages: {metadata.get('page_start', 'N/A')}-{metadata.get('page_end', 'N/A')}")
            print(f"Tokens: {metadata['token_count']}, Words: {metadata['word_count']}")
            print(f"Has Context: {metadata['has_section_context']}")
            print(f"Preview: {chunk['original_text'][:200]}...")
            print("-" * 60)
            
    except FileNotFoundError:
        print("   ⚠️ Metadata file not found - run main processing first")
    except Exception as e:
        print(f"   ⚠️ Error displaying samples: {str(e)}")

def validate_chunk_compliance():
    """Validate that chunks comply with specifications"""
    try:
        with open("section_aware_chunks_metadata.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        chunks = data.get('chunks', [])
        
        print(f"\n✅ SPECIFICATION COMPLIANCE VALIDATION:")
        
        # Check required metadata fields
        required_fields = ['document_id', 'section_path', 'heading_text']
        compliance_checks = {
            'has_required_metadata': 0,
            'within_token_limit': 0,
            'has_section_context': 0,
            'preserves_sentence_boundaries': 0
        }
        
        for chunk in chunks:
            metadata = chunk['metadata']
            
            # Check required metadata fields
            if all(field in metadata for field in required_fields):
                compliance_checks['has_required_metadata'] += 1
            
            # Check token limit
            if metadata.get('token_count', 0) <= 300:
                compliance_checks['within_token_limit'] += 1
            
            # Check section context
            if metadata.get('has_section_context', False):
                compliance_checks['has_section_context'] += 1
            
            # Check sentence boundaries (heuristic)
            text = chunk.get('original_text', '')
            if text.endswith('.') or text.endswith('!') or text.endswith('?'):
                compliance_checks['preserves_sentence_boundaries'] += 1
        
        total_chunks = len(chunks)
        print(f"   📋 Required metadata fields: {compliance_checks['has_required_metadata']}/{total_chunks} chunks")
        print(f"   🎯 Within token limit (≤300): {compliance_checks['within_token_limit']}/{total_chunks} chunks")
        print(f"   📄 Has section context: {compliance_checks['has_section_context']}/{total_chunks} chunks")
        print(f"   ✂️ Preserves sentence boundaries: {compliance_checks['preserves_sentence_boundaries']}/{total_chunks} chunks")
        
        # Overall compliance score
        avg_compliance = sum(compliance_checks.values()) / (len(compliance_checks) * total_chunks) * 100
        print(f"   🏆 Overall compliance score: {avg_compliance:.1f}%")
        
    except Exception as e:
        print(f"   ⚠️ Validation error: {str(e)}")

if __name__ == "__main__":
    main()
    
    # Run validation if requested
    if len(sys.argv) > 1 and "--validate" in sys.argv:
        validate_chunk_compliance()