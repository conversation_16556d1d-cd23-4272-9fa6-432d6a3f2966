"""
Section-Aware Chunking for PDF Documents
Following layout-aware document parsing with hierarchical structure preservation
"""
import os
import re
import json
import numpy as np
import faiss
from typing import List, Dict, Any, Tuple, Optional
from unstructured.partition.pdf import partition_pdf
from unstructured.documents.elements import Title, NarrativeText, ListItem, Table
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from collections import defaultdict
import tiktoken

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

class SectionAwareChunker:
    """
    Advanced section-aware chunker that preserves PDF document structure
    """
    
    def __init__(self, pdf_path: str, max_tokens: int = 300, section_max_tokens: int = 500):
        self.pdf_path = pdf_path
        self.max_tokens = max_tokens
        self.section_max_tokens = section_max_tokens
        self.elements = None
        self.document_structure = None
        self.chunks = None
        self.embeddings = None
        self.faiss_index = None
        
        # Initialize tokenizer for accurate token counting
        try:
            self.tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo")
        except:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
    
    def extract_with_layout_awareness(self) -> List[Any]:
        """Extract elements from PDF with layout-aware parsing"""
        print("📄 Extracting elements with layout-aware parsing...")
        
        self.elements = partition_pdf(
            filename=self.pdf_path,
            strategy="hi_res",  # High resolution for better layout detection
            infer_table_structure=True,
            extract_images_in_pdf=False,
            include_page_breaks=True,
            chunking_strategy=None  # We'll handle chunking ourselves
        )
        
        print(f"✅ Extracted {len(self.elements)} elements with layout information")
        return self.elements
    
    def build_document_hierarchy(self) -> Dict[str, Any]:
        """Build hierarchical document structure from extracted elements"""
        print("🏗️ Building document hierarchy...")
        
        if not self.elements:
            self.extract_with_layout_awareness()
        
        # Initialize document structure
        page_numbers = []
        for elem in self.elements:
            if hasattr(elem, 'metadata') and hasattr(elem.metadata, 'page_number'):
                page_num = elem.metadata.page_number
                if page_num is not None:
                    page_numbers.append(page_num)
        
        self.document_structure = {
            "document_id": os.path.basename(self.pdf_path).replace('.pdf', ''),
            "sections": [],
            "metadata": {
                "total_elements": len(self.elements),
                "total_pages": max(page_numbers) if page_numbers else 1
            }
        }
        
        current_section = None
        section_stack = []  # Stack to track nested sections
        
        for i, element in enumerate(self.elements):
            element_text = str(element).strip()
            if not element_text:
                continue
            
            element_type = getattr(element, 'category', 'unknown')
            page_num = getattr(element.metadata, 'page_number', None) if hasattr(element, 'metadata') else None
            
            # Detect headers and their levels
            header_info = self._analyze_header(element_text, element_type)
            
            if header_info['is_header']:
                # Create new section
                section = {
                    "section_id": len(self.document_structure["sections"]),
                    "level": header_info['level'],
                    "heading_text": element_text,
                    "section_number": header_info['section_number'],
                    "content": [],
                    "subsections": [],
                    "page_start": page_num,
                    "page_end": page_num,
                    "parent_path": []
                }
                
                # Handle section hierarchy
                self._handle_section_hierarchy(section, section_stack)
                
                # Add to document structure
                if section['level'] == 1:
                    self.document_structure["sections"].append(section)
                    current_section = section
                else:
                    # Add as subsection to appropriate parent
                    parent = self._find_parent_section(section_stack, section['level'])
                    if parent:
                        parent["subsections"].append(section)
                        section["parent_path"] = parent.get("parent_path", []) + [parent["heading_text"]]
                    else:
                        # Fallback: add as top-level section
                        self.document_structure["sections"].append(section)
                    current_section = section
                
                # Update section stack
                self._update_section_stack(section_stack, section)
                
            else:
                # Add content to current section
                if current_section is not None:
                    content_item = {
                        "text": element_text,
                        "type": element_type,
                        "page": page_num,
                        "element_index": i
                    }
                    current_section["content"].append(content_item)
                    current_section["page_end"] = page_num or current_section["page_end"]
        
        print(f"✅ Built hierarchy with {len(self.document_structure['sections'])} top-level sections")
        return self.document_structure
    
    def _analyze_header(self, text: str, element_type: str) -> Dict[str, Any]:
        """Analyze if text is a header and determine its properties"""
        header_info = {
            "is_header": False,
            "level": 0,
            "section_number": None
        }
        
        # Check element type first
        if element_type in ['Title', 'Header']:
            header_info["is_header"] = True
            header_info["level"] = 1
        
        # Pattern-based header detection
        header_patterns = [
            (r'^(\d+)\.\s+([A-Z][^a-z]*[A-Z].*)', 1),  # "1. INTRODUCTION"
            (r'^(\d+\.\d+)\s+([A-Z].*)', 2),           # "1.1 Overview"
            (r'^(\d+\.\d+\.\d+)\s+([A-Z].*)', 3),      # "1.1.1 Details"
            (r'^(\d+\.\d+\.\d+\.\d+)\s+([A-Z].*)', 4), # "******* Sub-details"
            (r'^([A-Z][A-Z\s]{5,})$', 1),              # All caps headers
            (r'^(SECTION|CHAPTER|PART|ARTICLE)\s+(\d+|[IVX]+)', 1),  # "SECTION 1"
        ]
        
        for pattern, level in header_patterns:
            match = re.match(pattern, text.strip())
            if match:
                header_info["is_header"] = True
                header_info["level"] = level
                header_info["section_number"] = match.group(1) if match.groups() else None
                break
        
        # Additional heuristics for headers
        if not header_info["is_header"]:
            # Short, capitalized text likely to be headers
            if (len(text) < 100 and 
                len(text.split()) > 1 and 
                text.isupper() and 
                not text.endswith('.')):
                header_info["is_header"] = True
                header_info["level"] = 2
        
        return header_info
    
    def _handle_section_hierarchy(self, section: Dict, section_stack: List[Dict]):
        """Handle section hierarchy and nesting"""
        # Build section path based on hierarchy
        section_path_parts = []
        
        # Add parent sections to path
        for parent in section_stack:
            if parent["level"] < section["level"]:
                section_path_parts.append(str(parent.get("section_number", parent["section_id"])))
        
        # Add current section
        if section.get("section_number"):
            section_path_parts.append(section["section_number"])
        else:
            section_path_parts.append(str(section["section_id"]))
        
        section["section_path"] = " > ".join(section_path_parts)
    
    def _find_parent_section(self, section_stack: List[Dict], current_level: int) -> Optional[Dict]:
        """Find the appropriate parent section for the current level"""
        for section in reversed(section_stack):
            if section["level"] < current_level:
                return section
        return None
    
    def _update_section_stack(self, section_stack: List[Dict], new_section: Dict):
        """Update the section stack for hierarchy tracking"""
        # Remove sections at same or deeper level
        section_stack[:] = [s for s in section_stack if s["level"] < new_section["level"]]
        # Add new section
        section_stack.append(new_section)
    
    def create_section_aware_chunks(self) -> List[Dict[str, Any]]:
        """Create section-aware chunks following the specified algorithm"""
        print("🎯 Creating section-aware chunks...")
        
        if not self.document_structure:
            self.build_document_hierarchy()
        
        chunks = []
        chunk_id = 0
        
        # Process each top-level section
        for section in self.document_structure["sections"]:
            section_chunks = self._process_section_for_chunking(section, chunk_id)
            chunks.extend(section_chunks)
            chunk_id += len(section_chunks)
        
        print(f"✅ Created {len(chunks)} section-aware chunks")
        self.chunks = chunks
        return chunks
    
    def _process_section_for_chunking(self, section: Dict, start_chunk_id: int) -> List[Dict[str, Any]]:
        """Process a section and its subsections for chunking"""
        chunks = []
        
        # Collect all text from section heading through end of content
        full_section_text = self._collect_section_text(section)
        
        # Count tokens in full section
        section_tokens = self._count_tokens(full_section_text)
        
        if section_tokens <= self.section_max_tokens:
            # Section fits within limit, create chunks respecting max_tokens
            section_chunks = self._split_section_by_tokens(
                section, full_section_text, start_chunk_id
            )
            chunks.extend(section_chunks)
        else:
            # Section too large, split at subsection boundaries
            chunks.extend(self._split_large_section(section, start_chunk_id))
        
        return chunks
    
    def _collect_section_text(self, section: Dict) -> str:
        """Collect all text from a section including subsections"""
        text_parts = []
        
        # Add section heading
        text_parts.append(section["heading_text"])
        
        # Add section content
        for content_item in section["content"]:
            text_parts.append(content_item["text"])
        
        # Add subsection content recursively
        for subsection in section["subsections"]:
            subsection_text = self._collect_section_text(subsection)
            text_parts.append(subsection_text)
        
        return "\n\n".join(text_parts)
    
    def _split_section_by_tokens(self, section: Dict, section_text: str, start_chunk_id: int) -> List[Dict[str, Any]]:
        """Split section text into chunks respecting token limits"""
        chunks = []
        
        # Split into sentences for better boundary detection
        sentences = sent_tokenize(section_text)
        
        current_chunk_text = ""
        current_sentences = []
        
        # Create section path for context
        section_path = self._build_section_path(section)
        section_context = f"### {section_path}: {section['heading_text']}"
        
        for sentence in sentences:
            # Check if adding this sentence would exceed token limit
            potential_text = current_chunk_text + "\n" + sentence if current_chunk_text else sentence
            full_chunk_text = section_context + "\n\n" + potential_text
            
            if self._count_tokens(full_chunk_text) <= self.max_tokens:
                current_chunk_text = potential_text
                current_sentences.append(sentence)
            else:
                # Create chunk with current content
                if current_chunk_text:
                    chunk = self._create_chunk(
                        chunk_id=start_chunk_id + len(chunks),
                        text=current_chunk_text,
                        section=section,
                        section_context=section_context
                    )
                    chunks.append(chunk)
                
                # Start new chunk with current sentence
                current_chunk_text = sentence
                current_sentences = [sentence]
        
        # Don't forget the last chunk
        if current_chunk_text:
            chunk = self._create_chunk(
                chunk_id=start_chunk_id + len(chunks),
                text=current_chunk_text,
                section=section,
                section_context=section_context
            )
            chunks.append(chunk)
        
        return chunks
    
    def _split_large_section(self, section: Dict, start_chunk_id: int) -> List[Dict[str, Any]]:
        """Split large sections at subsection boundaries"""
        chunks = []
        
        # Process main section content first
        if section["content"]:
            main_content = "\n\n".join([item["text"] for item in section["content"]])
            if main_content.strip():
                main_section_chunks = self._split_section_by_tokens(
                    section, section["heading_text"] + "\n\n" + main_content, start_chunk_id
                )
                chunks.extend(main_section_chunks)
        
        # Process each subsection
        for subsection in section["subsections"]:
            subsection_chunks = self._process_section_for_chunking(
                subsection, start_chunk_id + len(chunks)
            )
            chunks.extend(subsection_chunks)
        
        return chunks
    
    def _build_section_path(self, section: Dict) -> str:
        """Build full section path for context"""
        path_parts = []
        
        # Add parent path
        if section.get("parent_path"):
            path_parts.extend(section["parent_path"])
        
        # Add current section
        if section.get("section_number"):
            path_parts.append(f"Section {section['section_number']}")
        else:
            path_parts.append(f"Section {section['section_id']}")
        
        return " > ".join(path_parts) if path_parts else f"Section {section.get('section_id', 'Unknown')}"
    
    def _create_chunk(self, chunk_id: int, text: str, section: Dict, section_context: str) -> Dict[str, Any]:
        """Create a chunk with proper metadata"""
        # Prepend section context to chunk text
        full_text = section_context + "\n\n" + text
        
        return {
            "id": chunk_id,
            "text": full_text,
            "original_text": text,
            "metadata": {
                "document_id": self.document_structure["document_id"],
                "section_path": section.get("section_path", ""),
                "heading_text": section["heading_text"],
                "section_level": section["level"],
                "section_id": section["section_id"],
                "page_start": section.get("page_start"),
                "page_end": section.get("page_end"),
                "chunk_index": chunk_id,
                "token_count": self._count_tokens(full_text),
                "char_count": len(full_text),
                "word_count": len(full_text.split()),
                "has_section_context": True
            }
        }
    
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken"""
        try:
            return len(self.tokenizer.encode(text))
        except:
            # Fallback to word-based estimation
            return len(text.split()) * 1.3  # Rough approximation
    
    def generate_embeddings(self, embedding_function):
        """Generate embeddings for chunks"""
        if not self.chunks:
            raise ValueError("No chunks available. Run create_section_aware_chunks() first.")
        
        print("📐 Generating embeddings...")
        chunk_texts = [chunk['text'] for chunk in self.chunks]
        self.embeddings = embedding_function(chunk_texts)
        print(f"✅ Generated embeddings for {len(self.embeddings)} chunks")
        return self.embeddings
    
    def build_faiss_index(self, index_path: str = "section_aware_faiss_index.idx"):
        """Build and save FAISS index"""
        if not self.embeddings:
            raise ValueError("No embeddings available. Run generate_embeddings() first.")
        
        print("💾 Building section-aware FAISS index...")
        dim = len(self.embeddings[0])
        
        # Use appropriate index type based on dataset size
        if len(self.embeddings) > 100:
            nlist = min(100, len(self.embeddings) // 10)
            quantizer = faiss.IndexFlatL2(dim)
            self.faiss_index = faiss.IndexIVFFlat(quantizer, dim, nlist)
            
            embeddings_array = np.array(self.embeddings).astype("float32")
            self.faiss_index.train(embeddings_array)
            self.faiss_index.add(embeddings_array)
            self.faiss_index.nprobe = min(10, nlist)
        else:
            # Use flat index for smaller datasets
            self.faiss_index = faiss.IndexFlatL2(dim)
            embeddings_array = np.array(self.embeddings).astype("float32")
            self.faiss_index.add(embeddings_array)
        
        # Save index
        faiss.write_index(self.faiss_index, index_path)
        print(f"✅ Section-aware FAISS index saved to {index_path}")
        
        return self.faiss_index
    
    def save_chunks_metadata(self, metadata_path: str = "section_aware_chunks_metadata.json"):
        """Save chunk metadata to JSON file"""
        if not self.chunks:
            raise ValueError("No chunks available.")
        
        # Convert numpy types to native Python types for JSON serialization
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj
        
        # Prepare data for saving
        save_data = {
            "document_structure": convert_numpy_types(self.document_structure),
            "chunks": convert_numpy_types(self.chunks),
            "processing_metadata": {
                "max_tokens": self.max_tokens,
                "section_max_tokens": self.section_max_tokens,
                "total_chunks": len(self.chunks),
                "chunking_method": "section_aware_layout_preserving"
            }
        }
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)
        print(f"✅ Section-aware chunks metadata saved to {metadata_path}")
    
    def get_chunk_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the chunks"""
        if not self.chunks:
            return {}
        
        chunk_lengths = [chunk['metadata']['char_count'] for chunk in self.chunks]
        token_counts = [chunk['metadata']['token_count'] for chunk in self.chunks]
        word_counts = [chunk['metadata']['word_count'] for chunk in self.chunks]
        
        # Section-level statistics
        sections_per_chunk = [chunk['metadata']['section_level'] for chunk in self.chunks]
        section_paths = [chunk['metadata']['section_path'] for chunk in self.chunks]
        
        from collections import Counter
        section_distribution = Counter(section_paths)
        level_distribution = Counter(sections_per_chunk)
        
        stats = {
            'total_chunks': len(self.chunks),
            'avg_chunk_length': np.mean(chunk_lengths),
            'median_chunk_length': np.median(chunk_lengths),
            'min_chunk_length': min(chunk_lengths),
            'max_chunk_length': max(chunk_lengths),
            'std_chunk_length': np.std(chunk_lengths),
            'total_characters': sum(chunk_lengths),
            'avg_token_count': np.mean(token_counts),
            'max_token_count': max(token_counts),
            'avg_word_count': np.mean(word_counts),
            'total_words': sum(word_counts),
            'unique_sections': len(section_distribution),
            'section_distribution': dict(section_distribution.most_common(10)),
            'level_distribution': dict(level_distribution),
            'chunks_with_context': sum(1 for chunk in self.chunks if chunk['metadata']['has_section_context']),
            'document_structure_stats': {
                'total_sections': len(self.document_structure['sections']) if self.document_structure else 0,
                'total_pages': self.document_structure['metadata']['total_pages'] if self.document_structure else 0
            }
        }
        
        return stats
    
    def search_similar_chunks(self, query_embedding: List[float], top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar chunks using FAISS with section-aware scoring"""
        if not self.faiss_index:
            raise ValueError("No FAISS index loaded.")
        
        query_array = np.array([query_embedding]).astype("float32")
        distances, indices = self.faiss_index.search(query_array, top_k * 2)
        
        results = []
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            if idx < len(self.chunks):
                chunk = self.chunks[idx]
                
                # Enhanced scoring with section context
                base_score = 1.0 / (1.0 + distance)
                
                # Boost score for chunks with good section context
                context_boost = 1.0
                if chunk['metadata']['has_section_context']:
                    context_boost += 0.1
                if chunk['metadata']['token_count'] >= 50:  # Substantial content
                    context_boost += 0.05
                
                enhanced_score = base_score * context_boost
                
                results.append({
                    'chunk': chunk,
                    'distance': float(distance),
                    'similarity_score': float(enhanced_score),
                    'rank': i + 1,
                    'section_info': {
                        'path': chunk['metadata']['section_path'],
                        'heading': chunk['metadata']['heading_text'],
                        'level': chunk['metadata']['section_level']
                    }
                })
        
        # Sort by enhanced score and return top_k
        results.sort(key=lambda x: x['similarity_score'], reverse=True)
        return results[:top_k]