"""
Optimized embeddings module with batch processing and caching
"""
import os
import time
import pickle
import hashlib
from openai import AzureOpenAI
from dotenv import load_dotenv
from typing import List, Optional
import numpy as np

# Load environment variables from .env
load_dotenv()

# Read config from environment
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
api_key = os.getenv("AZURE_OPENAI_API_KEY")
api_version = os.getenv("AZURE_OPENAI_API_VERSION")
deployment = os.getenv("AZURE_EMBEDDING_MODEL")

# Initialize Azure OpenAI client
client = AzureOpenAI(
    api_version=api_version,
    azure_endpoint=endpoint,
    api_key=api_key,
)

# Cache settings
CACHE_DIR = ".embedding_cache"
CACHE_ENABLED = True

def _get_cache_path(text_hash: str) -> str:
    """Get cache file path for given text hash"""
    if not os.path.exists(CACHE_DIR):
        os.makedirs(CACHE_DIR)
    return os.path.join(CACHE_DIR, f"{text_hash}.pkl")

def _get_text_hash(texts: List[str]) -> str:
    """Get hash for list of texts"""
    combined_text = "||".join(texts)
    return hashlib.md5(combined_text.encode()).hexdigest()

def _load_from_cache(text_hash: str) -> Optional[List[List[float]]]:
    """Load embeddings from cache if available"""
    if not CACHE_ENABLED:
        return None
    
    cache_path = _get_cache_path(text_hash)
    if os.path.exists(cache_path):
        try:
            with open(cache_path, 'rb') as f:
                return pickle.load(f)
        except:
            # Remove corrupted cache file
            os.remove(cache_path)
    return None

def _save_to_cache(text_hash: str, embeddings: List[List[float]]):
    """Save embeddings to cache"""
    if not CACHE_ENABLED:
        return
    
    cache_path = _get_cache_path(text_hash)
    try:
        with open(cache_path, 'wb') as f:
            pickle.dump(embeddings, f)
    except:
        pass  # Ignore cache save errors

def optimized_embedding(chunks: List[str], deployment: str = deployment, batch_size: int = 50, show_progress: bool = True) -> List[List[float]]:
    """
    Optimized embedding function with caching and batch processing
    
    Args:
        chunks: List of text chunks to embed
        deployment: Azure OpenAI deployment name
        batch_size: Size of batches for API calls (max 100 for Azure)
        show_progress: Whether to show progress information
    
    Returns:
        List of embeddings
    """
    if not chunks:
        return []
    
    start_time = time.time()
    
    # Check cache first
    text_hash = _get_text_hash(chunks)
    cached_embeddings = _load_from_cache(text_hash)
    
    if cached_embeddings is not None:
        if show_progress:
            print(f"📦 Loaded {len(cached_embeddings)} embeddings from cache in {time.time() - start_time:.2f}s")
        return cached_embeddings
    
    all_embeddings = []
    total_batches = (len(chunks) + batch_size - 1) // batch_size
    total_tokens = 0
    
    if show_progress:
        print(f"🚀 Generating embeddings for {len(chunks)} chunks in {total_batches} batches")
    
    for i in range(0, len(chunks), batch_size):
        batch_start_time = time.time()
        batch = chunks[i:i + batch_size]
        batch_num = i // batch_size + 1
        
        try:
            response = client.embeddings.create(
                input=batch,
                model=deployment
            )
            
            batch_embeddings = [item.embedding for item in response.data]
            all_embeddings.extend(batch_embeddings)
            
            # Track token usage
            if hasattr(response, 'usage'):
                total_tokens += response.usage.total_tokens
            
            batch_time = time.time() - batch_start_time
            
            if show_progress:
                print(f"✅ Batch {batch_num}/{total_batches} completed in {batch_time:.2f}s ({len(batch)} texts)")
        
        except Exception as e:
            print(f"❌ Error in batch {batch_num}: {str(e)}")
            # For failed batches, add zero embeddings to maintain consistency
            embedding_dim = 3072  # Default for text-embedding-3-large
            for _ in batch:
                all_embeddings.append([0.0] * embedding_dim)
    
    total_time = time.time() - start_time
    
    if show_progress:
        print(f"🎉 Generated {len(all_embeddings)} embeddings in {total_time:.2f}s")
        print(f"📊 Total tokens used: {total_tokens:,}")
        print(f"⚡ Average time per embedding: {total_time/len(chunks):.3f}s")
    
    # Save to cache
    _save_to_cache(text_hash, all_embeddings)
    
    return all_embeddings

def embedding(chunks: List[str], deployment: str = deployment) -> List[List[float]]:
    """
    Legacy embedding function for backward compatibility
    """
    return optimized_embedding(chunks, deployment, show_progress=False)

def get_query_embedding(query: str, deployment: str = deployment) -> List[float]:
    """
    Get embedding for a single query with caching
    """
    embeddings = optimized_embedding([query], deployment, show_progress=False)
    return embeddings[0] if embeddings else []

def clear_embedding_cache():
    """Clear the embedding cache directory"""
    if os.path.exists(CACHE_DIR):
        import shutil
        shutil.rmtree(CACHE_DIR)
        print(f"🗑️ Cleared embedding cache directory: {CACHE_DIR}")

def get_cache_stats():
    """Get statistics about the embedding cache"""
    if not os.path.exists(CACHE_DIR):
        return {"cache_enabled": CACHE_ENABLED, "cache_files": 0, "cache_size_mb": 0}
    
    cache_files = len([f for f in os.listdir(CACHE_DIR) if f.endswith('.pkl')])
    cache_size = sum(
        os.path.getsize(os.path.join(CACHE_DIR, f))
        for f in os.listdir(CACHE_DIR)
        if f.endswith('.pkl')
    )
    
    return {
        "cache_enabled": CACHE_ENABLED,
        "cache_files": cache_files,
        "cache_size_mb": cache_size / (1024 * 1024),
        "cache_directory": CACHE_DIR
    }
