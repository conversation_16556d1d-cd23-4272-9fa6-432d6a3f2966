"""
Performance comparison between Unstructured 'fast' strategy and Mistral OCR for PDF text extraction.
"""

import time
import sys
import traceback
from pathlib import Path

# Add the chunking directory to Python path for imports
sys.path.append('/home/<USER>/Hackathon/Bajaj-Hackathon')

from chunking.optimized_section_aware_chunker import OptimizedSectionAwareChunker
from chunking.mistral_optimized_section_aware_chunker import MistralOptimizedSectionAwareChunker


def run_performance_comparison(pdf_path: str, iterations: int = 1):
    """
    Compare performance between Unstructured and Mistral OCR approaches.
    
    Args:
        pdf_path: Path to the PDF document
        iterations: Number of test iterations
    """
    print("🔍 PDF Text Extraction Performance Comparison")
    print("=" * 60)
    print(f"Document: {pdf_path}")
    print(f"Iterations: {iterations}")
    print()
    
    unstructured_results = []
    mistral_results = []
    
    # Test Unstructured approach
    print("📄 Testing Unstructured 'Hi-Res' Strategy...")
    print("-" * 40)
    
    try:
        chunker_unstructured = OptimizedSectionAwareChunker(pdf_path)
        
        for i in range(iterations):
            print(f"  Run {i+1}/{iterations}...")
            start_time = time.time()
            
            # Extract elements with hi_res strategy
            elements = chunker_unstructured.extract_with_layout_awareness()
            
            # Build document hierarchy
            document_structure = chunker_unstructured.build_document_hierarchy()
            
            # Create chunks
            chunks = chunker_unstructured.create_section_aware_chunks()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Get timings from timer
            timing_stats = {
                'pdf_extraction': 0.0,
                'section_identification': 0.0,
                'chunk_creation': 0.0,
                'total_processing': total_time
            }
            
            # Extract timing information from timer if available
            if hasattr(chunker_unstructured.timer, 'timings'):
                for timing in chunker_unstructured.timer.timings:
                    if 'extraction' in timing.operation.lower():
                        timing_stats['pdf_extraction'] += timing.duration
                    elif 'hierarchy' in timing.operation.lower():
                        timing_stats['section_identification'] += timing.duration
                    elif 'chunk' in timing.operation.lower():
                        timing_stats['chunk_creation'] += timing.duration
            
            unstructured_results.append({
                'total_time': total_time,
                'timing_stats': timing_stats,
                'chunks': len(chunks),
                'characters': sum(len(chunk.get('text', chunk.get('content', ''))) for chunk in chunks)
            })
            
            print(f"    ✅ Completed in {total_time:.2f}s - {len(chunks)} chunks")
    
    except Exception as e:
        print(f"    ❌ Unstructured failed: {e}")
        traceback.print_exc()
        unstructured_results = []
    
    print()
    
    # Test Mistral OCR approach
    print("🤖 Testing Mistral OCR Strategy...")
    print("-" * 40)
    
    try:
        chunker_mistral = MistralOptimizedSectionAwareChunker()
        
        for i in range(iterations):
            print(f"  Run {i+1}/{iterations}...")
            start_time = time.time()
            
            chunks, timing_stats = chunker_mistral.process_document_optimized(pdf_path)
            
            end_time = time.time()
            total_time = end_time - start_time
            mistral_results.append({
                'total_time': total_time,
                'timing_stats': timing_stats,
                'chunks': len(chunks),
                'characters': sum(len(chunk['content']) for chunk in chunks)
            })
            
            print(f"    ✅ Completed in {total_time:.2f}s - {len(chunks)} chunks")
    
    except Exception as e:
        print(f"    ❌ Mistral OCR failed: {e}")
        traceback.print_exc()
        mistral_results = []
    
    print()
    
    # Compare results
    print("📊 Performance Comparison Results")
    print("=" * 60)
    
    if unstructured_results and mistral_results:
        # Calculate averages
        avg_unstructured = sum(r['total_time'] for r in unstructured_results) / len(unstructured_results)
        avg_mistral = sum(r['total_time'] for r in mistral_results) / len(mistral_results)
        
        avg_unstructured_chars = sum(r['characters'] for r in unstructured_results) / len(unstructured_results)
        avg_mistral_chars = sum(r['characters'] for r in mistral_results) / len(mistral_results)
        
        avg_unstructured_chunks = sum(r['chunks'] for r in unstructured_results) / len(unstructured_results)
        avg_mistral_chunks = sum(r['chunks'] for r in mistral_results) / len(mistral_results)
        
        print(f"Unstructured 'Fast' Strategy:")
        print(f"  ⏱️  Average time: {avg_unstructured:.2f}s")
        print(f"  📝 Average characters: {avg_unstructured_chars:,.0f}")
        print(f"  📄 Average chunks: {avg_unstructured_chunks:.0f}")
        print()
        
        print(f"Mistral OCR Strategy:")
        print(f"  ⏱️  Average time: {avg_mistral:.2f}s")
        print(f"  📝 Average characters: {avg_mistral_chars:,.0f}")
        print(f"  📄 Average chunks: {avg_mistral_chunks:.0f}")
        print()
        
        # Performance comparison
        if avg_unstructured < avg_mistral:
            speedup = avg_mistral / avg_unstructured
            print(f"🏆 Winner: Unstructured 'Fast' Strategy")
            print(f"   Speedup: {speedup:.2f}x faster than Mistral OCR")
        else:
            speedup = avg_unstructured / avg_mistral
            print(f"🏆 Winner: Mistral OCR Strategy")
            print(f"   Speedup: {speedup:.2f}x faster than Unstructured")
        
        # Text extraction quality comparison
        char_diff = abs(avg_mistral_chars - avg_unstructured_chars)
        char_diff_percent = (char_diff / max(avg_mistral_chars, avg_unstructured_chars)) * 100
        
        print(f"\n📊 Text Extraction Quality:")
        print(f"   Character count difference: {char_diff:,.0f} ({char_diff_percent:.1f}%)")
        
        if char_diff_percent < 5:
            print("   ✅ Similar text extraction quality")
        elif char_diff_percent < 15:
            print("   ⚠️  Moderate difference in text extraction")
        else:
            print("   ❌ Significant difference in text extraction")
    
    elif unstructured_results:
        print("❌ Only Unstructured results available")
        avg_time = sum(r['total_time'] for r in unstructured_results) / len(unstructured_results)
        print(f"   Unstructured average time: {avg_time:.2f}s")
    
    elif mistral_results:
        print("❌ Only Mistral OCR results available")
        avg_time = sum(r['total_time'] for r in mistral_results) / len(mistral_results)
        print(f"   Mistral OCR average time: {avg_time:.2f}s")
    
    else:
        print("❌ No successful results from either approach")
    
    # Detailed breakdown for latest runs
    if unstructured_results and mistral_results:
        print(f"\n🔍 Detailed Timing Breakdown (Latest Runs):")
        print(f"Unstructured Strategy:")
        latest_unstruct = unstructured_results[-1]['timing_stats']
        for component, duration in latest_unstruct.items():
            print(f"  {component}: {duration:.2f}s")
        
        print(f"\nMistral OCR Strategy:")
        latest_mistral = mistral_results[-1]['timing_stats']
        for component, duration in latest_mistral.items():
            print(f"  {component}: {duration:.2f}s")


if __name__ == "__main__":
    pdf_path = "/home/<USER>/Hackathon/Bajaj-Hackathon/chunking/policy.pdf"
    
    if not Path(pdf_path).exists():
        print(f"❌ PDF file not found: {pdf_path}")
        sys.exit(1)
    
    print("🚀 Starting PDF text extraction performance comparison...")
    print()
    
    # Run comparison with 2 iterations for better averages
    run_performance_comparison(pdf_path, iterations=1)
    
    print("\n✅ Performance comparison complete!")
