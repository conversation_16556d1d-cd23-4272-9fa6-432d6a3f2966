import os
import time
import requests
import json
import re
from pathlib import Path
from typing import List, Dict, Any, Tuple
from unstructured.partition.pdf import partition_pdf

class PerformanceTimer:
    """Context manager for timing operations"""
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.duration = None
    
    def __enter__(self):
        print(f"⏱️ Starting: {self.operation_name}")
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.duration = time.time() - self.start_time
        print(f"✅ Completed: {self.operation_name} in {self.duration:.2f}s")


class MistralOptimizedSectionAwareChunker:
    """Optimized section-aware chunker using Mistral OCR"""
    
    def __init__(self):
        """Initialize the Mistral OCR chunker"""
        # Azure AI configuration for Mistral OCR
        self.ocr_endpoint = "https://mistral-ocr-2503-okcsm.eastus2.models.ai.azure.com/v1/chat/completions"
        self.ocr_key = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        
        self.headers = {
            "Authorization": f"Bearer {self.ocr_key}",
            "Content-Type": "application/json"
        }
        
        print(f"✅ Mistral OCR Chunker initialized with endpoint: {self.ocr_endpoint[:50]}...")
    
    def _mistral_ocr_extract(self, pdf_path: Path) -> str:
        """Extract text from PDF using Mistral OCR"""
        try:
            # Convert PDF to text using Mistral OCR
            payload = {
                "model": "mistral-pixtral-12b-2409",
                "messages": [
                    {
                        "role": "user",
                        "content": f"Extract all text content from this PDF file: {pdf_path}. Return only the text content without any formatting."
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.1
            }
            
            response = requests.post(
                self.ocr_endpoint,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                return content.strip()
            else:
                print(f"⚠️  Mistral OCR failed: {response.status_code}")
                return ""
                
        except Exception as e:
            print(f"⚠️  Mistral OCR error: {e}")
            return ""
    
    def process_document_optimized(self, pdf_path: str) -> Tuple[List[Dict], Dict]:
        """Process document using both Mistral OCR and fallback unstructured"""
        timing_stats = {}
        
        # Try Mistral OCR first
        with PerformanceTimer("mistral_ocr_extraction") as timer:
            mistral_text = self._mistral_ocr_extract(Path(pdf_path))
            timing_stats['mistral_ocr_extraction'] = timer.duration
        
        # If Mistral OCR provides good content, use it
        if mistral_text and len(mistral_text.strip()) > 1000:
            print(f"✅ Using Mistral OCR extraction ({len(mistral_text)} characters)")
            
            with PerformanceTimer("section_identification") as timer:
                sections = self._build_sections_from_mistral_content(mistral_text)
                timing_stats['section_identification'] = timer.duration
        else:
            print("⚠️  Mistral OCR insufficient, falling back to unstructured")
            
            # Fallback to unstructured PDF extraction
            with PerformanceTimer("unstructured_extraction") as timer:
                elements = partition_pdf(
                    filename=pdf_path,
                    strategy="hi_res",
                    infer_table_structure=False,
                    extract_images_in_pdf=False,
                    include_page_breaks=True,
                    chunking_strategy=None,
                    max_characters=10000
                )
                timing_stats['unstructured_extraction'] = timer.duration
            
            with PerformanceTimer("section_identification") as timer:
                sections = self._build_sections_from_elements(elements)
                timing_stats['section_identification'] = timer.duration
        
        with PerformanceTimer("chunk_creation") as timer:
            chunks = self._create_chunks_from_sections(sections)
            timing_stats['chunk_creation'] = timer.duration
        
        timing_stats['total_processing'] = sum(timing_stats.values())
        
        return chunks, timing_stats

    def _build_sections_from_elements(self, elements):
        """Build sections from unstructured elements"""
        sections = []
        current_section = None
        section_counter = 1
        
        for element in elements:
            text = str(element).strip()
            if not text:
                continue
            
            element_type = getattr(element, 'category', 'unknown')
            
            # Simple header detection
            is_header = (
                element_type in ['Title', 'Header'] or
                len(text) < 100 or
                text.isupper() or
                re.match(r'^\d+\.?\d*\s+', text)
            )
            
            if is_header:
                section = {
                    'id': section_counter,
                    'heading': text,
                    'content': [],
                    'level': self._determine_level(text)
                }
                sections.append(section)
                current_section = section
                section_counter += 1
            elif current_section:
                current_section['content'].append(text)
        
        return sections
    
    def _build_sections_from_mistral_content(self, content):
        """Build sections from Mistral OCR content"""
        sections = []
        lines = content.split('\n')
        current_section = None
        section_counter = 1
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Enhanced header detection for OCR content
            is_header = (
                len(line) < 100 or
                line.isupper() or
                re.match(r'^\d+\.?\d*\s+', line) or
                re.match(r'^[A-Z][A-Z\s]+$', line) or
                line.endswith(':') and len(line) < 50
            )
            
            if is_header:
                section = {
                    'id': section_counter,
                    'heading': line,
                    'content': [],
                    'level': self._determine_level(line)
                }
                sections.append(section)
                current_section = section
                section_counter += 1
            elif current_section:
                current_section['content'].append(line)
            else:
                # Create a default section if no header found yet
                section = {
                    'id': section_counter,
                    'heading': 'Document Content',
                    'content': [line],
                    'level': 1
                }
                sections.append(section)
                current_section = section
                section_counter += 1
        
        return sections
    
    def _determine_level(self, text):
        """Determine section level based on text patterns"""
        if re.match(r'^\d+\.\d+\.\d+', text):
            return 3
        elif re.match(r'^\d+\.\d+', text):
            return 2
        elif re.match(r'^\d+', text):
            return 1
        else:
            return 1
    
    def _create_chunks_from_sections(self, sections):
        """Create chunks from sections with optimal sizing"""
        chunks = []
        chunk_id = 1
        
        for section in sections:
            # Combine heading and content
            full_text = section['heading']
            if section['content']:
                full_text += '\n\n' + '\n'.join(section['content'])
            
            # Check if text fits in one chunk
            estimated_tokens = len(full_text.split()) * 1.3  # Rough token estimation
            
            if estimated_tokens <= 300:  # Max tokens per chunk
                chunk = {
                    'id': chunk_id,
                    'content': full_text,
                    'section_id': section['id'],
                    'section_heading': section['heading'],
                    'section_level': section['level'],
                    'estimated_tokens': int(estimated_tokens)
                }
                chunks.append(chunk)
                chunk_id += 1
            else:
                # Split large sections into smaller chunks
                sentences = re.split(r'[.!?]\s+', full_text)
                current_chunk = section['heading'] + '\n\n'
                current_tokens = len(current_chunk.split()) * 1.3
                
                for sentence in sentences:
                    sentence_tokens = len(sentence.split()) * 1.3
                    
                    if current_tokens + sentence_tokens <= 300:
                        current_chunk += sentence + '. '
                        current_tokens += sentence_tokens
                    else:
                        if current_chunk.strip():
                            chunk = {
                                'id': chunk_id,
                                'content': current_chunk.strip(),
                                'section_id': section['id'],
                                'section_heading': section['heading'],
                                'section_level': section['level'],
                                'estimated_tokens': int(current_tokens)
                            }
                            chunks.append(chunk)
                            chunk_id += 1
                        
                        current_chunk = section['heading'] + '\n\n' + sentence + '. '
                        current_tokens = len(current_chunk.split()) * 1.3
                
                # Add final chunk if any content remains
                if current_chunk.strip():
                    chunk = {
                        'id': chunk_id,
                        'content': current_chunk.strip(),
                        'section_id': section['id'],
                        'section_heading': section['heading'],
                        'section_level': section['level'],
                        'estimated_tokens': int(current_tokens)
                    }
                    chunks.append(chunk)
                    chunk_id += 1
        
        return chunks


def analyze_chunks(chunks):
    """Analyze and display chunk statistics"""
    if not chunks:
        print("⚠️  No chunks created!")
        return
    
    print(f"\n📊 Chunk Analysis:")
    print(f"  Total chunks: {len(chunks)}")
    print(f"  Average tokens per chunk: {sum(c.get('estimated_tokens', 0) for c in chunks) / len(chunks):.1f}")
    print(f"  Total characters: {sum(len(c['content']) for c in chunks):,}")
    
    # Show first few chunks
    print(f"\n📝 Sample Chunks:")
    for i, chunk in enumerate(chunks[:3]):
        print(f"  Chunk {i+1}: {chunk['content'][:100]}...")


if __name__ == "__main__":
    print("🚀 Starting Mistral OCR document processing...")
    
    try:
        pdf_path = "policy.pdf"
        chunker = MistralOptimizedSectionAwareChunker()
        
        # Process the document
        chunks, timing_stats = chunker.process_document_optimized(pdf_path)
        
        # Analyze results
        analyze_chunks(chunks)
        
        print(f"\n⏱️  Performance Summary:")
        print(f"  Mistral OCR extraction: {timing_stats.get('mistral_ocr_extraction', 0):.2f}s")
        print(f"  Section identification: {timing_stats['section_identification']:.2f}s")
        print(f"  Chunk creation: {timing_stats['chunk_creation']:.2f}s")
        print(f"  Total processing time: {timing_stats['total_processing']:.2f}s")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
