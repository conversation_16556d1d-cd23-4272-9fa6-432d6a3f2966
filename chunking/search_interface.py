#!/usr/bin/env python3
"""
Search Interface for Topic-Wise Vector Database
Interactive search and retrieval system
"""

import os
import json
import faiss
import numpy as np
from typing import List, Dict, Any
from embeddings import get_query_embedding

class PolicySearchEngine:
    """
    Search engine for section-aware policy document vector database
    """
    
    def __init__(self, 
                 index_path: str = "section_aware_faiss_index.idx",
                 metadata_path: str = "section_aware_chunks_metadata.json"):
        self.index_path = index_path
        self.metadata_path = metadata_path
        self.faiss_index = None
        self.chunks = None
        self.load_database()
    
    def load_database(self):
        """Load FAISS index and chunk metadata"""
        try:
            # Load FAISS index
            if os.path.exists(self.index_path):
                self.faiss_index = faiss.read_index(self.index_path)
                print(f"✅ Loaded FAISS index: {self.faiss_index.ntotal} vectors")
            else:
                raise FileNotFoundError(f"FAISS index not found: {self.index_path}")
            
            # Load chunk metadata (section-aware format)
            if os.path.exists(self.metadata_path):
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Extract chunks from section-aware format
                    if 'chunks' in data:
                        self.chunks = data['chunks']
                    else:
                        self.chunks = data  # Fallback for direct chunk format
                print(f"✅ Loaded chunk metadata: {len(self.chunks)} chunks")
            else:
                raise FileNotFoundError(f"Metadata not found: {self.metadata_path}")
                
        except Exception as e:
            print(f"❌ Error loading database: {str(e)}")
            raise
    
    def search(self, query: str, top_k: int = 5, include_metadata: bool = True) -> List[Dict[str, Any]]:
        """
        Search for relevant chunks based on query
        
        Args:
            query: Search query string
            top_k: Number of results to return
            include_metadata: Whether to include detailed metadata
            
        Returns:
            List of search results with chunks and scores
        """
        if not self.faiss_index or not self.chunks:
            raise ValueError("Database not loaded. Run load_database() first.")
        
        # Get query embedding
        query_embedding = get_query_embedding(query)
        query_array = np.array([query_embedding]).astype("float32")
        
        # Search FAISS index
        distances, indices = self.faiss_index.search(query_array, top_k * 2)
        
        # Process results
        results = []
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            if idx < len(self.chunks):
                chunk = self.chunks[idx]
                
                # Calculate similarity score
                similarity_score = 1.0 / (1.0 + distance)
                
                # Handle section-aware chunk format
                title = chunk['metadata']['heading_text'] if 'metadata' in chunk and 'heading_text' in chunk['metadata'] else chunk.get('title', 'Unknown Section')
                text = chunk.get('original_text', chunk.get('text', ''))
                
                result = {
                    'rank': i + 1,
                    'chunk_id': chunk['id'],
                    'title': title,
                    'text': text,
                    'similarity_score': float(similarity_score),
                    'distance': float(distance)
                }
                
                if include_metadata:
                    result['metadata'] = chunk['metadata']
                
                results.append(result)
        
        # Sort by similarity score and return top_k
        results.sort(key=lambda x: x['similarity_score'], reverse=True)
        return results[:top_k]
    
    def search_by_topic(self, topic_keywords: List[str], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Search for chunks by topic keywords
        
        Args:
            topic_keywords: List of topic-related keywords
            top_k: Number of results to return
            
        Returns:
            List of matching chunks
        """
        if not self.chunks:
            raise ValueError("Chunks not loaded.")
        
        # Score chunks based on topic keyword matches
        scored_chunks = []
        
        for chunk in self.chunks:
            score = 0
            text_lower = chunk['text'].lower()
            
            # Get title from metadata for section-aware chunks
            title = chunk['metadata']['heading_text'] if 'metadata' in chunk and 'heading_text' in chunk['metadata'] else chunk.get('title', '')
            title_lower = title.lower()
            
            # Check keywords in text and title
            for keyword in topic_keywords:
                keyword_lower = keyword.lower()
                if keyword_lower in text_lower:
                    score += 1
                if keyword_lower in title_lower:
                    score += 2  # Title matches are more important
                
                # Check in metadata key terms if available
                if 'metadata' in chunk and 'key_terms' in chunk['metadata']:
                    if keyword_lower in [term.lower() for term in chunk['metadata']['key_terms']]:
                        score += 1.5
            
            if score > 0:
                scored_chunks.append({
                    'chunk': chunk,
                    'topic_score': score,
                    'chunk_id': chunk['id'],
                    'title': title
                })
        
        # Sort by score and return top results
        scored_chunks.sort(key=lambda x: x['topic_score'], reverse=True)
        return scored_chunks[:top_k]
    
    def get_chunk_by_id(self, chunk_id: int) -> Dict[str, Any]:
        """Get specific chunk by ID"""
        if not self.chunks:
            raise ValueError("Chunks not loaded.")
        
        for chunk in self.chunks:
            if chunk['id'] == chunk_id:
                return chunk
        
        raise ValueError(f"Chunk with ID {chunk_id} not found.")
    
    def get_chunks_by_section(self, section_title: str) -> List[Dict[str, Any]]:
        """Get all chunks from a specific section"""
        if not self.chunks:
            raise ValueError("Chunks not loaded.")
        
        matching_chunks = []
        for chunk in self.chunks:
            # Get title from metadata for section-aware chunks
            title = chunk['metadata']['heading_text'] if 'metadata' in chunk and 'heading_text' in chunk['metadata'] else chunk.get('title', '')
            if section_title.lower() in title.lower():
                matching_chunks.append(chunk)
        
        return matching_chunks
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        if not self.chunks:
            return {}
        
        # Load full statistics if available (section-aware format)
        stats_path = "section_aware_statistics.json"
        if os.path.exists(stats_path):
            with open(stats_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # Generate basic stats for section-aware chunks
        chunk_lengths = [len(chunk['text']) for chunk in self.chunks]
        topics = []
        for chunk in self.chunks:
            title = chunk['metadata']['heading_text'] if 'metadata' in chunk and 'heading_text' in chunk['metadata'] else chunk.get('title', 'Unknown')
            topics.append(title)
        
        from collections import Counter
        topic_counts = Counter(topics)
        
        return {
            'total_chunks': len(self.chunks),
            'avg_chunk_length': np.mean(chunk_lengths),
            'unique_topics': len(topic_counts),
            'top_topics': dict(topic_counts.most_common(5))
        }

def interactive_search():
    """Interactive search interface"""
    print("🔍 Policy Document Search Interface")
    print("=" * 40)
    
    try:
        # Initialize search engine
        search_engine = PolicySearchEngine()
        
        print("\nDatabase loaded successfully!")
        stats = search_engine.get_database_stats()
        print(f"📊 Database contains {stats.get('total_chunks', 0)} chunks covering {stats.get('unique_topics', 0)} topics")
        
        while True:
            print("\n" + "-" * 40)
            print("Search Options:")
            print("1. Semantic search (enter query)")
            print("2. Topic search (enter keywords)")
            print("3. Browse by section")
            print("4. Show database stats")
            print("5. Exit")
            
            choice = input("\nEnter your choice (1-5) or search query: ").strip()
            
            if choice == "5" or choice.lower() in ["exit", "quit"]:
                print("👋 Goodbye!")
                break
            elif choice == "4":
                display_stats(search_engine)
            elif choice == "3":
                browse_sections(search_engine)
            elif choice == "2":
                topic_search(search_engine)
            elif choice == "1":
                query = input("Enter your search query: ").strip()
                if query:
                    semantic_search(search_engine, query)
            else:
                # Treat as direct search query
                if choice:
                    semantic_search(search_engine, choice)
                else:
                    print("Please enter a valid choice or search query.")
    
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def semantic_search(search_engine: PolicySearchEngine, query: str):
    """Perform semantic search and display results"""
    print(f"\n🔍 Searching for: '{query}'")
    print("-" * 30)
    
    try:
        results = search_engine.search(query, top_k=5)
        
        if not results:
            print("No results found.")
            return
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['title']}")
            print(f"   Similarity: {result['similarity_score']:.3f}")
            print(f"   Preview: {result['text'][:200]}...")
            
            if 'metadata' in result:
                metadata = result['metadata']
                if 'key_terms' in metadata:
                    print(f"   Key terms: {', '.join(metadata['key_terms'][:5])}")
                if 'page_numbers' in metadata and metadata['page_numbers']:
                    pages = [str(p) for p in metadata['page_numbers'] if p]
                    if pages:
                        print(f"   Pages: {', '.join(pages)}")
    
    except Exception as e:
        print(f"❌ Search error: {str(e)}")

def topic_search(search_engine: PolicySearchEngine):
    """Perform topic-based search"""
    keywords_input = input("Enter topic keywords (comma-separated): ").strip()
    if not keywords_input:
        return
    
    keywords = [kw.strip() for kw in keywords_input.split(',')]
    print(f"\n🎯 Searching for topics: {', '.join(keywords)}")
    print("-" * 30)
    
    try:
        results = search_engine.search_by_topic(keywords, top_k=5)
        
        if not results:
            print("No matching topics found.")
            return
        
        for i, result in enumerate(results, 1):
            chunk = result['chunk']
            print(f"\n{i}. {chunk['title']}")
            print(f"   Topic score: {result['topic_score']}")
            print(f"   Preview: {chunk['text'][:200]}...")
    
    except Exception as e:
        print(f"❌ Topic search error: {str(e)}")

def browse_sections(search_engine: PolicySearchEngine):
    """Browse chunks by section"""
    if not search_engine.chunks:
        print("No chunks available.")
        return
    
    # Get unique section titles
    sections = list(set([chunk['title'] for chunk in search_engine.chunks]))
    sections.sort()
    
    print(f"\n📚 Available sections ({len(sections)}):")
    for i, section in enumerate(sections[:10], 1):  # Show first 10
        print(f"{i:2d}. {section}")
    
    if len(sections) > 10:
        print(f"    ... and {len(sections) - 10} more sections")
    
    try:
        choice = input(f"\nEnter section number (1-{min(10, len(sections))}) or section name: ").strip()
        
        if choice.isdigit():
            idx = int(choice) - 1
            if 0 <= idx < min(10, len(sections)):
                section_title = sections[idx]
            else:
                print("Invalid section number.")
                return
        else:
            section_title = choice
        
        chunks = search_engine.get_chunks_by_section(section_title)
        
        if chunks:
            print(f"\n📄 Chunks in section '{section_title}' ({len(chunks)}):")
            for i, chunk in enumerate(chunks[:3], 1):  # Show first 3
                print(f"\n{i}. Preview: {chunk['text'][:150]}...")
        else:
            print(f"No chunks found for section '{section_title}'")
    
    except ValueError:
        print("Invalid input.")
    except Exception as e:
        print(f"❌ Browse error: {str(e)}")

def display_stats(search_engine: PolicySearchEngine):
    """Display database statistics"""
    print("\n📊 Database Statistics")
    print("-" * 25)
    
    try:
        stats = search_engine.get_database_stats()
        
        print(f"Total chunks: {stats.get('total_chunks', 0)}")
        print(f"Average chunk length: {stats.get('avg_chunk_length', 0):.0f} characters")
        print(f"Unique topics: {stats.get('unique_topics', 0)}")
        
        if 'top_topics' in stats:
            print(f"\nTop topics:")
            for topic, count in stats['top_topics'].items():
                topic_short = topic[:50] + "..." if len(topic) > 50 else topic
                print(f"  • {topic_short}: {count} chunks")
        
        if 'processing_info' in stats:
            info = stats['processing_info']
            print(f"\nProcessing info:")
            print(f"  • Method: {info.get('chunking_method', 'unknown')}")
            print(f"  • Processed: {info.get('timestamp', 'unknown')}")
    
    except Exception as e:
        print(f"❌ Stats error: {str(e)}")

if __name__ == "__main__":
    interactive_search()