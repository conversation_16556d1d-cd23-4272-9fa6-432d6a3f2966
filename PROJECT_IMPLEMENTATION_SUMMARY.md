# Project Implementation Summary: LLM-Powered Intelligent Query-Retrieval System

## Overview
This project implements an advanced, self-correcting, multi-query Retrieval-Augmented Generation (RAG) system for intelligent document processing and contextual question answering. It is designed for domains such as insurance, legal, HR, and compliance, and supports PDF, DOCX, and email document types. The system leverages Azure OpenAI (GPT-4.1), Cohere reranker, FAISS vector search, semantic chunking, and a robust background job and webhook system.

---

## High-Level Architecture
- **API Layer**: FastAPI-based, with endpoints for synchronous queries, webhook integration, job submission, monitoring, and admin operations.
- **Advanced Query Pipeline**: Orchestrates query decomposition, parallel sub-query processing, gap analysis, conditional retry, context aggregation, and answer synthesis.
- **Document Processing**: Handles downloading, OCR, and semantic chunking of documents.
- **Embedding & Vector Store**: Uses Azure OpenAI for embeddings and FAISS for similarity search.
- **Reranking**: Cohere reranker (Azure) for context relevance.
- **Background Job System**: Multiprocessing-enabled, with job queue, status tracking, and webhook notifications.
- **Caching**: LRU caches for documents, decompositions, gap analyses, and models.
- **Logging & Monitoring**: Structured logging, request/response tracking, and system health endpoints.

---

## Key Components & Their Roles

### 1. API Layer (`src/api/`)
- **routes.py**: Defines all API endpoints (query, webhook, jobs, stats, admin, health). Handles authentication, logging, and error handling.
- **middleware.py**: Implements authentication (Bearer token), request/response logging, and error formatting.

### 2. Core Pipeline (`src/core/`)
- **query_engine.py**: Orchestrates the basic retrieval pipeline: document processing, embedding, vector search, reranking, and answer generation.

### 3. Models (`src/models/`)
- **schemas.py**: Pydantic models for API requests/responses, document chunks, retrieval results, sub-queries, gap analysis, retry, and context aggregation.
- **job_models.py**: Models for job management, status, priority, webhook delivery, and metrics.

### 4. Services (`src/services/`)
- **advanced_query_processor.py**: Main orchestrator for the advanced RAG pipeline. Handles query decomposition, parallel sub-query processing, gap analysis, retry, context aggregation, and answer synthesis. Logs detailed pipeline metrics and supports fallback processing.
- **background_job_processor.py**: Manages background job queue, worker pools, job execution, retries, and webhook notifications. Supports job status, cancellation, and statistics.
- **chunking_service.py**: Semantic chunking of documents using Sentence Transformers, with multiprocessing support and fallback chunking.
- **cohere_reranker_service.py**: Integrates Azure Cohere reranker for context reranking, with parallel processing, dynamic thresholding, and connection pooling.
- **conditional_retry_service.py**: Manages single-iteration retry for gap filling, generating gap-filler queries using LLM, and executing additional retrieval.
- **context_aggregation_service.py**: Aggregates and deduplicates context from initial and gap-filler sources, with configurable formatting and performance tracking.
- **gap_analysis_service.py**: Uses GPT-4.1 to analyze if retrieved context is sufficient to answer questions, with caching and batch analysis.
- **query_decomposition_service.py**: Decomposes complex questions into atomic sub-queries using GPT-4.1, with caching and quality validation.
- **parallel_subquery_processor.py**: Orchestrates parallel execution of sub-queries through the RAG pipeline, with Cohere reranking and load balancing.
- **document_processor.py**: Downloads, extracts, and chunks documents (PDF, DOCX, TXT, images), with OCR fallback and multiprocessing.
- **embedding_service.py**: Generates embeddings for text/chunks using Azure OpenAI, with batch and parallel support.
- **vector_store.py**: Manages FAISS-based vector index for similarity search, with async and parallel search support.
- **Other services**: Include model caching, logging, job storage, webhook delivery, and maintenance.

### 5. Configuration (`src/config.py`)
- Centralizes all settings: API, Azure OpenAI, Cohere, FAISS, chunking, caching, job processing, logging, and more. Uses Pydantic for environment management.

### 6. Main Application (`src/main.py`)
- FastAPI app entry point. Sets up middleware, routes, logging, and manages application lifespan (startup/shutdown of services, model loading, job processor, etc).

---

## Pipeline Flow (Advanced RAG)
1. **Query Decomposition**: Breaks complex questions into atomic sub-queries (max 3) using GPT-4.1.
2. **Parallel Sub-Query Processing**: Each sub-query is processed in parallel: embedding, vector search, Cohere reranking.
3. **Gap Analysis**: For each question, GPT-4.1 checks if retrieved context is sufficient; identifies missing info.
4. **Conditional Retry**: If context is insufficient, generates gap-filler queries and retrieves additional context (max 1 retry).
5. **Context Aggregation**: Combines and deduplicates all context chunks, formats for LLM.
6. **Answer Synthesis**: LLM generates final answers using aggregated context and domain-specific prompts.

---

## Background Job & Webhook System
- **Job Submission**: Clients can submit jobs for async processing, with webhook callback on completion.
- **Job Management**: Status, cancellation, listing, and statistics endpoints.
- **Webhook Delivery**: Robust delivery with retries, HMAC signatures, and delivery tracking.

---

## Caching & Optimization
- **Document Cache**: LRU cache for processed documents and FAISS indices.
- **Decomposition & Gap Analysis Cache**: LRU caches for repeated questions/contexts.
- **Model Cache**: Application-level cache for loaded models (LLMs, transformers).
- **Multiprocessing**: Used for chunking, embedding, document processing, and job execution.

---

## Monitoring & Admin
- **Health Checks**: Endpoints for system, pipeline, job processor, cache, workers, logs, and webhooks.
- **Admin Endpoints**: Cache clearing, pipeline configuration, benchmarking, and expired job cleanup.
- **Logging**: Structured logs for requests, responses, errors, and pipeline metrics.

---

## Extensibility & Domain Adaptation
- **Domain Prompts**: System prompts and context formatting are tailored for insurance, legal, HR, and compliance.
- **Pluggable Components**: Modular design allows easy extension or replacement of chunking, embedding, reranking, and LLM services.

---

## Summary Table
| Component                | Purpose/Role                                                                 |
|--------------------------|------------------------------------------------------------------------------|
| API Layer                | Endpoints, authentication, logging, error handling                            |
| Advanced Query Processor | Orchestrates full RAG pipeline, fallback, metrics                             |
| Document Processor       | Download, extract, OCR, chunk documents                                       |
| Embedding Service        | Generate embeddings using Azure OpenAI                                        |
| Vector Store             | FAISS-based similarity search                                                 |
| Cohere Reranker          | Rerank context for relevance                                                  |
| Gap Analysis             | LLM-based context sufficiency check                                           |
| Conditional Retry        | Gap-filler query generation and retrieval                                     |
| Context Aggregation      | Combine, deduplicate, and format context                                      |
| Background Job System    | Async job queue, worker pool, webhook notification                            |
| Caching                  | LRU caches for documents, decompositions, gap analyses, models                |
| Monitoring/Admin         | Health checks, stats, admin endpoints, structured logging                     |

---

## Conclusion
This project delivers a production-grade, highly modular, and extensible LLM-powered document question-answering system with advanced retrieval, self-correction, and robust operational features. It is optimized for speed, reliability, and domain-specific accuracy, and is ready for further adaptation to new domains or integration with additional AI services.
