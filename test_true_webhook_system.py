#!/usr/bin/env python3
"""
Comprehensive test script for the true webhook system.
"""

import asyncio
import json
import sys
import time
from pathlib import Path
from typing import Dict, Any

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import requests
from src.config import settings


class WebhookTestServer:
    """Simple webhook test server to receive notifications."""
    
    def __init__(self):
        self.received_webhooks = []
        self.server_url = "http://localhost:8001/webhook"
    
    def start_server(self):
        """Start a simple webhook receiver server."""
        from http.server import HTTPServer, BaseHTTPRequestHandler
        import threading
        import json
        
        class WebhookHandler(BaseHTTPRequestHandler):
            def do_POST(self):
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                
                try:
                    webhook_data = json.loads(post_data.decode('utf-8'))
                    self.server.webhook_receiver.received_webhooks.append({
                        'data': webhook_data,
                        'headers': dict(self.headers),
                        'timestamp': time.time()
                    })
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(b'{"status": "received"}')
                    
                except Exception as e:
                    self.send_response(400)
                    self.end_headers()
                    self.wfile.write(f'{{"error": "{str(e)}"}}'.encode())
            
            def log_message(self, format, *args):
                pass  # Suppress logs
        
        server = HTTPServer(('localhost', 8001), WebhookHandler)
        server.webhook_receiver = self
        
        def run_server():
            server.serve_forever()
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        time.sleep(1)  # Give server time to start
        
        print(f"✅ Webhook test server started at {self.server_url}")


def test_job_submission():
    """Test job submission API."""
    print("🧪 Testing job submission...")
    
    webhook_server = WebhookTestServer()
    webhook_server.start_server()
    
    try:
        # Submit a job
        job_payload = {
            "documents": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
            "questions": [
                "What is the main topic of this document?",
                "What are the key points mentioned?"
            ],
            "webhook_url": webhook_server.server_url,
            "priority": 1,
            "metadata": {"test": "true"}
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/jobs",
            json=job_payload,
            headers={"Authorization": f"Bearer {settings.BEARER_TOKEN}"},
            timeout=10
        )
        
        if response.status_code == 200:
            job_data = response.json()
            print(f"✅ Job submitted successfully: {job_data['job_id']}")
            print(f"   Status: {job_data['status']}")
            print(f"   Webhook URL: {job_data['webhook_url']}")
            return job_data['job_id']
        else:
            print(f"❌ Job submission failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing job submission: {e}")
        return None


def test_job_status(job_id: str):
    """Test job status API."""
    print(f"🧪 Testing job status for {job_id}...")
    
    try:
        response = requests.get(
            f"http://localhost:8000/api/v1/jobs/{job_id}",
            headers={"Authorization": f"Bearer {settings.BEARER_TOKEN}"},
            timeout=10
        )
        
        if response.status_code == 200:
            status_data = response.json()
            print(f"✅ Job status retrieved successfully")
            print(f"   Status: {status_data['status']}")
            print(f"   Progress: {status_data.get('progress', 0)}%")
            print(f"   Created: {status_data['created_at']}")
            return status_data
        else:
            print(f"❌ Job status failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing job status: {e}")
        return None


def test_job_list():
    """Test job listing API."""
    print("🧪 Testing job listing...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/jobs?limit=10",
            headers={"Authorization": f"Bearer {settings.BEARER_TOKEN}"},
            timeout=10
        )
        
        if response.status_code == 200:
            jobs_data = response.json()
            print(f"✅ Job listing successful")
            print(f"   Found {jobs_data['count']} jobs")
            return True
        else:
            print(f"❌ Job listing failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing job listing: {e}")
        return False


def test_job_stats():
    """Test job statistics API."""
    print("🧪 Testing job statistics...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/stats/jobs",
            headers={"Authorization": f"Bearer {settings.BEARER_TOKEN}"},
            timeout=10
        )
        
        if response.status_code == 200:
            stats_data = response.json()
            print(f"✅ Job statistics retrieved successfully")
            print(f"   Max workers: {stats_data.get('max_workers')}")
            print(f"   Active workers: {stats_data.get('active_workers')}")
            print(f"   Total queue size: {stats_data.get('total_queue_size')}")
            return True
        else:
            print(f"❌ Job statistics failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing job statistics: {e}")
        return False


def test_webhook_stats():
    """Test webhook statistics API."""
    print("🧪 Testing webhook statistics...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/stats/webhooks",
            headers={"Authorization": f"Bearer {settings.BEARER_TOKEN}"},
            timeout=10
        )
        
        if response.status_code == 200:
            stats_data = response.json()
            print(f"✅ Webhook statistics retrieved successfully")
            print(f"   Delivered: {stats_data.get('delivered_count', 0)}")
            print(f"   Failed: {stats_data.get('failed_count', 0)}")
            print(f"   Pending: {stats_data.get('pending_count', 0)}")
            return True
        else:
            print(f"❌ Webhook statistics failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing webhook statistics: {e}")
        return False


def test_system_monitor():
    """Test system monitoring API."""
    print("🧪 Testing system monitoring...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/monitor/system",
            headers={"Authorization": f"Bearer {settings.BEARER_TOKEN}"},
            timeout=10
        )
        
        if response.status_code == 200:
            monitor_data = response.json()
            print(f"✅ System monitoring successful")
            print(f"   System status: {monitor_data.get('system_status')}")
            print(f"   Job processing: {monitor_data.get('job_processing', {}).get('is_running')}")
            return True
        else:
            print(f"❌ System monitoring failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing system monitoring: {e}")
        return False


def test_health_check():
    """Test health check includes job processor."""
    print("🧪 Testing health check...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/health",
            timeout=10
        )
        
        if response.status_code == 200:
            health_data = response.json()
            job_processor_status = health_data.get("job_processor", {}).get("status")
            
            print(f"✅ Health check successful")
            print(f"   Overall status: {health_data.get('enhanced_features')}")
            print(f"   Job processor: {job_processor_status}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing health check: {e}")
        return False


def wait_for_job_completion(job_id: str, timeout: int = 120) -> bool:
    """Wait for job to complete and check for webhook delivery."""
    print(f"⏳ Waiting for job {job_id} to complete (timeout: {timeout}s)...")
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            status_data = test_job_status(job_id)
            if status_data:
                status = status_data.get('status')
                if status in ['completed', 'failed']:
                    print(f"✅ Job {job_id} finished with status: {status}")
                    if status == 'completed':
                        print(f"   Results: {len(status_data.get('results', {}).get('answers', []))} answers")
                    return status == 'completed'
            
            time.sleep(5)  # Check every 5 seconds
            
        except Exception as e:
            print(f"❌ Error checking job status: {e}")
            time.sleep(5)
    
    print(f"⏰ Job {job_id} did not complete within {timeout} seconds")
    return False


def main():
    """Run all webhook system tests."""
    print("🚀 Starting True Webhook System Tests...")
    print(f"Testing against: http://localhost:8000")
    print("-" * 60)
    
    tests = [
        test_health_check,
        test_job_stats,
        test_webhook_stats,
        test_system_monitor,
        test_job_list,
    ]
    
    passed = 0
    total = len(tests)
    
    # Run basic tests first
    for test in tests:
        if test():
            passed += 1
        print()
    
    # Test job submission and processing
    print("🔄 Testing job processing flow...")
    job_id = test_job_submission()
    
    if job_id:
        passed += 1
        print()
        
        # Wait for job completion
        if wait_for_job_completion(job_id, timeout=180):
            passed += 1
            print("✅ Job processing completed successfully")
        else:
            print("❌ Job processing did not complete in time")
        
        total += 2  # Add job submission and completion tests
    else:
        total += 2
        print("❌ Job submission failed, skipping completion test")
    
    print()
    print("-" * 60)
    print(f"Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All webhook system tests passed!")
        return 0
    else:
        print("❌ Some webhook system tests failed")
        return 1


if __name__ == "__main__":
    exit(main())