import os
from openai import AzureOpenAI
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

# Read config from environment
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
api_key = os.getenv("AZURE_OPENAI_API_KEY")
api_version = os.getenv("AZURE_OPENAI_API_VERSION")
deployment = os.getenv("AZURE_OPENAI_EMBEDDING_MODEL")

# Initialize Azure OpenAI client
client = AzureOpenAI(
    api_version=api_version,
    azure_endpoint=endpoint,
    api_key=api_key,
)

def embedding(chunks, deployment=deployment):
    response = client.embeddings.create(
        input=chunks,
        model=deployment
    )

    embeddings = [item.embedding for item in response.data]

    # Optional: Debug info
    for item in response.data:
        length = len(item.embedding)
        print(
            f"data[{item.index}]: length={length}, "
            f"[{item.embedding[0]}, {item.embedding[1]}, ..., {item.embedding[length-2]}, {item.embedding[length-1]}]"
        )
    print("📊 Token usage:", response.usage)

    return embeddings

def get_query_embedding(query, deployment=deployment):
    return embedding([query], deployment)[0]