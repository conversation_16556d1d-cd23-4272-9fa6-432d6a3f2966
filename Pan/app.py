from fastapi import FastAPI, UploadFile, File
from pydantic import BaseModel
from chunking import chunk
from embeddings import embedding, get_query_embedding
from vector import build_faiss_index, load_faiss_index, search_faiss_index
from database import store_chunks_in_supabase, get_chunks_by_indices
from pipeline import query_llm

app = FastAPI()

class QueryRequest(BaseModel):
    query: str

@app.post("/rag/query")
def rag_query(payload: QueryRequest):
    # Load document from sample.txt (or change this to a DB or upload-based read)
    with open("sample.txt", "r", encoding="utf-8") as f:
        raw_text = f.read()

    # Step 1: Chunk the document
    chunks = chunk(raw_text)

    # Step 2: Embeddings
    embeddings = embedding(chunks)

    # Step 3: Store in Supabase
    store_chunks_in_supabase(chunks)

    # Step 4: Build vector store
    build_faiss_index(embeddings)

    # Step 5: Handle Query
    query = payload.query
    query_vector = get_query_embedding(query)

    index = load_faiss_index()
    matched_indices = search_faiss_index(index, query_vector)

    matched_chunks = get_chunks_by_indices(matched_indices)
    context = "\n\n".join(matched_chunks)

    # Step 6: Call LLM
    answer = query_llm(query, context)

    return {
        "question": query,
        "answer": answer,
        "matched_chunks": matched_chunks
    }
