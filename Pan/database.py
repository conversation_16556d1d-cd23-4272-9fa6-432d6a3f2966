import os
from supabase import create_client
from dotenv import load_dotenv

load_dotenv()

supabase = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_KEY"))

def store_chunks_in_supabase(chunks):
    for i, chunk in enumerate(chunks):
        supabase.table("chunks").insert({
            "chunk_index": i,
            "content": chunk,
            "source": "sample.txt",
            "faiss_index": i ,
        }).execute()

def get_chunks_by_indices(indices):
    retrieved = []
    for idx in indices:
        result = supabase.table("chunks").select("*").eq("faiss_index", idx).execute()
        if result.data:
            retrieved.append(result.data[0]["content"])
    return retrieved