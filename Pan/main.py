from chunking import chunk
from embeddings import embedding, get_query_embedding
from vector import build_faiss_index, load_faiss_index, search_faiss_index
from database import store_chunks_in_supabase, get_chunks_by_indices
from pipeline import query_llm

def main():
    # Step 1: Load document
    with open("sample.txt", "r", encoding="utf-8") as f:
        raw_text = f.read()

    # Step 2: Chunk the document
    print("🔍 Chunking document...")
    chunks = chunk(raw_text)

    # Step 3: Generate embeddings
    print("📐 Generating embeddings...")
    embeddings = embedding(chunks)

    # Step 4: Store metadata in Supabase
    print("📤 Storing metadata...")
    store_chunks_in_supabase(chunks)

    # Step 5: Store embeddings in FAISS
    print("💾 Building vector store...")
    build_faiss_index(embeddings)

    # Step 6: Accept a query
    query = input("❓ Ask a question: ")
    query_embedding = get_query_embedding(query)

    # Step 7: Retrieve matching chunks
    print("🔎 Searching vector store...")
    index = load_faiss_index()
    matched_indices = search_faiss_index(index, query_embedding)

    print("🗃️ Fetching context from DB...")
    matched_chunks = get_chunks_by_indices(matched_indices)
    context = "\n\n".join(matched_chunks)

    # Step 8: Get final answer from LLM
    print("🤖 Querying LLM...")
    answer = query_llm(query, context)

    print(f"\n💬 Answer:\n{answer}")

if __name__ == "__main__":
    main()