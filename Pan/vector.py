import faiss
import numpy as np

def build_faiss_index(embeddings, index_path="faiss_index.idx"):
    dim = len(embeddings[0])
    index = faiss.IndexFlatL2(dim)
    index.add(np.array(embeddings).astype("float32"))
    faiss.write_index(index, index_path)
    return index

def load_faiss_index(index_path="faiss_index.idx"):
    return faiss.read_index(index_path)

def search_faiss_index(index, query_embedding, top_k=5):
    D, I = index.search(np.array([query_embedding]).astype("float32"), top_k)
    return I[0]