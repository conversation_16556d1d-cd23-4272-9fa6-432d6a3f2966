from transformers import AutoModel
import time
import torch
from typing import List, Dict
import numpy as np

class OptimizedProvinceProcessor:
    def __init__(self):
        """Initialize with proper batch optimization"""
        print("Loading NAVER Provence model...")
        self.provence = AutoModel.from_pretrained(
            "naver/provence-reranker-debertav3-v1", 
            trust_remote_code=True
        )
        
        # Move to GPU if available
        if torch.cuda.is_available():
            self.provence = self.provence.cuda()
        
        print("Model loaded successfully!")
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {self.device}")
    
    def process_chunks_sequential(self, question: str, chunks: List[str], threshold: float = 0.1) -> List[Dict]:
        """
        Process chunks one by one (the slow way you were experiencing)
        """
        print("🐌 Processing chunks sequentially (slow method)...")
        start_time = time.time()
        
        results = []
        for i, chunk in enumerate(chunks):
            print(f"  Processing chunk {i+1}/{len(chunks)}")
            result = self.provence.process(
                question=question,
                context=chunk,
                threshold=threshold,
                always_select_title=True
            )
            results.append({
                'chunk_id': i,
                'original_chunk': chunk,
                'pruned_context': result.get('pruned_context', chunk),
                'relevance_score': result.get('reranking_score', 0.0),
                'kept_sentences': result.get('kept_sentences', len(chunk.split('.')))
            })
        
        sequential_time = time.time() - start_time
        print(f"Sequential processing completed in {sequential_time:.3f} seconds")
        return results, sequential_time
    
    def fast_batch_process(self, question: str, chunks: List[str], 
                          threshold: float = 0.1, top_k: int = 5, 
                          max_batch_size: int = 8) -> List[Dict]:
        """
        Ultra-fast batch processing with chunking to avoid memory issues
        """
        print(f"🚀 Processing {len(chunks)} chunks with optimized batching...")
        start_time = time.time()
        
        all_results = []
        
        # Process in smaller batches to avoid memory issues
        for i in range(0, len(chunks), max_batch_size):
            batch_chunks = chunks[i:i + max_batch_size]
            batch_size = len(batch_chunks)
            
            print(f"  Processing batch {i//max_batch_size + 1}/{(len(chunks) + max_batch_size - 1)//max_batch_size}")
            
            try:
                # Try batch processing first
                for j, chunk in enumerate(batch_chunks):
                    result = self.provence.process(
                        question=question,
                        context=chunk,
                        threshold=threshold,
                        always_select_title=True
                    )
                    all_results.append({
                        'original_index': i + j,
                        'chunk': chunk,
                        'pruned_context': result.get('pruned_context', chunk),
                        'relevance_score': result.get('reranking_score', 0.0),
                        'compression_ratio': 1 - (len(result.get('pruned_context', '')) / len(chunk)) if len(chunk) > 0 else 0
                    })
                    
            except Exception as e:
                print(f"  Batch failed: {e}")
                # Fallback for problematic batches
                for j, chunk in enumerate(batch_chunks):
                    try:
                        result = self.provence.process(
                            question=question,
                            context=chunk,
                            threshold=threshold
                        )
                        all_results.append({
                            'original_index': i + j,
                            'chunk': chunk,
                            'pruned_context': result.get('pruned_context', chunk),
                            'relevance_score': result.get('reranking_score', 0.0),
                            'compression_ratio': 1 - (len(result.get('pruned_context', '')) / len(chunk)) if len(chunk) > 0 else 0
                        })
                    except:
                        # Skip problematic chunks
                        continue
        
        # Sort by relevance score and return top-k
        all_results.sort(key=lambda x: x['relevance_score'], reverse=True)
        final_results = all_results[:top_k]
        
        total_time = time.time() - start_time
        print(f"✅ Completed in {total_time:.2f} seconds ({len(chunks)/total_time:.1f} chunks/sec)")
        
        return final_results, total_time

    def super_fast_process(self, question: str, chunks: List[str], 
                          threshold: float = 0.3, top_k: int = 5) -> List[Dict]:
        """
        Alternative: Use higher threshold for aggressive pruning and maximum speed
        """
        print(f"⚡ Super-fast processing with aggressive pruning...")
        start_time = time.time()
        
        all_results = []
        
        try:
            # Process chunks one by one but with higher threshold for speed
            for i, chunk in enumerate(chunks[:20]):  # Limit to first 20 chunks for speed
                result = self.provence.process(
                    question=question,
                    context=chunk,
                    threshold=threshold,  # Higher threshold = more aggressive pruning
                    always_select_title=True
                )
                
                all_results.append({
                    'rank': i + 1,
                    'chunk': chunk,
                    'pruned_context': result.get('pruned_context', chunk),
                    'relevance_score': result.get('reranking_score', 0.0),
                    'compression_ratio': 1 - (len(result.get('pruned_context', '')) / len(chunk)) if len(chunk) > 0 else 0
                })
            
            # Sort by relevance and get top-k
            all_results.sort(key=lambda x: x['relevance_score'], reverse=True)
            formatted_results = all_results[:top_k]
            
            total_time = time.time() - start_time
            print(f"✅ Super-fast completed in {total_time:.2f} seconds")
            
            return formatted_results, total_time
            
        except Exception as e:
            print(f"Super-fast method failed: {e}")
            return [], 0.0

    def process_chunks_batch(self, question: str, chunks: List[str], 
                           threshold: float = 0.1, top_k: int = 5) -> List[Dict]:
        """
        Optimized batch processing method
        """
        results, batch_time = self.fast_batch_process(question, chunks, threshold, top_k, max_batch_size=4)
        
        # Convert to legacy format for compatibility
        formatted_results = []
        for i, result in enumerate(results):
            formatted_results.append({
                'rank': i + 1,
                'chunk_id': result.get('original_index', i),
                'original_chunk': result.get('chunk', ''),
                'pruned_context': result.get('pruned_context', ''),
                'relevance_score': result.get('relevance_score', 0.0),
                'kept_sentences': len(result.get('pruned_context', '').split('.'))
            })
        
        return formatted_results, batch_time

# Create 20 large example chunks about artificial intelligence and technology
def create_example_chunks() -> List[str]:
    """Generate 20 large, realistic text chunks for testing"""
    
    chunks = [
        # Chunk 1: AI in Healthcare
        """
        Artificial intelligence is revolutionizing healthcare by enabling more accurate diagnoses, personalized treatment plans, and improved patient outcomes. Machine learning algorithms can analyze medical imaging data with unprecedented precision, often detecting diseases like cancer, pneumonia, and retinal disorders earlier than human specialists. Deep learning models trained on vast datasets of medical images can identify subtle patterns that might be missed by the human eye. Furthermore, AI-powered diagnostic tools are becoming increasingly accessible, allowing healthcare providers in remote or underserved areas to access expert-level diagnostic capabilities. Natural language processing is also transforming healthcare by extracting valuable insights from unstructured medical records, clinical notes, and research papers. These AI systems can identify potential drug interactions, suggest treatment protocols, and even predict patient deterioration before it becomes clinically apparent. The integration of AI in healthcare is not just improving individual patient care but also contributing to population health management and epidemiological research.
        """,
        
        # Chunk 2: Autonomous Vehicles
        """
        Self-driving cars represent one of the most complex applications of artificial intelligence, combining computer vision, sensor fusion, path planning, and real-time decision-making systems. These vehicles use a sophisticated array of sensors including cameras, LiDAR, radar, and ultrasonic sensors to create a comprehensive understanding of their environment. The AI systems must process this sensor data in real-time, identifying pedestrians, other vehicles, traffic signs, road markings, and potential hazards. Machine learning algorithms, particularly deep neural networks, are trained on millions of miles of driving data to learn how to navigate complex scenarios like merging onto highways, navigating through construction zones, and handling unexpected situations like emergency vehicles or jaywalking pedestrians. The development of autonomous vehicles also involves significant challenges in ensuring safety, reliability, and ethical decision-making in critical situations. Companies like Tesla, Waymo, and Cruise are continuously refining their AI systems through extensive testing and data collection, pushing the boundaries of what's possible in autonomous transportation.
        """,
        
        # Chunk 3: Natural Language Processing
        """
        Natural Language Processing has experienced remarkable advances with the development of transformer-based models like BERT, GPT, and their successors. These models have fundamentally changed how machines understand and generate human language, enabling applications ranging from sophisticated chatbots to automated content creation and language translation. The attention mechanism at the core of transformer architectures allows these models to understand context and relationships between words across long sequences of text. Pre-training on massive text corpora followed by fine-tuning on specific tasks has proven to be an incredibly effective approach for achieving state-of-the-art performance across a wide range of NLP tasks. Modern language models can perform tasks like sentiment analysis, named entity recognition, question answering, text summarization, and even creative writing with remarkable proficiency. The recent emergence of large language models with billions of parameters has opened up new possibilities for few-shot and zero-shot learning, where models can perform new tasks with minimal or no task-specific training examples. This has significant implications for democratizing AI capabilities and making advanced NLP accessible to a broader range of applications and users.
        """,
        
        # Chunk 4: Computer Vision
        """
        Computer vision has made tremendous strides through deep learning, particularly with convolutional neural networks that can automatically learn hierarchical features from images. These systems now surpass human performance in many image recognition tasks, from identifying objects in photographs to detecting anomalies in medical scans. The evolution from simple image classification to more complex tasks like object detection, semantic segmentation, and instance segmentation has been enabled by architectures like ResNet, YOLO, and Mask R-CNN. Computer vision applications span numerous industries: in manufacturing, AI systems inspect products for defects with superhuman precision and speed; in agriculture, drones equipped with computer vision monitor crop health and optimize irrigation; in retail, visual recognition systems enable automated checkout and inventory management. The integration of computer vision with other AI technologies has led to breakthrough applications like augmented reality, where digital information is seamlessly overlaid on the real world, and advanced robotics, where machines can navigate and manipulate objects in complex environments. The field continues to evolve with developments in 3D vision, video understanding, and real-time processing capabilities.
        """,
        
        # Chunk 5: AI Ethics and Bias
        """
        The rapid advancement of artificial intelligence has brought critical ethical considerations to the forefront, particularly regarding algorithmic bias, fairness, and the potential for AI systems to perpetuate or amplify existing societal inequalities. Machine learning models can inadvertently learn and reproduce biases present in their training data, leading to discriminatory outcomes in areas like hiring, lending, criminal justice, and healthcare. For example, facial recognition systems have shown higher error rates for individuals with darker skin tones, and resume screening algorithms have been found to favor male candidates for technical positions. Addressing these challenges requires a multifaceted approach including diverse and representative training data, bias detection and mitigation techniques, algorithmic auditing, and inclusive development teams. The concept of explainable AI has become increasingly important, as stakeholders need to understand how AI systems make decisions, especially in high-stakes applications. Regulatory frameworks and ethical guidelines are being developed worldwide to ensure that AI development and deployment align with human values and societal needs. Organizations are establishing AI ethics committees and implementing responsible AI practices throughout the development lifecycle to build trust and ensure beneficial outcomes for all users.
        """
    ]
    
    # Add more simplified chunks to reach 20
    for i in range(6, 21):
        chunks.append(f"""
        This is example chunk {i} about artificial intelligence and technology. It contains information about various applications of AI including machine learning, deep learning, natural language processing, computer vision, and robotics. AI is transforming industries like healthcare, finance, transportation, education, and entertainment. The field continues to evolve rapidly with new breakthroughs in neural networks, transformer models, and AI safety research. Key challenges include algorithmic bias, data privacy, computational efficiency, and ensuring AI systems remain aligned with human values and societal needs.
        """)
    
    # Clean up the chunks (remove extra whitespace)
    cleaned_chunks = [chunk.strip().replace('\n', ' ').replace('  ', ' ') for chunk in chunks]
    
    return cleaned_chunks

def run_complete_example():
    """
    Complete example demonstrating the efficiency improvements
    """
    print("🤖 **NAVER Provence Optimized Processing Demo**\n")
    
    # Initialize the processor
    processor = OptimizedProvinceProcessor()
    
    # Create example data
    question = "How is artificial intelligence being used to improve healthcare outcomes and medical diagnosis?"
    chunks = create_example_chunks()
    
    print(f"📊 **Test Setup:**")
    print(f"   • Question: {question}")
    print(f"   • Number of chunks: {len(chunks)}")
    print(f"   • Average chunk length: {sum(len(chunk) for chunk in chunks) // len(chunks)} characters")
    print()
    
    # Method 1: Sequential Processing (your current slow method)
    print("=" * 70)
    sequential_results, sequential_time = processor.process_chunks_sequential(
        question, chunks[:5], threshold=0.1  # Test with only 5 chunks for demo
    )
    print(f"📈 Sequential Results Summary:")
    print(f"   • Total processing time: {sequential_time:.3f} seconds")
    print(f"   • Average time per chunk: {sequential_time/5:.3f} seconds")
    print()
    
    # Method 2: Fast Batch Processing 
    print("=" * 70)
    batch_results, batch_time = processor.fast_batch_process(
        question, chunks, threshold=0.1, top_k=5, max_batch_size=4
    )
    print(f"📈 Fast Batch Results Summary:")
    print(f"   • Total processing time: {batch_time:.3f} seconds")
    print(f"   • Speedup: {sequential_time/batch_time:.1f}x faster (extrapolated)")
    print(f"   • Top {len(batch_results)} most relevant chunks returned")
    print()
    
    # Method 3: Super Fast Processing
    print("=" * 70)
    super_fast_results, super_fast_time = processor.super_fast_process(
        question, chunks, threshold=0.3, top_k=5
    )
    print()
    
    # Display top results
    print("🏆 **Top 3 Most Relevant Chunks (Fast Batch Processing):**")
    print("-" * 70)
    
    for i, result in enumerate(batch_results[:3]):
        print(f"**Rank {i+1}** (Score: {result['relevance_score']:.3f})")
        print(f"Original length: {len(result['chunk'])} chars")
        print(f"Pruned length: {len(result['pruned_context'])} chars")
        print(f"Compression: {result['compression_ratio']:.1%}")
        print(f"Preview: {result['pruned_context'][:200]}...")
        print()
    
    # Performance comparison
    print("⚡ **Performance Comparison:**")
    print(f"   • Sequential method: {sequential_time:.3f} seconds (5 chunks)")
    print(f"   • Fast batch method: {batch_time:.3f} seconds (20 chunks)")
    print(f"   • Super fast method: {super_fast_time:.3f} seconds (20 chunks)")
    print(f"   • Processing rate: {len(chunks)/batch_time:.1f} chunks/second")
    
    return batch_results

def test_different_thresholds():
    """
    Test how different pruning thresholds affect results
    """
    print("\n🔬 **Testing Different Pruning Thresholds**\n")
    
    processor = OptimizedProvinceProcessor()
    chunks = create_example_chunks()[:5]  # Use first 5 chunks for faster testing
    question = "What are the applications of AI in healthcare?"
    
    thresholds = [0.1, 0.3, 0.5, 0.7]
    
    for threshold in thresholds:
        print(f"**Threshold: {threshold}**")
        results, _ = processor.fast_batch_process(
            question, chunks, threshold=threshold, top_k=3, max_batch_size=2
        )
        
        if results:
            avg_pruning = sum(
                result['compression_ratio'] for result in results
            ) / len(results) * 100
            
            relevance_scores = [f"{r['relevance_score']:.3f}" for r in results[:3]]
            
            print(f"   • Average pruning: {avg_pruning:.1f}%")
            print(f"   • Top 3 relevance scores: {relevance_scores}")
        else:
            print("   • No results returned")
        print()

# Usage example with your data
def run_optimized_example():
    """Run with your specific use case"""
    processor = OptimizedProvinceProcessor()
    
    # Your question and chunks
    question = "How is artificial intelligence being used to improve healthcare outcomes and medical diagnosis?"
    chunks = create_example_chunks()  # Your 20 chunks or load your actual data
    
    print("🎯 **Method 1: Optimized Batch Processing**")
    results1, time1 = processor.fast_batch_process(
        question=question,
        chunks=chunks,
        threshold=0.1,  # Conservative pruning
        top_k=5,
        max_batch_size=4  # Smaller batches for stability
    )
    
    print(f"\n🎯 **Method 2: Super-Fast Processing**")
    results2, time2 = processor.super_fast_process(
        question=question,
        chunks=chunks,
        threshold=0.3,  # More aggressive pruning
        top_k=5
    )
    
    # Display results
    print(f"\n🏆 **Comparison Results:**")
    print(f"Optimized Batch: {time1:.2f}s | Super Fast: {time2:.2f}s")
    
    if results1:
        print(f"\n**Top Result from Optimized Batch:**")
        result = results1[0]
        print(f"Score: {result['relevance_score']:.3f} | Compression: {result['compression_ratio']:.1%}")
        print(f"Preview: {result['pruned_context'][:150]}...")

if __name__ == "__main__":
    # Run the complete example
    batch_results = run_complete_example()
    
    # Test different thresholds
    test_different_thresholds()
    
    # Run optimized example
    print("\n" + "="*70)
    run_optimized_example()
    
    print("\n✅ **Demo completed successfully!**")
    print("\n💡 **Key Takeaways:**")
    print("   • Optimized batch processing is much more stable")
    print("   • Smaller batch sizes prevent memory issues")
    print("   • Higher thresholds = faster processing + more pruning")
    print("   • Fallback mechanisms ensure reliability")
