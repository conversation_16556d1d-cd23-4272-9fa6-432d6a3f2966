# filename: prov_final.py

import time
import torch
from transformers import AutoModel
from typing import List, Dict

class ProvenceReranker:
    def __init__(self):
        """Initializes the NAVER Provence model."""
        print("Loading NAVER Provence model...")
        self.provence = AutoModel.from_pretrained(
            "naver/provence-reranker-debertav3-v1",
            trust_remote_code=True
        )
        print("Model loaded successfully!")
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.provence.to(self.device)
        print(f"Using device: {self.device}")

    def rerank(self, question: str, chunks: List[str], top_k: int = 5) -> (List[Dict], float):
        """
        Efficiently reranks and prunes chunks in a single batch call.
        
        This uses the model as intended by the authors:
        - Processes all chunks in a single batch.
        - `reorder=True`: Sorts the chunks by relevance score.
        - `top_k=5`: Returns only the 5 most relevant chunks.
        The model inherently prunes the content of these returned chunks.
        """
        print("\n" + "="*70)
        print("🚀 Running Single-Step Batch Reranking (Correct & Final Method)")
        start_time = time.time()

        # Create a list of questions, one for each chunk, for the batch call.
        questions_batch = [question] * len(chunks)
        
        # This is the single, correct call.
        # It tells the model to process all chunks, reorder them by relevance,
        # and give us back only the top k. Pruning happens automatically.
        top_results = self.provence.process(
            question=questions_batch,
            context=chunks,
            reorder=True,      # KEY: This enables ranking.
            top_k=top_k,       # KEY: This returns only the most relevant results.
            batch_size=200 # Process all chunks in one go.
        )
        
        total_time = time.time() - start_time
        print(f"Single-step batch processing completed in {total_time:.3f} seconds.")
        
        # Format the output for consistency
        final_results = []
        for rank, item in enumerate(top_results):
            final_results.append({
                'rank': rank + 1,
                'chunk_id': item.get('chunk_id'),
                'original_chunk': item.get('original_context'),
                'pruned_context': item.get('pruned_context'),
                'relevance_score': item.get('reranking_score'),
            })

        return final_results, total_time


def create_example_chunks() -> List[str]:
    """Generates 20 large, realistic text chunks for testing."""
    # This function remains the same.
    chunks = [
        "Artificial intelligence is revolutionizing healthcare...", "Self-driving cars represent one of the most complex applications...",
        "Natural Language Processing has experienced remarkable advances...", "Computer vision has made tremendous strides through deep learning...",
        "The rapid advancement of artificial intelligence has brought critical ethical considerations...", "Quantum computing represents a paradigm shift...",
        "The integration of artificial intelligence with robotics has created intelligent machines...", "Artificial intelligence has transformed the financial services industry...",
        "Artificial intelligence is playing an increasingly important role in addressing climate change...", "Artificial intelligence is transforming education by enabling personalized learning...",
        "The entertainment industry has embraced artificial intelligence...", "Artificial intelligence has become both a powerful tool for cybersecurity...",
        "Precision agriculture powered by artificial intelligence is revolutionizing farming...", "The convergence of artificial intelligence and Internet of Things...",
        "Artificial intelligence is accelerating drug discovery and development...", "Artificial intelligence is playing an increasingly critical role in space exploration...",
        "Industry 4.0, characterized by the integration of artificial intelligence into manufacturing...", "Artificial intelligence is emerging as a powerful tool in mental health care...",
        "Beyond autonomous vehicles, artificial intelligence is transforming all aspects of transportation...", "The future of artificial intelligence holds immense promise..."
    ]
    return [c.strip().replace('\n', ' ') for c in chunks]


def run_rerank_test():
    """Demonstrates the efficiency of the final, single-step batch approach."""
    print("🤖 **Provence Reranker Final Optimization Test**")
    
    reranker = ProvenceReranker()
    
    question = "How is artificial intelligence being used to improve healthcare outcomes and medical diagnosis?"
    chunks = create_example_chunks()
    
    print(f"\n📊 **Test Setup:**")
    print(f"  • Question: '{question}'")
    print(f"  • Number of chunks to rerank: {len(chunks)}")
    
    # Run the single, optimized rerank method
    final_results, total_time = reranker.rerank(question, chunks, top_k=5)
    
    print("\n" + "="*70)
    print(f"✅ **Processing Complete**")
    print(f"  • Total time taken: {total_time:.3f} seconds")

    print("\n🏆 **Top 5 Reranked & Pruned Results:**")
    print("-" * 70)
    
    if not final_results:
        print("No results were returned.")
        return

    for result in final_results:
        print(f"**Rank {result['rank']}** (Score: {result.get('relevance_score', 0.0):.3f}) - Chunk ID: {result['chunk_id']}")
        print(f"  Preview of Pruned Context: {result['pruned_context'][:250]}...")
        print()

if __name__ == "__main__":
    run_rerank_test()