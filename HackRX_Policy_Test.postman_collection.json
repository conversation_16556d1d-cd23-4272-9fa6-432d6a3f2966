{"info": {"_postman_id": "hackrx-policy-test", "name": "HackRX Policy Document Test", "description": "Test collection for HackRX hackathon endpoint with National Parivar Mediclaim Plus Policy", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Server is accessible\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response time is acceptable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "http://**************:8000", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000"}}, "response": []}, {"name": "HackRX Full Policy Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response time is acceptable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(300000); // 5 minutes", "});", "", "pm.test(\"Response has answers field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('answers');", "});", "", "pm.test(\"Answers array has correct length\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.answers).to.be.an('array');", "    pm.expect(jsonData.answers).to.have.lengthOf(10);", "});", "", "pm.test(\"All answers are non-empty strings\", function () {", "    var jsonData = pm.response.json();", "    jsonData.answers.forEach(function(answer, index) {", "        pm.expect(answer, `Answer ${index + 1} should be a non-empty string`).to.be.a('string').that.is.not.empty;", "    });", "});", "", "pm.test(\"Answers contain policy-specific information\", function () {", "    var jsonData = pm.response.json();", "    var answerText = jsonData.answers.join(' ').toLowerCase();", "    pm.expect(answerText).to.include('parivar mediclaim');", "});", "", "// Log results for manual review", "console.log('=== HACKRX TEST RESULTS ===');", "console.log('Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "if (pm.response.status === 200) {", "    var jsonData = pm.response.json();", "    console.log('Answers Count:', jsonData.answers.length);", "    ", "    // Log first 3 answers for review", "    jsonData.answers.slice(0, 3).forEach(function(answer, index) {", "        console.log(`Answer ${index + 1}:`, answer.substring(0, 100) + '...');", "    });", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["console.log('Starting HackRX Policy Test...');", "console.log('Document: National Parivar Mediclaim Plus Policy');", "console.log('Questions: 10 policy-related questions');", "console.log('Expected response time: < 5 minutes');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer 0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"documents\": \"https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D\",\n    \"questions\": [\n        \"What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?\",\n        \"What is the waiting period for pre-existing diseases (PED) to be covered?\",\n        \"Does this policy cover maternity expenses, and what are the conditions?\",\n        \"What is the waiting period for cataract surgery?\",\n        \"Are the medical expenses for an organ donor covered under this policy?\",\n        \"What is the No Claim Discount (NCD) offered in this policy?\",\n        \"Is there a benefit for preventive health check-ups?\",\n        \"How does the policy define a 'Hospital'?\",\n        \"What is the extent of coverage for AYUSH treatments?\",\n        \"Are there any sub-limits on room rent and ICU charges for Plan A?\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**************:8000/hackrx/run", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000", "path": ["hackrx", "run"]}}, "response": []}, {"name": "HackRX Quick Test (3 Questions)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response time is acceptable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(120000); // 2 minutes", "});", "", "pm.test(\"Response has answers field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('answers');", "});", "", "pm.test(\"Answers array has correct length\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.answers).to.be.an('array');", "    pm.expect(jsonData.answers).to.have.lengthOf(3);", "});", "", "// Log results", "console.log('=== QUICK TEST RESULTS ===');", "console.log('Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "if (pm.response.status === 200) {", "    var jsonData = pm.response.json();", "    console.log('Answers Count:', jsonData.answers.length);", "    jsonData.answers.forEach(function(answer, index) {", "        console.log(`Answer ${index + 1}:`, answer.substring(0, 150) + '...');", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer 0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"documents\": \"https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D\",\n    \"questions\": [\n        \"What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?\",\n        \"What is the waiting period for pre-existing diseases (PED) to be covered?\",\n        \"Does this policy cover maternity expenses, and what are the conditions?\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**************:8000/hackrx/run", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000", "path": ["hackrx", "run"]}}, "response": []}, {"name": "Error Test - <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Authentication error is handled correctly\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([401, 403]);", "});", "", "console.log('Error Test - Invalid Token');", "console.log('Status:', pm.response.status);", "console.log('Response:', pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer invalid_token_12345", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"documents\": \"https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D\",\n    \"questions\": [\n        \"What is the grace period for premium payment?\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**************:8000/hackrx/run", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000", "path": ["hackrx", "run"]}}, "response": []}, {"name": "Error Test - Missing Questions", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validation error is handled correctly\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 422]);", "});", "", "console.log('Error Test - Missing Questions');", "console.log('Status:', pm.response.status);", "console.log('Response:', pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer 0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"documents\": \"https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D\",\n    \"questions\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**************:8000/hackrx/run", "protocol": "http", "host": ["140", "238", "227", "29"], "port": "8000", "path": ["hackrx", "run"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('HackRX Collection - Test Starting');", "console.log('Target: http://**************:8000/hackrx/run');", "console.log('Document: National Parivar Mediclaim Plus Policy');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.globals.set('test_timestamp', new Date().toISOString());"]}}], "variable": [{"key": "base_url", "value": "http://**************:8000", "type": "string"}, {"key": "bearer_token", "value": "0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378", "type": "string"}, {"key": "document_url", "value": "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D", "type": "string"}]}