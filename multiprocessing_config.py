"""Multiprocessing configuration for the LLM Query-Retrieval System."""

import multiprocessing as mp


class MultiprocessingConfig:
    """Configuration for multiprocessing settings."""
    
    def __init__(self):
        # Worker pool configurations
        self.max_workers = min(32, (mp.cpu_count() or 1) + 4)
        self.thread_pool_size = min(64, (mp.cpu_count() or 1) * 4)
        
        # Service-specific configurations
        self.llm_workers = min(8, self.max_workers // 2)
        self.embedding_workers = min(16, self.max_workers)
        self.document_workers = min(4, self.max_workers // 4)
        self.ocr_workers = min(6, self.max_workers // 2)
        
        # Performance settings - optimized for typical workloads
        self.chunk_size = 20  # Smaller batches for better memory management and responsiveness
        self.timeout_seconds = 300
        self.enable_process_pools = True
        self.enable_thread_pools = True
    
    def get_optimal_workers(self, service_type: str) -> int:
        """Get optimal number of workers for a service type."""
        service_workers = {
            'llm_processing': self.llm_workers,
            'embedding_generation': self.embedding_workers,
            'document_processing': self.document_workers,
            'ocr_processing': self.ocr_workers,
            'default': min(4, self.max_workers // 2)
        }
        return service_workers.get(service_type, service_workers['default'])
    
    def get_chunk_size(self, service_type: str) -> int:
        """Get optimal chunk size for batch processing based on service type."""
        service_chunk_sizes = {
            'llm_processing': 10,      # Small batches for LLM API calls (I/O bound, rate limited)
            'embedding_generation': 25, # Medium batches for embedding generation (I/O bound)
            'document_processing': 5,   # Small batches for CPU-intensive document processing
            'ocr_processing': 8,        # Small batches for OCR (CPU + I/O intensive)
            'vector_search': 15,        # Medium batches for vector search (memory intensive)
            'chunking': 12,            # Medium batches for text chunking (CPU bound)
            'reranking': 8,            # Small batches for reranking (CPU intensive)
            'default': self.chunk_size
        }
        return service_chunk_sizes.get(service_type, service_chunk_sizes['default'])
    
    def get_timeout(self, service_type: str) -> int:
        """Get timeout for service operations."""
        return self.timeout_seconds
    
    def get_config_dict(self) -> dict:
        """Get configuration as dictionary for logging/monitoring."""
        return {
            'max_workers': self.max_workers,
            'thread_pool_size': self.thread_pool_size,
            'llm_workers': self.llm_workers,
            'embedding_workers': self.embedding_workers,
            'document_workers': self.document_workers,
            'ocr_workers': self.ocr_workers,
            'chunk_size': self.chunk_size,
            'timeout_seconds': self.timeout_seconds,
            'enable_process_pools': self.enable_process_pools,
            'enable_thread_pools': self.enable_thread_pools
        }