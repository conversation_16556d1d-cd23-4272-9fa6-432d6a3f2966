"""
Corrected Hackathon Specific Webhook Test Client
===============================================

This script tests the exact hackathon endpoint with the CORRECT specifications.

Target: http://**************:8000/api/v1/hackrx/run (FIXED URL)
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hackrx_test_corrected.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HackRXTester:
    """Test client specifically for HackRX endpoint with CORRECTED configuration."""
    
    def __init__(self):
        self.base_url = "http://**************:8000"
        self.endpoint = "/api/v1/hackrx/run"  # FIXED: Added /api/v1 prefix
        self.bearer_token = "0b45a8133a043a45183420ea211e6424fb2a1035869f623fc8e5108796574378"
        self.document_url = "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D"
        self.questions = [
            "What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?",
            "What is the waiting period for pre-existing diseases (PED) to be covered?",
            "Does this policy cover maternity expenses, and what are the conditions?",
            "What is the waiting period for cataract surgery?",
            "Are the medical expenses for an organ donor covered under this policy?",
            "What is the No Claim Discount (NCD) offered in this policy?",
            "Is there a benefit for preventive health check-ups?",
            "How does the policy define a 'Hospital'?",
            "What is the extent of coverage for AYUSH treatments?",
            "Are there any sub-limits on room rent and ICU charges for Plan A?"
        ]
        
    async def test_endpoint(self) -> Dict[str, Any]:
        """Test the HackRX endpoint with the CORRECT specifications."""
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.bearer_token}'
        }
        
        # Payload format matches QueryRequest model exactly
        payload = {
            "documents": self.document_url,
            "questions": self.questions
        }
        
        url = f"{self.base_url}{self.endpoint}"
        
        logger.info("🚀 Starting CORRECTED HackRX endpoint test...")
        logger.info(f"URL: {url}")
        logger.info(f"Questions: {len(self.questions)} questions")
        logger.info("✅ Using CORRECT endpoint path: /api/v1/hackrx/run")
        
        start_time = time.time()
        
        try:
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                logger.info("📡 Sending request...")
                
                async with session.post(url, json=payload, headers=headers) as response:
                    response_time = time.time() - start_time
                    
                    logger.info(f"📊 Response received: {response.status} ({response_time:.2f}s)")
                    
                    # Get response data
                    try:
                        response_data = await response.json()
                    except:
                        response_text = await response.text()
                        response_data = {"raw_response": response_text}
                    
                    # Create result
                    result = {
                        "success": response.status == 200,
                        "status_code": response.status,
                        "response_time": response_time,
                        "response_data": response_data,
                        "timestamp": datetime.now().isoformat(),
                        "url": url,
                        "payload": payload
                    }
                    
                    # Log detailed results
                    if result["success"]:
                        logger.info("✅ Test PASSED!")
                        
                        if isinstance(response_data, dict) and "answers" in response_data:
                            answers = response_data["answers"]
                            if isinstance(answers, list):
                                logger.info(f"📝 Received {len(answers)} answers")
                                
                                # Show first few answers
                                for i, answer in enumerate(answers[:3], 1):
                                    logger.info(f"Answer {i}: {answer[:100]}...")
                                
                                # Validate answer count
                                if len(answers) == len(self.questions):
                                    logger.info("✅ Answer count matches question count")
                                else:
                                    logger.warning(f"⚠️ Answer count mismatch: got {len(answers)}, expected {len(self.questions)}")
                            else:
                                logger.warning("⚠️ 'answers' field is not a list")
                        else:
                            logger.warning("⚠️ Response missing 'answers' field")
                    else:
                        logger.error(f"❌ Test FAILED: {response.status}")
                        logger.error(f"Response: {response_data}")
                    
                    return result
                    
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            logger.error(f"⏰ Request timed out after {response_time:.2f}s")
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "response_data": None,
                "error": "Request timeout",
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "payload": payload
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"💥 Request failed: {str(e)}")
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "response_data": None,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "payload": payload
            }
    
    async def test_with_subset_questions(self) -> Dict[str, Any]:
        """Test with a smaller subset of questions for faster testing."""
        
        logger.info("🔬 Testing with subset of questions...")
        
        # Use first 3 questions for quick test
        subset_questions = self.questions[:3]
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.bearer_token}'
        }
        
        payload = {
            "documents": self.document_url,
            "questions": subset_questions
        }
        
        url = f"{self.base_url}{self.endpoint}"
        start_time = time.time()
        
        try:
            timeout = aiohttp.ClientTimeout(total=120)  # 2 minutes for subset
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    response_time = time.time() - start_time
                    
                    try:
                        response_data = await response.json()
                    except:
                        response_text = await response.text()
                        response_data = {"raw_response": response_text}
                    
                    result = {
                        "test_type": "subset",
                        "success": response.status == 200,
                        "status_code": response.status,
                        "response_time": response_time,
                        "response_data": response_data,
                        "timestamp": datetime.now().isoformat(),
                        "questions_count": len(subset_questions)
                    }
                    
                    logger.info(f"📊 Subset test: {response.status} ({response_time:.2f}s)")
                    return result
                    
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"💥 Subset test failed: {str(e)}")
            return {
                "test_type": "subset",
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "questions_count": len(subset_questions)
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check if the server is accessible."""
        
        logger.info("🏥 Performing health check...")
        
        try:
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Try to access health endpoint
                health_url = f"{self.base_url}/api/v1/health"
                async with session.get(health_url) as response:
                    logger.info(f"📡 Health endpoint accessible: {response.status}")
                    return {
                        "server_accessible": True,
                        "status_code": response.status,
                        "response_time": 0,
                        "health_url": health_url
                    }
                    
        except Exception as e:
            logger.error(f"💥 Health check failed: {str(e)}")
            return {
                "server_accessible": False,
                "error": str(e)
            }

async def main():
    """Main function to run the CORRECTED HackRX test."""
    
    print("🧪 CORRECTED HackRX Endpoint Tester")
    print("=" * 60)
    print("Target: http://**************:8000/api/v1/hackrx/run")
    print("✅ FIXED: Using correct endpoint path with /api/v1 prefix")
    print("Questions: 10 policy-related questions")
    print("=" * 60)
    
    tester = HackRXTester()
    results = {}
    
    try:
        # 1. Health check
        logger.info("Step 1: Health check...")
        results["health_check"] = await tester.health_check()
        
        # 2. Quick subset test first
        logger.info("Step 2: Quick subset test...")
        results["subset_test"] = await tester.test_with_subset_questions()
        
        # 3. Full test if subset works
        if results["subset_test"]["success"]:
            logger.info("Step 3: Full test with all questions...")
            results["main_test"] = await tester.test_endpoint()
        else:
            logger.warning("Skipping full test due to subset test failure")
            results["main_test"] = {"success": False, "skipped": True}
        
        # Display results
        print("\n" + "="*60)
        print("CORRECTED TEST RESULTS")
        print("="*60)
        
        if results.get("health_check", {}).get("server_accessible"):
            print("✅ Health Check: PASSED")
        else:
            print("❌ Health Check: FAILED")
            
        if results.get("subset_test", {}).get("success"):
            print("✅ Subset Test: PASSED")
        else:
            print("❌ Subset Test: FAILED")
            
        if results.get("main_test", {}).get("success"):
            print("✅ Full Test: PASSED")
            print("\n🎉 SUCCESS! Your HackRX endpoint is working!")
        elif results.get("subset_test", {}).get("success"):
            print("⚠️ Full Test: SKIPPED (subset passed)")
            print("\n⚠️ PARTIAL SUCCESS! Subset test passed.")
        else:
            print("❌ Full Test: FAILED")
            print("\n❌ FAILURE! Endpoint needs attention.")
            
        print("="*60)
            
    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
    except Exception as e:
        logger.error(f"💥 Test failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
